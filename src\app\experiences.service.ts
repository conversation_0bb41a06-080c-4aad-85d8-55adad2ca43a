import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ExperiencesService {
  private apiUrl = 'https://portflio-backend-uiv7.onrender.com/api/experiences';

  constructor(private http: HttpClient) {}

  getExperiences(): Observable<any> {
    return this.http.get<any>(this.apiUrl);
  }
}
