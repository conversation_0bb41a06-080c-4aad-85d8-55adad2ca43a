<div class="pt-16 pb-12 bg-gradient-to-br from-pink-50 via-white to-rose-100" id="skills">
  <div class="text-center pb-10">
    <h2 class="text-4xl font-extrabold uppercase text-pink-600 tracking-wider relative inline-block">
      Skills
      <span class="block w-16 h-1 bg-pink-400 mx-auto mt-2 rounded-full"></span>
    </h2>
  </div>

  <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6 px-6 sm:px-12">
    <div
      *ngFor="let skill of skills"
      class="bg-white border border-pink-200 rounded-3xl p-6 shadow-md hover:shadow-pink-200 transition-all duration-300 hover:scale-105 flex flex-col items-center text-center"
    >
      <div class="w-20 h-20 flex items-center justify-center rounded-full bg-pink-100 border border-pink-300 shadow-inner mb-3">
        <img [src]="skill.icon" [alt]="skill.name" class="w-10 h-10 object-contain" />
      </div>
      <h3 class="text-pink-700 font-semibold text-sm tracking-wide">{{ skill.name }}</h3>
    </div>
  </div>
</div>
