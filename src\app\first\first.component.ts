import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';

declare var VANTA: any;

@Component({
  selector: 'app-first',
  templateUrl: './first.component.html',
  styleUrl: './first.component.css'
})
export class FirstComponent implements OnInit, OnDestroy {
  private vantaEffect: any;

  ngOnInit() {
    this.vantaEffect = VANTA.BIRDS({
      el: "#vanta-birds",
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      backgroundColor: 0xae179c,
      color1: 0xa314a4,
      color2: 0xeae612,
      birdSize: 1.20,
      wingSpan: 32.00,
      speedLimit: 9.00,
      separation: 58.00,
      alignment: 77.00,
      cohesion: 40.00,
      quantity: 4.00,
      backgroundAlpha: 0.35
    });
  }

  ngOnDestroy() {
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  }
}
