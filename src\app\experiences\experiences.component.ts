import { Component, OnInit } from '@angular/core';
import { ExperiencesService } from '../experiences.service';

@Component({
  selector: 'app-experiences',
  templateUrl: './experiences.component.html',
  styleUrls: ['./experiences.component.css']
})
export class ExperiencesComponent implements OnInit {
  experiences: any[] = [];
skills: any;

  constructor(private experiencesService: ExperiencesService) {}

  ngOnInit(): void {
    this.experiencesService.getExperiences().subscribe(
      (data: any[]) => {
        this.experiences = data.map(exp => ({
          title: exp.title,
          company: exp.company,
          from: exp.from,
          to: exp.to
        }));
      },
      error => {
        console.error('Error fetching experiences:', error);
      }
    );
  }
}
