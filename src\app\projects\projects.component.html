<div class="text-center mt-6">
  <h2 class="text-4xl font-righteous uppercase theme-text-primary mb-4 relative inline-block after:block after:w-32 after:h-1 after:bg-pink-400 after:rounded after:mx-auto after:mt-2">
    Projects
  </h2>
</div>

<div class="flex flex-wrap justify-center gap-6 p-4">
  <div *ngFor="let project of projects"
    class="backdrop-blur-xl bg-pink-400/80 border border-pink-200/40 rounded-xl shadow-xl w-80 p-6 flex flex-col items-center text-center">

    <div class="w-full h-72 rounded-xl overflow-hidden mb-4">
      <img [src]="project.photo" alt="Project Image" class="w-full h-full object-cover object-center" />
    </div>

    <h1 class="text-white text-2xl font-righteous uppercase mb-1">{{ project.name }}</h1>
    <p class="text-white text-sm font-lato tracking-wider uppercase">{{ project.title }}</p>
    <p class="text-white text-xs font-lato mt-1">{{ project.description }}</p>

    <div class="mt-4">
      <strong class="text-white">Skills:</strong>
      <span *ngFor="let skill of project.skills"
        class="inline-block bg-white text-pink-700 text-xs rounded px-2 py-1 mr-2 mt-2">{{ skill }}</span>
    </div>

    <div class="mt-5 flex gap-3">
      <button class="px-4 py-2 border border-pink-600 text-pink-600 rounded-full text-sm uppercase hover:scale-110 hover:border-pink-300 hover:text-pink-300 transition"
        (click)="openGitHub(project.githubLink)">GitHub</button>
      <button class="px-4 py-2 border border-pink-600 text-pink-600 rounded-full text-sm uppercase hover:scale-110 hover:border-pink-300 hover:text-pink-300 transition"
        (click)="openGitHub(project.link)">Live Demo</button>
    </div>
  </div>
</div>
