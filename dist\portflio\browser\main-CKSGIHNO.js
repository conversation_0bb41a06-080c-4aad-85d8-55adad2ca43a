var r0=Object.defineProperty,o0=Object.defineProperties;var i0=Object.getOwnPropertyDescriptors;var Gu=Object.getOwnPropertySymbols;var s0=Object.prototype.hasOwnProperty,a0=Object.prototype.propertyIsEnumerable;var Wu=(e,t,n)=>t in e?r0(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,C=(e,t)=>{for(var n in t||={})s0.call(t,n)&&Wu(e,n,t[n]);if(Gu)for(var n of Gu(t))a0.call(t,n)&&Wu(e,n,t[n]);return e},B=(e,t)=>o0(e,i0(t));var Uo=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(l){o(l)}},s=c=>{try{a(n.throw(c))}catch(l){o(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function qu(e,t){return Object.is(e,t)}var ie=null,Po=!1,ko=1,Ut=Symbol("SIGNAL");function $(e){let t=ie;return ie=e,t}function Zu(){return ie}var Fr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function va(e){if(Po)throw new Error("");if(ie===null)return;ie.consumerOnSignalRead(e);let t=ie.nextProducerIndex++;if(Bo(ie),t<ie.producerNode.length&&ie.producerNode[t]!==e&&Tr(ie)){let n=ie.producerNode[t];jo(n,ie.producerIndexOfThis[t])}ie.producerNode[t]!==e&&(ie.producerNode[t]=e,ie.producerIndexOfThis[t]=Tr(ie)?Qu(e,ie,t):0),ie.producerLastReadVersion[t]=e.version}function c0(){ko++}function Xu(e){if(!(Tr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===ko)){if(!e.producerMustRecompute(e)&&!Da(e)){e.dirty=!1,e.lastCleanEpoch=ko;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=ko}}function Ku(e){if(e.liveConsumerNode===void 0)return;let t=Po;Po=!0;try{for(let n of e.liveConsumerNode)n.dirty||l0(n)}finally{Po=t}}function Yu(){return ie?.consumerAllowSignalWrites!==!1}function l0(e){e.dirty=!0,Ku(e),e.consumerMarkedDirty?.(e)}function Vo(e){return e&&(e.nextProducerIndex=0),$(e)}function Ca(e,t){if($(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Tr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)jo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Da(e){Bo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Xu(n),r!==n.version))return!0}return!1}function wa(e){if(Bo(e),Tr(e))for(let t=0;t<e.producerNode.length;t++)jo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Qu(e,t,n){if(Ju(e),e.liveConsumerNode.length===0&&ed(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=Qu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function jo(e,t){if(Ju(e),e.liveConsumerNode.length===1&&ed(e))for(let r=0;r<e.producerNode.length;r++)jo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Bo(o),o.producerIndexOfThis[r]=t}}function Tr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Bo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Ju(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function ed(e){return e.producerNode!==void 0}function td(e){let t=Object.create(u0);t.computation=e;let n=()=>{if(Xu(t),va(t),t.value===Lo)throw t.error;return t.value};return n[Ut]=t,n}var ma=Symbol("UNSET"),ya=Symbol("COMPUTING"),Lo=Symbol("ERRORED"),u0=B(C({},Fr),{value:ma,dirty:!0,error:null,equal:qu,producerMustRecompute(e){return e.value===ma||e.value===ya},producerRecomputeValue(e){if(e.value===ya)throw new Error("Detected cycle in computations.");let t=e.value;e.value=ya;let n=Vo(e),r;try{r=e.computation()}catch(o){r=Lo,e.error=o}finally{Ca(e,n)}if(t!==ma&&t!==Lo&&r!==Lo&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function d0(){throw new Error}var nd=d0;function rd(){nd()}function od(e){nd=e}var f0=null;function id(e){let t=Object.create(ad);t.value=e;let n=()=>(va(t),t.value);return n[Ut]=t,n}function Ea(e,t){Yu()||rd(),e.equal(e.value,t)||(e.value=t,h0(e))}function sd(e,t){Yu()||rd(),Ea(e,t(e.value))}var ad=B(C({},Fr),{equal:qu,value:void 0});function h0(e){e.version++,c0(),Ku(e),f0?.()}function x(e){return typeof e=="function"}function Mn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var $o=Mn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function Nr(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var ee=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(x(r))try{r()}catch(i){t=i instanceof $o?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{cd(i)}catch(s){t=t??[],s instanceof $o?t=[...t,...s.errors]:t.push(s)}}if(t)throw new $o(t)}}add(t){var n;if(t&&t!==this)if(this.closed)cd(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Nr(n,t)}remove(t){let{_finalizers:n}=this;n&&Nr(n,t),t instanceof e&&t._removeParent(this)}};ee.EMPTY=(()=>{let e=new ee;return e.closed=!0,e})();var ba=ee.EMPTY;function Ho(e){return e instanceof ee||e&&"closed"in e&&x(e.remove)&&x(e.add)&&x(e.unsubscribe)}function cd(e){x(e)?e():e.unsubscribe()}var Ke={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Sn={setTimeout(e,t,...n){let{delegate:r}=Sn;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Sn;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function zo(e){Sn.setTimeout(()=>{let{onUnhandledError:t}=Ke;if(t)t(e);else throw e})}function Or(){}var ld=Ia("C",void 0,void 0);function ud(e){return Ia("E",void 0,e)}function dd(e){return Ia("N",e,void 0)}function Ia(e,t,n){return{kind:e,value:t,error:n}}var rn=null;function An(e){if(Ke.useDeprecatedSynchronousErrorHandling){let t=!rn;if(t&&(rn={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=rn;if(rn=null,n)throw r}}else e()}function fd(e){Ke.useDeprecatedSynchronousErrorHandling&&rn&&(rn.errorThrown=!0,rn.error=e)}var on=class extends ee{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Ho(t)&&t.add(this)):this.destination=m0}static create(t,n,r){return new xn(t,n,r)}next(t){this.isStopped?Ma(dd(t),this):this._next(t)}error(t){this.isStopped?Ma(ud(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Ma(ld,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},p0=Function.prototype.bind;function _a(e,t){return p0.call(e,t)}var Sa=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Go(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Go(r)}else Go(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Go(n)}}},xn=class extends on{constructor(t,n,r){super();let o;if(x(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Ke.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&_a(t.next,i),error:t.error&&_a(t.error,i),complete:t.complete&&_a(t.complete,i)}):o=t}this.destination=new Sa(o)}};function Go(e){Ke.useDeprecatedSynchronousErrorHandling?fd(e):zo(e)}function g0(e){throw e}function Ma(e,t){let{onStoppedNotification:n}=Ke;n&&Sn.setTimeout(()=>n(e,t))}var m0={closed:!0,next:Or,error:g0,complete:Or};var Tn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Oe(e){return e}function Aa(...e){return xa(e)}function xa(e){return e.length===0?Oe:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var L=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=v0(n)?n:new xn(n,r,o);return An(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=hd(r),new r((o,i)=>{let s=new xn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Tn](){return this}pipe(...n){return xa(n)(this)}toPromise(n){return n=hd(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function hd(e){var t;return(t=e??Ke.Promise)!==null&&t!==void 0?t:Promise}function y0(e){return e&&x(e.next)&&x(e.error)&&x(e.complete)}function v0(e){return e&&e instanceof on||y0(e)&&Ho(e)}function Ta(e){return x(e?.lift)}function V(e){return t=>{if(Ta(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function k(e,t,n,r,o){return new Fa(e,t,n,r,o)}var Fa=class extends on{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Fn(){return V((e,t)=>{let n=null;e._refCount++;let r=k(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Nn=class extends L{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Ta(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new ee;let n=this.getSubject();t.add(this.source.subscribe(k(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=ee.EMPTY)}return t}refCount(){return Fn()(this)}};var pd=Mn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ce=(()=>{class e extends L{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Wo(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new pd}next(n){An(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){An(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){An(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?ba:(this.currentObservers=null,i.push(n),new ee(()=>{this.currentObservers=null,Nr(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new L;return n.source=this,n}}return e.create=(t,n)=>new Wo(t,n),e})(),Wo=class extends ce{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:ba}};var fe=class extends ce{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Re=new L(e=>e.complete());function gd(e){return e&&x(e.schedule)}function md(e){return e[e.length-1]}function qo(e){return x(md(e))?e.pop():void 0}function Pt(e){return gd(md(e))?e.pop():void 0}function vd(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function yd(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function sn(e){return this instanceof sn?(this.v=e,this):new sn(e)}function Cd(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o={},a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(p){return function(v){return Promise.resolve(v).then(p,d)}}function a(p,v){r[p]&&(o[p]=function(D){return new Promise(function(_,F){i.push([p,D,_,F])>1||c(p,D)})},v&&(o[p]=v(o[p])))}function c(p,v){try{l(r[p](v))}catch(D){m(i[0][3],D)}}function l(p){p.value instanceof sn?Promise.resolve(p.value.v).then(u,d):m(i[0][2],p)}function u(p){c("next",p)}function d(p){c("throw",p)}function m(p,v){p(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Dd(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof yd=="function"?yd(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var Zo=e=>e&&typeof e.length=="number"&&typeof e!="function";function Xo(e){return x(e?.then)}function Ko(e){return x(e[Tn])}function Yo(e){return Symbol.asyncIterator&&x(e?.[Symbol.asyncIterator])}function Qo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function C0(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Jo=C0();function ei(e){return x(e?.[Jo])}function ti(e){return Cd(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield sn(n.read());if(o)return yield sn(void 0);yield yield sn(r)}}finally{n.releaseLock()}})}function ni(e){return x(e?.getReader)}function te(e){if(e instanceof L)return e;if(e!=null){if(Ko(e))return D0(e);if(Zo(e))return w0(e);if(Xo(e))return E0(e);if(Yo(e))return wd(e);if(ei(e))return b0(e);if(ni(e))return I0(e)}throw Qo(e)}function D0(e){return new L(t=>{let n=e[Tn]();if(x(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function w0(e){return new L(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function E0(e){return new L(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,zo)})}function b0(e){return new L(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function wd(e){return new L(t=>{_0(e,t).catch(n=>t.error(n))})}function I0(e){return wd(ti(e))}function _0(e,t){var n,r,o,i;return vd(this,void 0,void 0,function*(){try{for(n=Dd(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function Se(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function ri(e,t=0){return V((n,r)=>{n.subscribe(k(r,o=>Se(r,e,()=>r.next(o),t),()=>Se(r,e,()=>r.complete(),t),o=>Se(r,e,()=>r.error(o),t)))})}function oi(e,t=0){return V((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Ed(e,t){return te(e).pipe(oi(t),ri(t))}function bd(e,t){return te(e).pipe(oi(t),ri(t))}function Id(e,t){return new L(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function _d(e,t){return new L(n=>{let r;return Se(n,t,()=>{r=e[Jo](),Se(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>x(r?.return)&&r.return()})}function ii(e,t){if(!e)throw new Error("Iterable cannot be null");return new L(n=>{Se(n,t,()=>{let r=e[Symbol.asyncIterator]();Se(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Md(e,t){return ii(ti(e),t)}function Sd(e,t){if(e!=null){if(Ko(e))return Ed(e,t);if(Zo(e))return Id(e,t);if(Xo(e))return bd(e,t);if(Yo(e))return ii(e,t);if(ei(e))return _d(e,t);if(ni(e))return Md(e,t)}throw Qo(e)}function Q(e,t){return t?Sd(e,t):te(e)}function M(...e){let t=Pt(e);return Q(e,t)}function On(e,t){let n=x(e)?e:()=>e,r=o=>o.error(n());return new L(t?o=>t.schedule(r,0,o):r)}function Na(e){return!!e&&(e instanceof L||x(e.lift)&&x(e.subscribe))}var yt=Mn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function T(e,t){return V((n,r)=>{let o=0;n.subscribe(k(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:M0}=Array;function S0(e,t){return M0(t)?e(...t):e(t)}function si(e){return T(t=>S0(e,t))}var{isArray:A0}=Array,{getPrototypeOf:x0,prototype:T0,keys:F0}=Object;function ai(e){if(e.length===1){let t=e[0];if(A0(t))return{args:t,keys:null};if(N0(t)){let n=F0(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function N0(e){return e&&typeof e=="object"&&x0(e)===T0}function ci(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Rr(...e){let t=Pt(e),n=qo(e),{args:r,keys:o}=ai(e);if(r.length===0)return Q([],t);let i=new L(O0(r,t,o?s=>ci(o,s):Oe));return n?i.pipe(si(n)):i}function O0(e,t,n=Oe){return r=>{Ad(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Ad(t,()=>{let l=Q(e[c],t),u=!1;l.subscribe(k(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Ad(e,t,n){e?Se(n,e,t):t()}function xd(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,m=()=>{d&&!c.length&&!l&&t.complete()},p=D=>l<r?v(D):c.push(D),v=D=>{i&&t.next(D),l++;let _=!1;te(n(D,u++)).subscribe(k(t,F=>{o?.(F),i?p(F):t.next(F)},()=>{_=!0},void 0,()=>{if(_)try{for(l--;c.length&&l<r;){let F=c.shift();s?Se(t,s,()=>v(F)):v(F)}m()}catch(F){t.error(F)}}))};return e.subscribe(k(t,p,()=>{d=!0,m()})),()=>{a?.()}}function ne(e,t,n=1/0){return x(t)?ne((r,o)=>T((i,s)=>t(r,i,o,s))(te(e(r,o))),n):(typeof t=="number"&&(n=t),V((r,o)=>xd(r,o,e,n)))}function Rn(e=1/0){return ne(Oe,e)}function Td(){return Rn(1)}function Un(...e){return Td()(Q(e,Pt(e)))}function li(e){return new L(t=>{te(e()).subscribe(t)})}function Oa(...e){let t=qo(e),{args:n,keys:r}=ai(e),o=new L(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;te(n[u]).subscribe(k(i,m=>{d||(d=!0,l--),a[u]=m},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?ci(r,a):a),i.complete())}))}});return t?o.pipe(si(t)):o}function Ae(e,t){return V((n,r)=>{let o=0;n.subscribe(k(r,i=>e.call(t,i,o++)&&r.next(i)))})}function kt(e){return V((t,n)=>{let r=null,o=!1,i;r=t.subscribe(k(n,void 0,void 0,s=>{i=te(e(s,kt(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Fd(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(k(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function vt(e,t){return x(t)?ne(e,t,1):ne(e,1)}function Lt(e){return V((t,n)=>{let r=!1;t.subscribe(k(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Ct(e){return e<=0?()=>Re:V((t,n)=>{let r=0;t.subscribe(k(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function Ra(e){return T(()=>e)}function ui(e=R0){return V((t,n)=>{let r=!1;t.subscribe(k(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function R0(){return new yt}function Vt(e){return V((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function ot(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ae((o,i)=>e(o,i,r)):Oe,Ct(1),n?Lt(t):ui(()=>new yt))}function Pn(e){return e<=0?()=>Re:V((t,n)=>{let r=[];t.subscribe(k(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Ua(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ae((o,i)=>e(o,i,r)):Oe,Pn(1),n?Lt(t):ui(()=>new yt))}function Pa(e,t){return V(Fd(e,t,arguments.length>=2,!0))}function ka(...e){let t=Pt(e);return V((n,r)=>{(t?Un(e,n,t):Un(e,n)).subscribe(r)})}function xe(e,t){return V((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(k(r,c=>{o?.unsubscribe();let l=0,u=i++;te(e(c,u)).subscribe(o=k(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function La(e){return V((t,n)=>{te(e).subscribe(k(n,()=>n.complete(),Or)),!n.closed&&t.subscribe(n)})}function le(e,t,n){let r=x(e)||t||n?{next:e,error:t,complete:n}:e;return r?V((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(k(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):Oe}var Cf="https://g.co/ng/security#xss",I=class extends Error{constructor(t,n){super(qi(t,n)),this.code=t}};function qi(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Gr(e){return{toString:e}.toString()}var di="__parameters__";function U0(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Df(e,t,n){return Gr(()=>{let r=U0(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(di)?c[di]:Object.defineProperty(c,di,{value:[]})[di];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var De=globalThis;function Z(e){for(let t in e)if(e[t]===Z)return t;throw Error("Could not find renamed property on target object.")}function P0(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ee(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(Ee).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Nd(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var k0=Z({__forward_ref__:Z});function Zi(e){return e.__forward_ref__=Zi,e.toString=function(){return Ee(this())},e}function we(e){return wf(e)?e():e}function wf(e){return typeof e=="function"&&e.hasOwnProperty(k0)&&e.__forward_ref__===Zi}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function pe(e){return{providers:e.providers||[],imports:e.imports||[]}}function Xi(e){return Od(e,bf)||Od(e,If)}function Ef(e){return Xi(e)!==null}function Od(e,t){return e.hasOwnProperty(t)?e[t]:null}function L0(e){let t=e&&(e[bf]||e[If]);return t||null}function Rd(e){return e&&(e.hasOwnProperty(Ud)||e.hasOwnProperty(V0))?e[Ud]:null}var bf=Z({\u0275prov:Z}),Ud=Z({\u0275inj:Z}),If=Z({ngInjectableDef:Z}),V0=Z({ngInjectorDef:Z}),b=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=w({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function _f(e){return e&&!!e.\u0275providers}var j0=Z({\u0275cmp:Z}),B0=Z({\u0275dir:Z}),$0=Z({\u0275pipe:Z}),H0=Z({\u0275mod:Z}),Di=Z({\u0275fac:Z}),Pr=Z({__NG_ELEMENT_ID__:Z}),Pd=Z({__NG_ENV_ID__:Z});function $n(e){return typeof e=="string"?e:e==null?"":String(e)}function z0(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():$n(e)}function G0(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new I(-200,e)}function Vc(e,t){throw new I(-201,!1)}var R=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(R||{}),Ja;function Mf(){return Ja}function Te(e){let t=Ja;return Ja=e,t}function Sf(e,t,n){let r=Xi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&R.Optional)return null;if(t!==void 0)return t;Vc(e,"Injector")}var W0={},Lr=W0,ec="__NG_DI_FLAG__",wi="ngTempTokenPath",q0="ngTokenPath",Z0=/\n/gm,X0="\u0275",kd="__source",jn;function K0(){return jn}function jt(e){let t=jn;return jn=e,t}function Y0(e,t=R.Default){if(jn===void 0)throw new I(-203,!1);return jn===null?Sf(e,void 0,t):jn.get(e,t&R.Optional?null:void 0,t)}function E(e,t=R.Default){return(Mf()||Y0)(we(e),t)}function y(e,t=R.Default){return E(e,Ki(t))}function Ki(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function tc(e){let t=[];for(let n=0;n<e.length;n++){let r=we(e[n]);if(Array.isArray(r)){if(r.length===0)throw new I(900,!1);let o,i=R.Default;for(let s=0;s<r.length;s++){let a=r[s],c=Q0(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(E(o,i))}else t.push(E(r))}return t}function Af(e,t){return e[ec]=t,e.prototype[ec]=t,e}function Q0(e){return e[ec]}function J0(e,t,n,r){let o=e[wi];throw t[kd]&&o.unshift(t[kd]),e.message=e1(`
`+e.message,o,n,r),e[q0]=o,e[wi]=null,e}function e1(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==X0?e.slice(2):e;let o=Ee(t);if(Array.isArray(t))o=t.map(Ee).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):Ee(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Z0,`
  `)}`}var Yi=Af(Df("Optional"),8);var jc=Af(Df("SkipSelf"),4);function ln(e,t){let n=e.hasOwnProperty(Di);return n?e[Di]:null}function Bc(e,t){e.forEach(n=>Array.isArray(n)?Bc(n,t):t(n))}function xf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ei(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function t1(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function n1(e,t,n){let r=Wr(e,t);return r>=0?e[r|1]=n:(r=~r,t1(e,r,t,n)),r}function Va(e,t){let n=Wr(e,t);if(n>=0)return e[n|1]}function Wr(e,t){return r1(e,t,1)}function r1(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Hn={},Be=[],zn=new b(""),Tf=new b("",-1),Ff=new b(""),bi=class{get(t,n=Lr){if(n===Lr){let r=new Error(`NullInjectorError: No provider for ${Ee(t)}!`);throw r.name="NullInjectorError",r}return n}},Nf=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Nf||{}),at=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(at||{}),Ht=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ht||{});function o1(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function nc(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];i1(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Of(e){return e===3||e===4||e===6}function i1(e){return e.charCodeAt(0)===64}function Vr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Ld(e,n,o,null,t[++r]):Ld(e,n,o,null,null))}}return e}function Ld(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Rf="ng-template";function s1(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&o1(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if($c(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function $c(e){return e.type===4&&e.value!==Rf}function a1(e,t,n){let r=e.type===4&&!n?Rf:e.value;return t===r}function c1(e,t,n){let r=4,o=e.attrs,i=o!==null?d1(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!Ye(r)&&!Ye(c))return!1;if(s&&Ye(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!a1(e,c,n)||c===""&&t.length===1){if(Ye(r))return!1;s=!0}}else if(r&8){if(o===null||!s1(e,o,c,n)){if(Ye(r))return!1;s=!0}}else{let l=t[++a],u=l1(c,o,$c(e),n);if(u===-1){if(Ye(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(Ye(r))return!1;s=!0}}}}return Ye(r)||s}function Ye(e){return(e&1)===0}function l1(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return f1(t,e)}function u1(e,t,n=!1){for(let r=0;r<t.length;r++)if(c1(e,t[r],n))return!0;return!1}function d1(e){for(let t=0;t<e.length;t++){let n=e[t];if(Of(n))return t}return e.length}function f1(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function Vd(e,t){return e?":not("+t.trim()+")":t}function h1(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ye(s)&&(t+=Vd(i,o),o=""),r=s,i=i||!Ye(r);n++}return o!==""&&(t+=Vd(i,o)),t}function p1(e){return e.map(h1).join(",")}function g1(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ye(o))break;o=i}r++}return{attrs:t,classes:n}}function re(e){return Gr(()=>{let t=jf(e),n=B(C({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Nf.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||at.Emulated,styles:e.styles||Be,_:null,schemas:e.schemas||null,tView:null,id:""});Bf(n);let r=e.dependencies;return n.directiveDefs=Bd(r,!1),n.pipeDefs=Bd(r,!0),n.id=v1(n),n})}function m1(e){return zt(e)||Pf(e)}function y1(e){return e!==null}function ge(e){return Gr(()=>({type:e.type,bootstrap:e.bootstrap||Be,declarations:e.declarations||Be,imports:e.imports||Be,exports:e.exports||Be,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function jd(e,t){if(e==null)return Hn;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ht.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ht.None?[r,a]:r,t[i]=s):n[i]=r}return n}function et(e){return Gr(()=>{let t=jf(e);return Bf(t),t})}function Uf(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function zt(e){return e[j0]||null}function Pf(e){return e[B0]||null}function kf(e){return e[$0]||null}function Lf(e){let t=zt(e)||Pf(e)||kf(e);return t!==null?t.standalone:!1}function Vf(e,t){let n=e[H0]||null;if(!n&&t===!0)throw new Error(`Type ${Ee(e)} does not have '\u0275mod' property.`);return n}function jf(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Hn,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||Be,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:jd(e.inputs,t),outputs:jd(e.outputs),debugInfo:null}}function Bf(e){e.features?.forEach(t=>t(e))}function Bd(e,t){if(!e)return null;let n=t?kf:m1;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(y1)}function v1(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Qi(e){return{\u0275providers:e}}function C1(...e){return{\u0275providers:$f(!0,e),\u0275fromNgModule:!0}}function $f(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return Bc(t,s=>{let a=s;rc(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Hf(o,i),n}function Hf(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Hc(o,i=>{t(i,r)})}}function rc(e,t,n,r){if(e=we(e),!e)return!1;let o=null,i=Rd(e),s=!i&&zt(e);if(!i&&!s){let c=e.ngModule;if(i=Rd(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)rc(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{Bc(i.imports,u=>{rc(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&Hf(l,t)}if(!a){let l=ln(o)||(()=>new o);t({provide:o,useFactory:l,deps:Be},o),t({provide:Ff,useValue:o,multi:!0},o),t({provide:zn,useValue:()=>E(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;Hc(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function Hc(e,t){for(let n of e)_f(n)&&(n=n.\u0275providers),Array.isArray(n)?Hc(n,t):t(n)}var D1=Z({provide:String,useValue:Z});function zf(e){return e!==null&&typeof e=="object"&&D1 in e}function w1(e){return!!(e&&e.useExisting)}function E1(e){return!!(e&&e.useFactory)}function Gn(e){return typeof e=="function"}function b1(e){return!!e.useClass}var Ji=new b(""),mi={},I1={},ja;function zc(){return ja===void 0&&(ja=new bi),ja}var be=class{},jr=class extends be{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,ic(t,s=>this.processProvider(s)),this.records.set(Tf,kn(void 0,this)),o.has("environment")&&this.records.set(be,kn(void 0,this));let i=this.records.get(Ji);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Ff,Be,R.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=$(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),$(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=jt(this),r=Te(void 0),o;try{return t()}finally{jt(n),Te(r)}}get(t,n=Lr,r=R.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Pd))return t[Pd](this);r=Ki(r);let o,i=jt(this),s=Te(void 0);try{if(!(r&R.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=x1(t)&&Xi(t);l&&this.injectableDefInScope(l)?c=kn(oc(t),mi):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&R.Self?zc():this.parent;return n=r&R.Optional&&n===Lr?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[wi]=a[wi]||[]).unshift(Ee(t)),i)throw a;return J0(a,t,"R3InjectorError",this.source)}else throw a}finally{Te(s),jt(i)}}resolveInjectorInitializers(){let t=$(null),n=jt(this),r=Te(void 0),o;try{let i=this.get(zn,Be,R.Self);for(let s of i)s()}finally{jt(n),Te(r),$(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(Ee(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new I(205,!1)}processProvider(t){t=we(t);let n=Gn(t)?t:we(t&&t.provide),r=M1(t);if(!Gn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=kn(void 0,mi,!0),o.factory=()=>tc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=$(null);try{return n.value===mi&&(n.value=I1,n.value=n.factory()),typeof n.value=="object"&&n.value&&A1(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{$(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=we(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function oc(e){let t=Xi(e),n=t!==null?t.factory:ln(e);if(n!==null)return n;if(e instanceof b)throw new I(204,!1);if(e instanceof Function)return _1(e);throw new I(204,!1)}function _1(e){if(e.length>0)throw new I(204,!1);let n=L0(e);return n!==null?()=>n.factory(e):()=>new e}function M1(e){if(zf(e))return kn(void 0,e.useValue);{let t=Gf(e);return kn(t,mi)}}function Gf(e,t,n){let r;if(Gn(e)){let o=we(e);return ln(o)||oc(o)}else if(zf(e))r=()=>we(e.useValue);else if(E1(e))r=()=>e.useFactory(...tc(e.deps||[]));else if(w1(e))r=()=>E(we(e.useExisting));else{let o=we(e&&(e.useClass||e.provide));if(S1(e))r=()=>new o(...tc(e.deps));else return ln(o)||oc(o)}return r}function kn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function S1(e){return!!e.deps}function A1(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function x1(e){return typeof e=="function"||typeof e=="object"&&e instanceof b}function ic(e,t){for(let n of e)Array.isArray(n)?ic(n,t):n&&_f(n)?ic(n.\u0275providers,t):t(n)}function Pe(e,t){e instanceof jr&&e.assertNotDestroyed();let n,r=jt(e),o=Te(void 0);try{return t()}finally{jt(r),Te(o)}}function Wf(){return Mf()!==void 0||K0()!=null}function T1(e){if(!Wf())throw new I(-203,!1)}function F1(e){let t=De.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function N1(e){return typeof e=="function"}var Et=0,P=1,A=2,Ie=3,Qe=4,tt=5,Ii=6,_i=7,Je=8,Wn=9,ct=10,he=11,Br=12,$d=13,nr=14,lt=15,qn=16,Ln=17,Zn=18,es=19,qf=20,Bt=21,Ba=22,$e=23,ut=25,Zf=1;var un=7,Mi=8,Si=9,He=10,Ai=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Ai||{});function $t(e){return Array.isArray(e)&&typeof e[Zf]=="object"}function bt(e){return Array.isArray(e)&&e[Zf]===!0}function Xf(e){return(e.flags&4)!==0}function ts(e){return e.componentOffset>-1}function Gc(e){return(e.flags&1)===1}function Gt(e){return!!e.template}function sc(e){return(e[A]&512)!==0}var ac=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Kf(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function rr(){return Yf}function Yf(e){return e.type.prototype.ngOnChanges&&(e.setInput=R1),O1}rr.ngInherit=!0;function O1(){let e=Jf(this),t=e?.current;if(t){let n=e.previous;if(n===Hn)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function R1(e,t,n,r,o){let i=this.declaredInputs[r],s=Jf(e)||U1(e,{previous:Hn,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new ac(l&&l.currentValue,n,c===Hn),Kf(e,t,o,n)}var Qf="__ngSimpleChanges__";function Jf(e){return e[Qf]||null}function U1(e,t){return e[Qf]=t}var Hd=null;var it=function(e,t,n){Hd?.(e,t,n)},eh="svg",P1="math";function dt(e){for(;Array.isArray(e);)e=e[Et];return e}function th(e,t){return dt(t[e])}function ze(e,t){return dt(t[e.index])}function nh(e,t){return e.data[t]}function k1(e,t){return e[t]}function qt(e,t){let n=t[e];return $t(n)?n:n[Et]}function Wc(e){return(e[A]&128)===128}function L1(e){return bt(e[Ie])}function xi(e,t){return t==null?null:e[t]}function rh(e){e[Ln]=0}function oh(e){e[A]&1024||(e[A]|=1024,Wc(e)&&ns(e))}function V1(e,t){for(;e>0;)t=t[nr],e--;return t}function $r(e){return!!(e[A]&9216||e[$e]?.dirty)}function cc(e){e[ct].changeDetectionScheduler?.notify(7),e[A]&64&&(e[A]|=1024),$r(e)&&ns(e)}function ns(e){e[ct].changeDetectionScheduler?.notify(0);let t=dn(e);for(;t!==null&&!(t[A]&8192||(t[A]|=8192,!Wc(t)));)t=dn(t)}function ih(e,t){if((e[A]&256)===256)throw new I(911,!1);e[Bt]===null&&(e[Bt]=[]),e[Bt].push(t)}function j1(e,t){if(e[Bt]===null)return;let n=e[Bt].indexOf(t);n!==-1&&e[Bt].splice(n,1)}function dn(e){let t=e[Ie];return bt(t)?t[Ie]:t}var O={lFrame:mh(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var sh=!1;function B1(){return O.lFrame.elementDepthCount}function $1(){O.lFrame.elementDepthCount++}function H1(){O.lFrame.elementDepthCount--}function ah(){return O.bindingsEnabled}function z1(){return O.skipHydrationRootTNode!==null}function G1(e){return O.skipHydrationRootTNode===e}function W1(){O.skipHydrationRootTNode=null}function X(){return O.lFrame.lView}function Ge(){return O.lFrame.tView}function qc(e){return O.lFrame.contextLView=e,e[Je]}function Zc(e){return O.lFrame.contextLView=null,e}function Ne(){let e=ch();for(;e!==null&&e.type===64;)e=e.parent;return e}function ch(){return O.lFrame.currentTNode}function q1(){let e=O.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function qr(e,t){let n=O.lFrame;n.currentTNode=e,n.isParent=t}function lh(){return O.lFrame.isParent}function Z1(){O.lFrame.isParent=!1}function uh(){return sh}function zd(e){sh=e}function dh(){let e=O.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function X1(){return O.lFrame.bindingIndex}function K1(e){return O.lFrame.bindingIndex=e}function Xc(){return O.lFrame.bindingIndex++}function fh(e){let t=O.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Y1(){return O.lFrame.inI18n}function Q1(e,t){let n=O.lFrame;n.bindingIndex=n.bindingRootIndex=e,lc(t)}function J1(){return O.lFrame.currentDirectiveIndex}function lc(e){O.lFrame.currentDirectiveIndex=e}function e2(e){let t=O.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function hh(e){O.lFrame.currentQueryIndex=e}function t2(e){let t=e[P];return t.type===2?t.declTNode:t.type===1?e[tt]:null}function ph(e,t,n){if(n&R.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&R.Host);)if(o=t2(i),o===null||(i=i[nr],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=O.lFrame=gh();return r.currentTNode=t,r.lView=e,!0}function Kc(e){let t=gh(),n=e[P];O.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function gh(){let e=O.lFrame,t=e===null?null:e.child;return t===null?mh(e):t}function mh(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function yh(){let e=O.lFrame;return O.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var vh=yh;function Yc(){let e=yh();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function n2(e){return(O.lFrame.contextLView=V1(e,O.lFrame.contextLView))[Je]}function yn(){return O.lFrame.selectedIndex}function fn(e){O.lFrame.selectedIndex=e}function Ch(){let e=O.lFrame;return nh(e.tView,e.selectedIndex)}function or(){O.lFrame.currentNamespace=eh}function ir(){r2()}function r2(){O.lFrame.currentNamespace=null}function o2(){return O.lFrame.currentNamespace}var Dh=!0;function Qc(){return Dh}function Jc(e){Dh=e}function i2(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Yf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function el(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function yi(e,t,n){wh(e,t,3,n)}function vi(e,t,n,r){(e[A]&3)===n&&wh(e,t,n,r)}function $a(e,t){let n=e[A];(n&3)===t&&(n&=16383,n+=1,e[A]=n)}function wh(e,t,n,r){let o=r!==void 0?e[Ln]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Ln]+=65536),(a<i||i==-1)&&(s2(e,n,t,c),e[Ln]=(e[Ln]&**********)+c+2),c++}function Gd(e,t){it(4,e,t);let n=$(null);try{t.call(e)}finally{$(n),it(5,e,t)}}function s2(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[A]>>14<e[Ln]>>16&&(e[A]&3)===t&&(e[A]+=16384,Gd(a,i)):Gd(a,i)}var Bn=-1,hn=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function a2(e){return e instanceof hn}function c2(e){return(e.flags&8)!==0}function l2(e){return(e.flags&16)!==0}var Ha={},uc=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Ki(r);let o=this.injector.get(t,Ha,r);return o!==Ha||n===Ha?o:this.parentInjector.get(t,n,r)}};function Eh(e){return e!==Bn}function Ti(e){return e&32767}function u2(e){return e>>16}function Fi(e,t){let n=u2(e),r=t;for(;n>0;)r=r[nr],n--;return r}var dc=!0;function Ni(e){let t=dc;return dc=e,t}var d2=256,bh=d2-1,Ih=5,f2=0,st={};function h2(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Pr)&&(r=n[Pr]),r==null&&(r=n[Pr]=f2++);let o=r&bh,i=1<<o;t.data[e+(o>>Ih)]|=i}function Oi(e,t){let n=_h(e,t);if(n!==-1)return n;let r=t[P];r.firstCreatePass&&(e.injectorIndex=t.length,za(r.data,e),za(t,null),za(r.blueprint,null));let o=tl(e,t),i=e.injectorIndex;if(Eh(o)){let s=Ti(o),a=Fi(o,t),c=a[P].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function za(e,t){e.push(0,0,0,0,0,0,0,0,t)}function _h(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function tl(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Th(o),r===null)return Bn;if(n++,o=o[nr],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Bn}function fc(e,t,n){h2(e,t,n)}function p2(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Of(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function Mh(e,t,n){if(n&R.Optional||e!==void 0)return e;Vc(t,"NodeInjector")}function Sh(e,t,n,r){if(n&R.Optional&&r===void 0&&(r=null),!(n&(R.Self|R.Host))){let o=e[Wn],i=Te(void 0);try{return o?o.get(t,r,n&R.Optional):Sf(t,r,n&R.Optional)}finally{Te(i)}}return Mh(r,t,n)}function Ah(e,t,n,r=R.Default,o){if(e!==null){if(t[A]&2048&&!(r&R.Self)){let s=C2(e,t,n,r,st);if(s!==st)return s}let i=xh(e,t,n,r,st);if(i!==st)return i}return Sh(t,n,r,o)}function xh(e,t,n,r,o){let i=y2(n);if(typeof i=="function"){if(!ph(t,e,r))return r&R.Host?Mh(o,n,r):Sh(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&R.Optional))Vc(n);else return s}finally{vh()}}else if(typeof i=="number"){let s=null,a=_h(e,t),c=Bn,l=r&R.Host?t[lt][tt]:null;for((a===-1||r&R.SkipSelf)&&(c=a===-1?tl(e,t):t[a+8],c===Bn||!qd(r,!1)?a=-1:(s=t[P],a=Ti(c),t=Fi(c,t)));a!==-1;){let u=t[P];if(Wd(i,a,u.data)){let d=g2(a,t,n,s,r,l);if(d!==st)return d}c=t[a+8],c!==Bn&&qd(r,t[P].data[a+8]===l)&&Wd(i,a,t)?(s=u,a=Ti(c),t=Fi(c,t)):a=-1}}return o}function g2(e,t,n,r,o,i){let s=t[P],a=s.data[e+8],c=r==null?ts(a)&&dc:r!=s&&(a.type&3)!==0,l=o&R.Host&&i===a,u=m2(a,s,n,c,l);return u!==null?Xn(t,s,u,a):st}function m2(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,m=o?a+u:l;for(let p=d;p<m;p++){let v=s[p];if(p<c&&n===v||p>=c&&v.type===n)return p}if(o){let p=s[c];if(p&&Gt(p)&&p.type===n)return c}return null}function Xn(e,t,n,r){let o=e[n],i=t.data;if(a2(o)){let s=o;s.resolving&&G0(z0(i[n]));let a=Ni(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Te(s.injectImpl):null,u=ph(e,r,R.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&i2(n,i[n],t)}finally{l!==null&&Te(l),Ni(a),s.resolving=!1,vh()}}return o}function y2(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Pr)?e[Pr]:void 0;return typeof t=="number"?t>=0?t&bh:v2:t}function Wd(e,t,n){let r=1<<e;return!!(n[t+(e>>Ih)]&r)}function qd(e,t){return!(e&R.Self)&&!(e&R.Host&&t)}var cn=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Ah(this._tNode,this._lView,t,Ki(r),n)}};function v2(){return new cn(Ne(),X())}function rs(e){return Gr(()=>{let t=e.prototype.constructor,n=t[Di]||hc(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Di]||hc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function hc(e){return wf(e)?()=>{let t=hc(we(e));return t&&t()}:ln(e)}function C2(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[A]&2048&&!(s[A]&512);){let a=xh(i,s,n,r|R.Self,st);if(a!==st)return a;let c=i.parent;if(!c){let l=s[qf];if(l){let u=l.get(n,st,r);if(u!==st)return u}c=Th(s),s=s[nr]}i=c}return o}function Th(e){let t=e[P],n=t.type;return n===2?t.declTNode:n===1?e[tt]:null}function nl(e){return p2(Ne(),e)}function Zd(e,t=null,n=null,r){let o=Fh(e,t,n,r);return o.resolveInjectorInitializers(),o}function Fh(e,t=null,n=null,r,o=new Set){let i=[n||Be,C1(e)];return r=r||(typeof e=="object"?void 0:Ee(e)),new jr(i,t||zc(),r||null,o)}var Ue=class e{static{this.THROW_IF_NOT_FOUND=Lr}static{this.NULL=new bi}static create(t,n){if(Array.isArray(t))return Zd({name:""},n,t,"");{let r=t.name??"";return Zd({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=w({token:e,providedIn:"any",factory:()=>E(Tf)})}static{this.__NG_ELEMENT_ID__=-1}};var D2=new b("");D2.__NG_ELEMENT_ID__=e=>{let t=Ne();if(t===null)throw new I(204,!1);if(t.type&2)return t.value;if(e&R.Optional)return null;throw new I(204,!1)};var w2="ngOriginalError";function Ga(e){return e[w2]}var rl=(()=>{class e{static{this.__NG_ELEMENT_ID__=E2}static{this.__NG_ENV_ID__=n=>n}}return e})(),pc=class extends rl{constructor(t){super(),this._lView=t}onDestroy(t){return ih(this._lView,t),()=>j1(this._lView,t)}};function E2(){return new pc(X())}var It=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new fe(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}}return e})();var gc=class extends ce{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,Wf()&&(this.destroyRef=y(rl,{optional:!0})??void 0,this.pendingTasks=y(It,{optional:!0})??void 0)}emit(t){let n=$(null);try{super.next(t)}finally{$(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof ee&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},se=gc;function Ri(...e){}function Nh(e){let t,n;function r(){e=Ri;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Xd(e){return queueMicrotask(()=>e()),()=>{e=Ri}}var W=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new se(!1),this.onMicrotaskEmpty=new se(!1),this.onStable=new se(!1),this.onError=new se(!1),typeof Zone>"u")throw new I(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.callbackScheduled=!1,_2(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new I(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new I(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,b2,Ri,Ri);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},b2={};function ol(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function I2(e){e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,Zone.root.run(()=>{Nh(()=>{e.callbackScheduled=!1,mc(e),e.isCheckStableRunning=!0,ol(e),e.isCheckStableRunning=!1})}),mc(e))}function _2(e){let t=()=>{I2(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(M2(a))return n.invokeTask(o,i,s,a);try{return Kd(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),Yd(e)}},onInvoke:(n,r,o,i,s,a,c)=>{try{return Kd(e),n.invoke(o,i,s,a,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!S2(a)&&t(),Yd(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,mc(e),ol(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function mc(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Kd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Yd(e){e._nesting--,ol(e)}var Ui=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new se,this.onMicrotaskEmpty=new se,this.onStable=new se,this.onError=new se}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function M2(e){return Oh(e,"__ignore_ng_zone__")}function S2(e){return Oh(e,"__scheduler_tick__")}function Oh(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function A2(e="zone.js",t){return e==="noop"?new Ui:e==="zone.js"?new W(t):e}var Dt=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Ga(t);for(;n&&Ga(n);)n=Ga(n);return n||null}},x2=new b("",{providedIn:"root",factory:()=>{let e=y(W),t=y(Dt);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function T2(){return os(Ne(),X())}function os(e,t){return new vn(ze(e,t))}var vn=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=T2}}return e})();function Rh(e){return(e.flags&128)===128}var Uh=new Map,F2=0;function N2(){return F2++}function O2(e){Uh.set(e[es],e)}function R2(e){Uh.delete(e[es])}var Qd="__ngContext__";function pn(e,t){$t(t)?(e[Qd]=t[es],O2(t)):e[Qd]=t}function Ph(e){return Lh(e[Br])}function kh(e){return Lh(e[Qe])}function Lh(e){for(;e!==null&&!bt(e);)e=e[Qe];return e}var yc;function Vh(e){yc=e}function U2(){if(yc!==void 0)return yc;if(typeof document<"u")return document;throw new I(210,!1)}var is=new b("",{providedIn:"root",factory:()=>P2}),P2="ng",il=new b(""),ft=new b("",{providedIn:"platform",factory:()=>"unknown"});var sl=new b("",{providedIn:"root",factory:()=>U2().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var k2="h",L2="b";var V2=()=>null;function al(e,t,n=!1){return V2(e,t,n)}var jh=!1,j2=new b("",{providedIn:"root",factory:()=>jh});var fi;function B2(){if(fi===void 0&&(fi=null,De.trustedTypes))try{fi=De.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return fi}function Jd(e){return B2()?.createScriptURL(e)||e}var Pi=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Cf})`}};function Zr(e){return e instanceof Pi?e.changingThisBreaksApplicationSecurity:e}function cl(e,t){let n=$2(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Cf})`)}return n===t}function $2(e){return e instanceof Pi&&e.getTypeName()||null}var H2=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Bh(e){return e=String(e),e.match(H2)?e:"unsafe:"+e}var ss=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(ss||{});function Cn(e){let t=Hh();return t?t.sanitize(ss.URL,e)||"":cl(e,"URL")?Zr(e):Bh($n(e))}function z2(e){let t=Hh();if(t)return Jd(t.sanitize(ss.RESOURCE_URL,e)||"");if(cl(e,"ResourceURL"))return Jd(Zr(e));throw new I(904,!1)}function G2(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?z2:Cn}function $h(e,t,n){return G2(t,n)(e)}function Hh(){let e=X();return e&&e[ct].sanitizer}function zh(e){return e instanceof Function?e():e}function W2(e){return(e??y(Ue)).get(ft)==="browser"}var wt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(wt||{}),q2;function ll(e,t){return q2(e,t)}function Vn(e,t,n,r,o){if(r!=null){let i,s=!1;bt(r)?i=r:$t(r)&&(s=!0,r=r[Et]);let a=dt(r);e===0&&n!==null?o==null?Xh(t,n,a):ki(t,n,a,o||null,!0):e===1&&n!==null?ki(t,n,a,o||null,!0):e===2?cy(t,a,s):e===3&&t.destroyNode(a),i!=null&&uy(t,e,i,n,o)}}function Z2(e,t){return e.createText(t)}function X2(e,t,n){e.setValue(t,n)}function Gh(e,t,n){return e.createElement(t,n)}function K2(e,t){Wh(e,t),t[Et]=null,t[tt]=null}function Y2(e,t,n,r,o,i){r[Et]=o,r[tt]=t,as(e,r,n,1,o,i)}function Wh(e,t){t[ct].changeDetectionScheduler?.notify(8),as(e,t,t[he],2,null,null)}function Q2(e){let t=e[Br];if(!t)return Wa(e[P],e);for(;t;){let n=null;if($t(t))n=t[Br];else{let r=t[He];r&&(n=r)}if(!n){for(;t&&!t[Qe]&&t!==e;)$t(t)&&Wa(t[P],t),t=t[Ie];t===null&&(t=e),$t(t)&&Wa(t[P],t),n=t&&t[Qe]}t=n}}function J2(e,t,n,r){let o=He+r,i=n.length;r>0&&(n[o-1][Qe]=t),r<i-He?(t[Qe]=n[o],xf(n,He+r,t)):(n.push(t),t[Qe]=null),t[Ie]=n;let s=t[qn];s!==null&&n!==s&&qh(s,t);let a=t[Zn];a!==null&&a.insertView(e),cc(t),t[A]|=128}function qh(e,t){let n=e[Si],r=t[Ie];if($t(r))e[A]|=Ai.HasTransplantedViews;else{let o=r[Ie][lt];t[lt]!==o&&(e[A]|=Ai.HasTransplantedViews)}n===null?e[Si]=[t]:n.push(t)}function ul(e,t){let n=e[Si],r=n.indexOf(t);n.splice(r,1)}function vc(e,t){if(e.length<=He)return;let n=He+t,r=e[n];if(r){let o=r[qn];o!==null&&o!==e&&ul(o,r),t>0&&(e[n-1][Qe]=r[Qe]);let i=Ei(e,He+t);K2(r[P],r);let s=i[Zn];s!==null&&s.detachView(i[P]),r[Ie]=null,r[Qe]=null,r[A]&=-129}return r}function Zh(e,t){if(!(t[A]&256)){let n=t[he];n.destroyNode&&as(e,t,n,3,null,null),Q2(t)}}function Wa(e,t){if(t[A]&256)return;let n=$(null);try{t[A]&=-129,t[A]|=256,t[$e]&&wa(t[$e]),ty(e,t),ey(e,t),t[P].type===1&&t[he].destroy();let r=t[qn];if(r!==null&&bt(t[Ie])){r!==t[Ie]&&ul(r,t);let o=t[Zn];o!==null&&o.detachView(e)}R2(t)}finally{$(n)}}function ey(e,t){let n=e.cleanup,r=t[_i];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[_i]=null);let o=t[Bt];if(o!==null){t[Bt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function ty(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof hn)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];it(4,a,c);try{c.call(a)}finally{it(5,a,c)}}else{it(4,o,i);try{i.call(o)}finally{it(5,o,i)}}}}}function ny(e,t,n){return ry(e,t.parent,n)}function ry(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[Et];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===at.None||i===at.Emulated)return null}return ze(r,n)}}function ki(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Xh(e,t,n){e.appendChild(t,n)}function ef(e,t,n,r,o){r!==null?ki(e,t,n,r,o):Xh(e,t,n)}function Kh(e,t){return e.parentNode(t)}function oy(e,t){return e.nextSibling(t)}function iy(e,t,n){return ay(e,t,n)}function sy(e,t,n){return e.type&40?ze(e,n):null}var ay=sy,tf;function dl(e,t,n,r){let o=ny(e,r,t),i=t[he],s=r.parent||t[tt],a=iy(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)ef(i,o,n[c],a,!1);else ef(i,o,n,a,!1);tf!==void 0&&tf(i,r,t,n,o)}function Ur(e,t){if(t!==null){let n=t.type;if(n&3)return ze(t,e);if(n&4)return Cc(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Ur(e,r);{let o=e[t.index];return bt(o)?Cc(-1,o):dt(o)}}else{if(n&128)return Ur(e,t.next);if(n&32)return ll(t,e)()||dt(e[t.index]);{let r=Yh(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=dn(e[lt]);return Ur(o,r)}else return Ur(e,t.next)}}}return null}function Yh(e,t){if(t!==null){let r=e[lt][tt],o=t.projection;return r.projection[o]}return null}function Cc(e,t){let n=He+e+1;if(n<t.length){let r=t[n],o=r[P].firstChild;if(o!==null)return Ur(r,o)}return t[un]}function cy(e,t,n){e.removeChild(null,t,n)}function fl(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&pn(dt(a),r),n.flags|=2),(n.flags&32)!==32)if(c&8)fl(e,t,n.child,r,o,i,!1),Vn(t,e,o,a,i);else if(c&32){let l=ll(n,r),u;for(;u=l();)Vn(t,e,o,u,i);Vn(t,e,o,a,i)}else c&16?ly(e,t,r,n,o,i):Vn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function as(e,t,n,r,o,i){fl(n,r,e.firstChild,t,o,i,!1)}function ly(e,t,n,r,o,i){let s=n[lt],c=s[tt].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];Vn(t,e,o,u,i)}else{let l=c,u=s[Ie];Rh(r)&&(l.flags|=128),fl(e,t,l,u,o,i,!0)}}function uy(e,t,n,r,o){let i=n[un],s=dt(n);i!==s&&Vn(t,e,r,i,o);for(let a=He;a<n.length;a++){let c=n[a];as(c[P],c,e,t,r,i)}}function dy(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:wt.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=wt.Important),e.setStyle(n,r,o,i))}}function fy(e,t,n){e.setAttribute(t,"style",n)}function Qh(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Jh(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&nc(e,t,r),o!==null&&Qh(e,t,o),i!==null&&fy(e,t,i)}var _t={};function q(e=1){ep(Ge(),X(),yn()+e,!1)}function ep(e,t,n,r){if(!r)if((t[A]&3)===3){let i=e.preOrderCheckHooks;i!==null&&yi(t,i,n)}else{let i=e.preOrderHooks;i!==null&&vi(t,i,0,n)}fn(n)}function j(e,t=R.Default){let n=X();if(n===null)return E(e,t);let r=Ne();return Ah(r,n,we(e),t)}function tp(){let e="invalid";throw new Error(e)}function np(e,t,n,r,o,i){let s=$(null);try{let a=null;o&Ht.SignalBased&&(a=t[r][Ut]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ht.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Kf(t,a,r,i)}finally{$(s)}}function hy(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)fn(~o);else{let i=o,s=n[++r],a=n[++r];Q1(s,i);let c=t[i];a(2,c)}}}finally{fn(-1)}}function cs(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[Et]=o,d[A]=r|4|128|8|64,(l!==null||e&&e[A]&2048)&&(d[A]|=2048),rh(d),d[Ie]=d[nr]=e,d[Je]=n,d[ct]=s||e&&e[ct],d[he]=a||e&&e[he],d[Wn]=c||e&&e[Wn]||null,d[tt]=i,d[es]=N2(),d[Ii]=u,d[qf]=l,d[lt]=t.type==2?e[lt]:d,d}function ls(e,t,n,r,o){let i=e.data[t];if(i===null)i=py(e,t,n,r,o),Y1()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=q1();i.injectorIndex=s===null?-1:s.injectorIndex}return qr(i,!0),i}function py(e,t,n,r,o){let i=ch(),s=lh(),a=s?i:i&&i.parent,c=e.data[t]=Cy(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=c),i!==null&&(s?i.child==null&&c.parent!==null&&(i.child=c):i.next===null&&(i.next=c,c.prev=i)),c}function rp(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function op(e,t,n,r,o){let i=yn(),s=r&2;try{fn(-1),s&&t.length>ut&&ep(e,t,ut,!1),it(s?2:0,o),n(r,o)}finally{fn(i),it(s?3:1,o)}}function ip(e,t,n){if(Xf(t)){let r=$(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{$(r)}}}function sp(e,t,n){ah()&&(My(e,t,n,ze(n,t)),(n.flags&64)===64&&dp(e,t,n))}function ap(e,t,n=ze){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function cp(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=hl(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function hl(e,t,n,r,o,i,s,a,c,l,u){let d=ut+r,m=d+o,p=gy(d,m),v=typeof l=="function"?l():l;return p[P]={type:e,blueprint:p,template:n,queries:null,viewQuery:a,declTNode:t,data:p.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:m,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:u}}function gy(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:_t);return n}function my(e,t,n,r){let i=r.get(j2,jh)||n===at.ShadowDom,s=e.selectRootElement(t,i);return yy(s),s}function yy(e){vy(e)}var vy=()=>null;function Cy(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return z1()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function nf(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,c=Ht.None;Array.isArray(s)?(a=s[0],c=s[1]):a=s;let l=i;if(o!==null){if(!o.hasOwnProperty(i))continue;l=o[i]}e===0?rf(r,n,l,a,c):rf(r,n,l,a)}return r}function rf(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function Dy(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],c=null,l=null;for(let u=r;u<o;u++){let d=i[u],m=n?n.get(d):null,p=m?m.inputs:null,v=m?m.outputs:null;c=nf(0,d.inputs,u,c,p),l=nf(1,d.outputs,u,l,v);let D=c!==null&&s!==null&&!$c(t)?ky(c,u,s):null;a.push(D)}c!==null&&(c.hasOwnProperty("class")&&(t.flags|=8),c.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=c,t.outputs=l}function wy(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Ey(e,t,n,r,o,i,s,a){let c=ze(t,n),l=t.inputs,u;!a&&l!=null&&(u=l[r])?(pl(e,n,u,r,o),ts(t)&&by(n,t.index)):t.type&3?(r=wy(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)):t.type&12}function by(e,t){let n=qt(t,e);n[A]&16||(n[A]|=64)}function lp(e,t,n,r){if(ah()){let o=r===null?null:{"":-1},i=Ay(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&up(e,t,n,s,o,a),o&&xy(n,r,o)}n.mergedAttrs=Vr(n.mergedAttrs,n.attrs)}function up(e,t,n,r,o,i){for(let l=0;l<r.length;l++)fc(Oi(n,t),e,r[l].type);Fy(n,e.data.length,r.length);for(let l=0;l<r.length;l++){let u=r[l];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,c=rp(e,t,r.length,null);for(let l=0;l<r.length;l++){let u=r[l];n.mergedAttrs=Vr(n.mergedAttrs,u.hostAttrs),Ny(e,n,t,c,u),Ty(c,u,o),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),c++}Dy(e,n,i)}function Iy(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;_y(s)!=a&&s.push(a),s.push(n,r,i)}}function _y(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function My(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;ts(n)&&Oy(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Oi(n,t),pn(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let c=e.data[a],l=Xn(t,e,a,n);if(pn(l,t),s!==null&&Py(t,a-o,l,c,n,s),Gt(c)){let u=qt(n.index,t);u[Je]=Xn(t,e,a,n)}}}function dp(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=J1();try{fn(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];lc(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&Sy(c,l)}}finally{fn(-1),lc(s)}}function Sy(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Ay(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(u1(t,s.selectors,!1))if(r||(r=[]),Gt(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let c=a.length;Dc(e,t,c)}else r.unshift(s),Dc(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Dc(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function xy(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new I(-301,!1);r.push(t[o],i)}}}function Ty(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Gt(t)&&(n[""]=e)}}function Fy(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Ny(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=ln(o.type,!0)),s=new hn(i,Gt(o),j);e.blueprint[r]=s,n[r]=s,Iy(e,t,r,rp(e,n,o.hostVars,_t),o)}function Oy(e,t,n){let r=ze(t,e),o=cp(n),i=e[ct].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=us(e,cs(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function Ry(e,t,n,r,o,i){let s=ze(e,t);Uy(t[he],s,i,e.value,n,r,o)}function Uy(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?$n(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function Py(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let c=s[a++],l=s[a++],u=s[a++],d=s[a++];np(r,n,c,l,u,d)}}function ky(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function fp(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function hp(e,t){let n=e.contentQueries;if(n!==null){let r=$(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];hh(i),a.contentQueries(2,t[s],s)}}}finally{$(r)}}}function us(e,t){return e[Br]?e[$d][Qe]=t:e[Br]=t,e[$d]=t,t}function wc(e,t,n){hh(0);let r=$(null);try{t(e,n)}finally{$(r)}}function Ly(e){return e[_i]??=[]}function Vy(e){return e.cleanup??=[]}function pp(e,t){let n=e[Wn],r=n?n.get(Dt,null):null;r&&r.handleError(t)}function pl(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],c=n[i++],l=t[s],u=e.data[s];np(u,l,r,a,c,o)}}function gp(e,t,n){let r=th(t,e);X2(e[he],r,n)}function jy(e,t){let n=qt(t,e),r=n[P];By(r,n);let o=n[Et];o!==null&&n[Ii]===null&&(n[Ii]=al(o,n[Wn])),gl(r,n,n[Je])}function By(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function gl(e,t,n){Kc(t);try{let r=e.viewQuery;r!==null&&wc(1,r,n);let o=e.template;o!==null&&op(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Zn]?.finishViewCreation(e),e.staticContentQueries&&hp(e,t),e.staticViewQueries&&wc(2,e.viewQuery,n);let i=e.components;i!==null&&$y(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[A]&=-5,Yc()}}function $y(e,t){for(let n=0;n<t.length;n++)jy(e,t[n])}function Hy(e,t,n,r){let o=$(null);try{let i=t.tView,a=e[A]&4096?4096:16,c=cs(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[qn]=l;let u=e[Zn];return u!==null&&(c[Zn]=u.createEmbeddedView(i)),gl(i,c,n),c}finally{$(o)}}function of(e,t){return!t||t.firstChild===null||Rh(e)}function zy(e,t,n,r=!0){let o=t[P];if(J2(o,t,e,n),r){let s=Cc(n,e),a=t[he],c=Kh(a,e[un]);c!==null&&Y2(o,e[tt],a,t,c,s)}let i=t[Ii];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Li(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(dt(i)),bt(i)&&Gy(i,r);let s=n.type;if(s&8)Li(e,t,n.child,r);else if(s&32){let a=ll(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=Yh(t,n);if(Array.isArray(a))r.push(...a);else{let c=dn(t[lt]);Li(c[P],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Gy(e,t){for(let n=He;n<e.length;n++){let r=e[n],o=r[P].firstChild;o!==null&&Li(r[P],r,o,t)}e[un]!==e[Et]&&t.push(e[un])}var mp=[];function Wy(e){return e[$e]??qy(e)}function qy(e){let t=mp.pop()??Object.create(Xy);return t.lView=e,t}function Zy(e){e.lView[$e]!==e&&(e.lView=null,mp.push(e))}var Xy=B(C({},Fr),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ns(e.lView)},consumerOnSignalRead(){this.lView[$e]=this}});function Ky(e){let t=e[$e]??Object.create(Yy);return t.lView=e,t}var Yy=B(C({},Fr),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=dn(e.lView);for(;t&&!yp(t[P]);)t=dn(t);t&&oh(t)},consumerOnSignalRead(){this.lView[$e]=this}});function yp(e){return e.type!==2}var Qy=100;function vp(e,t=!0,n=0){let r=e[ct],o=r.rendererFactory,i=!1;i||o.begin?.();try{Jy(e,n)}catch(s){throw t&&pp(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function Jy(e,t){let n=uh();try{zd(!0),Ec(e,t);let r=0;for(;$r(e);){if(r===Qy)throw new I(103,!1);r++,Ec(e,1)}}finally{zd(n)}}function ev(e,t,n,r){let o=t[A];if((o&256)===256)return;let i=!1,s=!1;!i&&t[ct].inlineEffectRunner?.flush(),Kc(t);let a=!0,c=null,l=null;i||(yp(e)?(l=Wy(t),c=Vo(l)):Zu()===null?(a=!1,l=Ky(t),c=Vo(l)):t[$e]&&(wa(t[$e]),t[$e]=null));try{rh(t),K1(e.bindingStartIndex),n!==null&&op(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let p=e.preOrderCheckHooks;p!==null&&yi(t,p,null)}else{let p=e.preOrderHooks;p!==null&&vi(t,p,0,null),$a(t,0)}if(s||tv(t),Cp(t,0),e.contentQueries!==null&&hp(e,t),!i)if(u){let p=e.contentCheckHooks;p!==null&&yi(t,p)}else{let p=e.contentHooks;p!==null&&vi(t,p,1),$a(t,1)}hy(e,t);let d=e.components;d!==null&&wp(t,d,0);let m=e.viewQuery;if(m!==null&&wc(2,m,r),!i)if(u){let p=e.viewCheckHooks;p!==null&&yi(t,p)}else{let p=e.viewHooks;p!==null&&vi(t,p,2),$a(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[Ba]){for(let p of t[Ba])p();t[Ba]=null}i||(t[A]&=-73)}catch(u){throw i||ns(t),u}finally{l!==null&&(Ca(l,c),a&&Zy(l)),Yc()}}function Cp(e,t){for(let n=Ph(e);n!==null;n=kh(n))for(let r=He;r<n.length;r++){let o=n[r];Dp(o,t)}}function tv(e){for(let t=Ph(e);t!==null;t=kh(t)){if(!(t[A]&Ai.HasTransplantedViews))continue;let n=t[Si];for(let r=0;r<n.length;r++){let o=n[r];oh(o)}}}function nv(e,t,n){let r=qt(t,e);Dp(r,n)}function Dp(e,t){Wc(e)&&Ec(e,t)}function Ec(e,t){let r=e[P],o=e[A],i=e[$e],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Da(i)),s||=!1,i&&(i.dirty=!1),e[A]&=-9217,s)ev(r,e,r.template,e[Je]);else if(o&8192){Cp(e,1);let a=r.components;a!==null&&wp(e,a,1)}}function wp(e,t,n){for(let r=0;r<t.length;r++)nv(e,t[r],n)}function ml(e,t){let n=uh()?64:1088;for(e[ct].changeDetectionScheduler?.notify(t);e;){e[A]|=n;let r=dn(e);if(sc(e)&&!r)return e;e=r}return null}var gn=class{get rootNodes(){let t=this._lView,n=t[P];return Li(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Je]}set context(t){this._lView[Je]=t}get destroyed(){return(this._lView[A]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[Ie];if(bt(t)){let n=t[Mi],r=n?n.indexOf(this):-1;r>-1&&(vc(t,r),Ei(n,r))}this._attachedToViewContainer=!1}Zh(this._lView[P],this._lView)}onDestroy(t){ih(this._lView,t)}markForCheck(){ml(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[A]&=-129}reattach(){cc(this._lView),this._lView[A]|=128}detectChanges(){this._lView[A]|=1024,vp(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new I(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=sc(this._lView),n=this._lView[qn];n!==null&&!t&&ul(n,this._lView),Wh(this._lView[P],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new I(902,!1);this._appRef=t;let n=sc(this._lView),r=this._lView[qn];r!==null&&!n&&qh(r,this._lView),cc(this._lView)}},ds=(()=>{class e{static{this.__NG_ELEMENT_ID__=iv}}return e})(),rv=ds,ov=class extends rv{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Hy(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new gn(o)}};function iv(){return sv(Ne(),X())}function sv(e,t){return e.type&4?new ov(t,e,os(e,t)):null}var s6=new RegExp(`^(\\d+)*(${L2}|${k2})*(.*)`);var av=()=>null;function sf(e,t){return av(e,t)}var Kn=class{},Ep=new b("",{providedIn:"root",factory:()=>!1});var bp=new b(""),bc=class{},Vi=class{};function cv(e){let t=Error(`No component factory found for ${Ee(e)}.`);return t[lv]=e,t}var lv="ngComponent";var Ic=class{resolveComponentFactory(t){throw cv(t)}},Yn=class{static{this.NULL=new Ic}},Qn=class{},sr=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>uv()}}return e})();function uv(){let e=X(),t=Ne(),n=qt(t.index,e);return($t(n)?n:e)[he]}var dv=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>null})}}return e})();var af=new Set;function ar(e){af.has(e)||(af.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var Fe=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Fe||{}),fv={destroy(){}};function yl(e,t){!t&&T1(yl);let n=t?.injector??y(Ue);return W2(n)?(ar("NgAfterNextRender"),pv(e,n,!0,t?.phase??Fe.MixedReadWrite)):fv}function hv(e,t){if(e instanceof Function)switch(t){case Fe.EarlyRead:return{earlyRead:e};case Fe.Write:return{write:e};case Fe.MixedReadWrite:return{mixedReadWrite:e};case Fe.Read:return{read:e}}return e}function pv(e,t,n,r){let o=hv(e,r),i=t.get(vl),s=i.handler??=new Mc,a=[],c=[],l=()=>{for(let p of c)s.unregister(p);u()},u=t.get(rl).onDestroy(l),d=0,m=(p,v)=>{if(!v)return;let D=n?(...F)=>(d--,d<1&&l(),v(...F)):v,_=Pe(t,()=>new _c(p,a,D));s.register(_),c.push(_),d++};return m(Fe.EarlyRead,o.earlyRead),m(Fe.Write,o.write),m(Fe.MixedReadWrite,o.mixedReadWrite),m(Fe.Read,o.read),{destroy:l}}var _c=class{constructor(t,n,r){this.phase=t,this.pipelinedArgs=n,this.callbackFn=r,this.zone=y(W),this.errorHandler=y(Dt,{optional:!0}),y(Kn,{optional:!0})?.notify(6)}invoke(){try{let t=this.zone.runOutsideAngular(()=>this.callbackFn.apply(null,this.pipelinedArgs));this.pipelinedArgs.splice(0,this.pipelinedArgs.length,t)}catch(t){this.errorHandler?.handleError(t)}}},Mc=class{constructor(){this.executingCallbacks=!1,this.buckets={[Fe.EarlyRead]:new Set,[Fe.Write]:new Set,[Fe.MixedReadWrite]:new Set,[Fe.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},vl=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Sc(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Nd(o,a);else if(i==2){let c=a,l=t[++s];r=Nd(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var ji=class extends Yn{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=zt(t);return new Jn(n,this.ngModule)}};function cf(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:Ht.None;t?n.push({propName:s,templateName:r,isSignal:(a&Ht.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function gv(e){let t=e.toLowerCase();return t==="svg"?eh:t==="math"?P1:null}var Jn=class extends Vi{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=cf(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return cf(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=p1(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=$(null);try{o=o||this.ngModule;let s=o instanceof be?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new uc(t,s):t,c=a.get(Qn,null);if(c===null)throw new I(407,!1);let l=a.get(dv,null),u=a.get(vl,null),d=a.get(Kn,null),m={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,afterRenderEventManager:u,changeDetectionScheduler:d},p=c.createRenderer(null,this.componentDef),v=this.componentDef.selectors[0][0]||"div",D=r?my(p,r,this.componentDef.encapsulation,a):Gh(p,v,gv(v)),_=512;this.componentDef.signals?_|=4096:this.componentDef.onPush||(_|=16);let F=null;D!==null&&(F=al(D,a,!0));let ve=hl(0,null,null,1,0,null,null,null,null,null,null),U=cs(null,ve,null,_,null,null,m,p,a,null,F);Kc(U);let Ce,Me;try{let de=this.componentDef,mt,ga=null;de.findHostDirectiveDefs?(mt=[],ga=new Map,de.findHostDirectiveDefs(de,mt,ga),mt.push(de)):mt=[de];let t0=mv(U,D),n0=yv(t0,D,de,mt,U,m,p);Me=nh(ve,ut),D&&Dv(p,de,D,r),n!==void 0&&wv(Me,this.ngContentSelectors,n),Ce=Cv(n0,de,mt,ga,U,[Ev]),gl(ve,U,null)}finally{Yc()}return new Ac(this.componentType,Ce,os(Me,U),U,Me)}finally{$(i)}}},Ac=class extends bc{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new gn(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;pl(i[P],i,o,t,n),this.previousInputValues.set(t,n);let s=qt(this._tNode.index,i);ml(s,1)}}get injector(){return new cn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function mv(e,t){let n=e[P],r=ut;return e[r]=t,ls(n,r,2,"#host",null)}function yv(e,t,n,r,o,i,s){let a=o[P];vv(r,e,t,s);let c=null;t!==null&&(c=al(t,o[Wn]));let l=i.rendererFactory.createRenderer(t,n),u=16;n.signals?u=4096:n.onPush&&(u=64);let d=cs(o,cp(n),null,u,o[e.index],e,i,l,null,null,c);return a.firstCreatePass&&Dc(a,e,r.length-1),us(o,d),o[e.index]=d}function vv(e,t,n,r){for(let o of e)t.mergedAttrs=Vr(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(Sc(t,t.mergedAttrs,!0),n!==null&&Jh(r,n,t))}function Cv(e,t,n,r,o,i){let s=Ne(),a=o[P],c=ze(s,o);up(a,o,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,m=Xn(o,a,d,s);pn(m,o)}dp(a,o,s),c&&pn(c,o);let l=Xn(o,a,s.directiveStart+s.componentOffset,s);if(e[Je]=o[Je]=l,i!==null)for(let u of i)u(l,t);return ip(a,s,o),l}function Dv(e,t,n,r){if(r)nc(e,n,["ng-version","18.1.4"]);else{let{attrs:o,classes:i}=g1(t.selectors[0]);o&&nc(e,n,o),i&&i.length>0&&Qh(e,n,i.join(" "))}}function wv(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Ev(){let e=Ne();el(X()[P],e)}var cr=(()=>{class e{static{this.__NG_ELEMENT_ID__=bv}}return e})();function bv(){let e=Ne();return _v(e,X())}var Iv=cr,Ip=class extends Iv{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return os(this._hostTNode,this._hostLView)}get injector(){return new cn(this._hostTNode,this._hostLView)}get parentInjector(){let t=tl(this._hostTNode,this._hostLView);if(Eh(t)){let n=Fi(t,this._hostLView),r=Ti(t),o=n[P].data[r+8];return new cn(o,n)}else return new cn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=lf(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-He}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=sf(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,of(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!N1(t),a;if(s)a=n;else{let v=n||{};a=v.index,r=v.injector,o=v.projectableNodes,i=v.environmentInjector||v.ngModuleRef}let c=s?t:new Jn(zt(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let D=(s?l:this.parentInjector).get(be,null);D&&(i=D)}let u=zt(c.componentType??{}),d=sf(this._lContainer,u?.id??null),m=d?.firstChild??null,p=c.create(l,o,m,i);return this.insertImpl(p.hostView,a,of(this._hostTNode,d)),p}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(L1(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[Ie],l=new Ip(c,c[tt],c[Ie]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return zy(s,o,i,r),t.attachToViewContainerRef(),xf(qa(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=lf(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=vc(this._lContainer,n);r&&(Ei(qa(this._lContainer),n),Zh(r[P],r))}detach(t){let n=this._adjustIndex(t,-1),r=vc(this._lContainer,n);return r&&Ei(qa(this._lContainer),n)!=null?new gn(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function lf(e){return e[Mi]}function qa(e){return e[Mi]||(e[Mi]=[])}function _v(e,t){let n,r=t[e.index];return bt(r)?n=r:(n=fp(r,t,null,e),t[e.index]=n,us(t,n)),Sv(n,t,e,r),new Ip(n,e,t)}function Mv(e,t){let n=e[he],r=n.createComment(""),o=ze(t,e),i=Kh(n,o);return ki(n,i,r,oy(n,o),!1),r}var Sv=Tv,Av=()=>!1;function xv(e,t,n){return Av(e,t,n)}function Tv(e,t,n,r){if(e[un])return;let o;n.type&8?o=dt(r):o=Mv(t,n),e[un]=o}function Xr(e,t){ar("NgSignals");let n=id(e),r=n[Ut];return t?.equal&&(r.equal=t.equal),n.set=o=>Ea(r,o),n.update=o=>sd(r,o),n.asReadonly=Fv.bind(n),n}function Fv(){let e=this[Ut];if(e.readonlyFn===void 0){let t=()=>this();t[Ut]=e,e.readonlyFn=t}return e.readonlyFn}function Nv(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(Pv))}return i}return Bi.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(l=>{o.template=l}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let l=o.styles.length,u=o.styleUrls;o.styleUrls.forEach((d,m)=>{a.push(""),s.push(r(d).then(p=>{a[l+m]=p,u.splice(u.indexOf(d),1),u.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(l=>{a.push(l),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>kv(i));t.push(c)}),Rv(),Promise.all(t).then(()=>{})}var Bi=new Map,Ov=new Set;function Rv(){let e=Bi;return Bi=new Map,e}function Uv(){return Bi.size===0}function Pv(e){return typeof e=="string"?e:e.text()}function kv(e){Ov.delete(e)}function Lv(e){return Object.getPrototypeOf(e.prototype).constructor}function fs(e){let t=Lv(e.type),n=!0,r=[e];for(;t;){let o;if(Gt(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new I(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=hi(e.inputs),s.inputTransforms=hi(e.inputTransforms),s.declaredInputs=hi(e.declaredInputs),s.outputs=hi(e.outputs);let a=o.hostBindings;a&&Hv(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Bv(e,c),l&&$v(e,l),Vv(e,o),P0(e.outputs,o.outputs),Gt(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===fs&&(n=!1)}}t=Object.getPrototypeOf(t)}jv(r)}function Vv(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function jv(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Vr(o.hostAttrs,n=Vr(n,o.hostAttrs))}}function hi(e){return e===Hn?{}:e===Be?[]:e}function Bv(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function $v(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Hv(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function Cl(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var Wt=class{},Hr=class{};var $i=class extends Wt{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new ji(this);let o=Vf(t);this._bootstrapComponents=zh(o.bootstrap),this._r3Injector=Fh(t,n,[{provide:Wt,useValue:this},{provide:Yn,useValue:this.componentFactoryResolver},...r],Ee(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Hi=class extends Hr{constructor(t){super(),this.moduleType=t}create(t){return new $i(this.moduleType,t,[])}};function zv(e,t,n){return new $i(e,t,n)}var xc=class extends Wt{constructor(t){super(),this.componentFactoryResolver=new ji(this),this.instance=null;let n=new jr([...t.providers,{provide:Wt,useValue:this},{provide:Yn,useValue:this.componentFactoryResolver}],t.parent||zc(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function hs(e,t,n=null){return new xc({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function _p(e){return Wv(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Gv(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Wv(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function qv(e,t,n){return e[t]=n}function er(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function Mp(e,t,n,r){let o=er(e,t,n);return er(e,t+1,r)||o}function Zv(e){return(e.flags&32)===32}function Xv(e,t,n,r,o,i,s,a,c){let l=t.consts,u=ls(t,e,4,s||null,a||null);lp(t,n,u,xi(l,c)),el(t,u);let d=u.tView=hl(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function Kv(e,t,n,r,o,i,s,a,c,l){let u=n+ut,d=t.firstCreatePass?Xv(u,t,e,r,o,i,s,a,c):t.data[u];qr(d,!1);let m=Yv(t,e,d,n);Qc()&&dl(t,e,m,d),pn(m,e);let p=fp(m,e,m,d);return e[u]=p,us(e,p),xv(p,d,e),Gc(d)&&sp(t,e,d),c!=null&&ap(e,d,l),d}function We(e,t,n,r,o,i,s,a){let c=X(),l=Ge(),u=xi(l.consts,i);return Kv(c,l,e,t,n,r,o,u,s,a),We}var Yv=Qv;function Qv(e,t,n,r){return Jc(!0),t[he].createComment("")}function ps(e,t,n,r){let o=X(),i=Xc();if(er(o,i,t)){let s=Ge(),a=Ch();Ry(a,o,e,t,n,r)}return ps}function Jv(e,t,n,r){return er(e,Xc(),n)?t+$n(n)+r:_t}function eC(e,t,n,r,o,i){let s=X1(),a=Mp(e,s,n,o);return fh(2),a?t+$n(n)+r+$n(o)+i:_t}function pi(e,t){return e<<17|t<<2}function mn(e){return e>>17&32767}function tC(e){return(e&2)==2}function nC(e,t){return e&131071|t<<17}function Tc(e){return e|2}function tr(e){return(e&131068)>>2}function Za(e,t){return e&-131069|t<<2}function rC(e){return(e&1)===1}function Fc(e){return e|1}function oC(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=mn(s),c=tr(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Wr(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let m=mn(e[a+1]);e[r+1]=pi(m,a),m!==0&&(e[m+1]=Za(e[m+1],r)),e[a+1]=nC(e[a+1],r)}else e[r+1]=pi(a,0),a!==0&&(e[a+1]=Za(e[a+1],r)),a=r;else e[r+1]=pi(c,0),a===0?a=r:e[c+1]=Za(e[c+1],r),c=r;l&&(e[r+1]=Tc(e[r+1])),uf(e,u,r,!0),uf(e,u,r,!1),iC(t,u,e,r,i),s=pi(a,c),i?t.classBindings=s:t.styleBindings=s}function iC(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Wr(i,t)>=0&&(n[r+1]=Fc(n[r+1]))}function uf(e,t,n,r){let o=e[n+1],i=t===null,s=r?mn(o):tr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];sC(c,t)&&(a=!0,e[s+1]=r?Fc(l):Tc(l)),s=r?mn(l):tr(l)}a&&(e[n+1]=r?Tc(o):Fc(o))}function sC(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Wr(e,t)>=0:!1}function me(e,t,n){let r=X(),o=Xc();if(er(r,o,t)){let i=Ge(),s=Ch();Ey(i,s,r,e,t,r[he],n,!1)}return me}function df(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";pl(e,n,i[s],s,r)}function Dl(e,t){return aC(e,t,null,!0),Dl}function aC(e,t,n,r){let o=X(),i=Ge(),s=fh(2);if(i.firstUpdatePass&&lC(i,e,s,r),t!==_t&&er(o,s,t)){let a=i.data[yn()];pC(i,a,o,o[he],e,o[s+1]=gC(t,n),r,s)}}function cC(e,t){return t>=e.expandoStartIndex}function lC(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[yn()],s=cC(e,n);mC(i,r)&&t===null&&!s&&(t=!1),t=uC(o,i,t,r),oC(o,i,t,n,s,r)}}function uC(e,t,n,r){let o=e2(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=Xa(null,e,t,n,r),n=zr(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=Xa(o,e,t,n,r),i===null){let c=dC(e,t,r);c!==void 0&&Array.isArray(c)&&(c=Xa(null,e,t,c[1],r),c=zr(c,t.attrs,r),fC(e,t,r,c))}else i=hC(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function dC(e,t,n){let r=n?t.classBindings:t.styleBindings;if(tr(r)!==0)return e[mn(r)]}function fC(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[mn(o)]=r}function hC(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=zr(r,s,n)}return zr(r,t.attrs,n)}function Xa(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=zr(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function zr(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),n1(e,s,n?!0:t[++i]))}return e===void 0?null:e}function pC(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=rC(l)?ff(c,t,n,o,tr(l),s):void 0;if(!zi(u)){zi(i)||tC(l)&&(i=ff(c,null,n,o,a,s));let d=th(yn(),n);dy(r,s,d,o,i)}}function ff(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,m=n[o+1];m===_t&&(m=d?Be:void 0);let p=d?Va(m,r):u===r?m:void 0;if(l&&!zi(p)&&(p=Va(c,r)),zi(p)&&(a=p,s))return a;let v=e[o+1];o=s?mn(v):tr(v)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=Va(c,r))}return a}function zi(e){return e!==void 0}function gC(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=Ee(Zr(e)))),e}function mC(e,t){return(e.flags&(t?8:16))!==0}function yC(e,t,n,r,o,i){let s=t.consts,a=xi(s,o),c=ls(t,e,2,r,a);return lp(t,n,c,xi(s,i)),c.attrs!==null&&Sc(c,c.attrs,!1),c.mergedAttrs!==null&&Sc(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function f(e,t,n,r){let o=X(),i=Ge(),s=ut+e,a=o[he],c=i.firstCreatePass?yC(s,i,o,t,n,r):i.data[s],l=vC(i,o,c,a,t,e);o[s]=l;let u=Gc(c);return qr(c,!0),Jh(a,l,c),!Zv(c)&&Qc()&&dl(i,o,l,c),B1()===0&&pn(l,o),$1(),u&&(sp(i,o,c),ip(i,c,o)),r!==null&&ap(o,c),f}function h(){let e=Ne();lh()?Z1():(e=e.parent,qr(e,!1));let t=e;G1(t)&&W1(),H1();let n=Ge();return n.firstCreatePass&&(el(n,e),Xf(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&c2(t)&&df(n,t,X(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&l2(t)&&df(n,t,X(),t.stylesWithoutHost,!1),h}function g(e,t,n,r){return f(e,t,n,r),h(),g}var vC=(e,t,n,r,o,i)=>(Jc(!0),Gh(r,o,o2()));function Sp(){return X()}var an=void 0;function CC(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var DC=["en",[["a","p"],["AM","PM"],an],[["AM","PM"],an,an],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],an,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],an,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",an,"{1} 'at' {0}",an],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",CC],Ka={};function qe(e){let t=wC(e),n=hf(t);if(n)return n;let r=t.split("-")[0];if(n=hf(r),n)return n;if(r==="en")return DC;throw new I(701,!1)}function hf(e){return e in Ka||(Ka[e]=De.ng&&De.ng.common&&De.ng.common.locales&&De.ng.common.locales[e]),Ka[e]}var J=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(J||{});function wC(e){return e.toLowerCase().replace(/_/g,"-")}var Gi="en-US";var EC=Gi;function bC(e){typeof e=="string"&&(EC=e.toLowerCase().replace(/_/g,"-"))}var IC=(e,t,n)=>{};function ht(e,t,n,r){let o=X(),i=Ge(),s=Ne();return MC(i,o,o[he],s,e,t,r),ht}function _C(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[_i],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function MC(e,t,n,r,o,i,s){let a=Gc(r),l=e.firstCreatePass&&Vy(e),u=t[Je],d=Ly(t),m=!0;if(r.type&3||s){let D=ze(r,t),_=s?s(D):D,F=d.length,ve=s?Ce=>s(dt(Ce[r.index])):r.index,U=null;if(!s&&a&&(U=_C(e,t,o,r.index)),U!==null){let Ce=U.__ngLastListenerFn__||U;Ce.__ngNextListenerFn__=i,U.__ngLastListenerFn__=i,m=!1}else{i=gf(r,t,u,i),IC(D,o,i);let Ce=n.listen(_,o,i);d.push(i,Ce),l&&l.push(o,ve,F,F+1)}}else i=gf(r,t,u,i);let p=r.outputs,v;if(m&&p!==null&&(v=p[o])){let D=v.length;if(D)for(let _=0;_<D;_+=2){let F=v[_],ve=v[_+1],Me=t[F][ve].subscribe(i),de=d.length;d.push(i,Me),l&&l.push(o,r.index,de,-(de+1))}}}function pf(e,t,n,r){let o=$(null);try{return it(6,t,n),n(r)!==!1}catch(i){return pp(e,i),!1}finally{it(7,t,n),$(o)}}function gf(e,t,n,r){return function o(i){if(i===Function)return r;let s=e.componentOffset>-1?qt(e.index,t):t;ml(s,5);let a=pf(t,n,r,i),c=o.__ngNextListenerFn__;for(;c;)a=pf(t,n,c,i)&&a,c=c.__ngNextListenerFn__;return a}}function Zt(e=1){return n2(e)}function SC(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function S(e,t=""){let n=X(),r=Ge(),o=e+ut,i=r.firstCreatePass?ls(r,o,1,t,null):r.data[o],s=AC(r,n,i,t,e);n[o]=s,Qc()&&dl(r,n,s,i),qr(i,!1)}var AC=(e,t,n,r,o)=>(Jc(!0),Z2(t[he],r));function ke(e){return lr("",e,""),ke}function lr(e,t,n){let r=X(),o=Jv(r,e,t,n);return o!==_t&&gp(r,yn(),o),lr}function gs(e,t,n,r,o){let i=X(),s=eC(i,e,t,n,r,o);return s!==_t&&gp(i,yn(),s),gs}function xC(e,t,n){let r=Ge();if(r.firstCreatePass){let o=Gt(e);Nc(n,r.data,r.blueprint,o,!0),Nc(t,r.data,r.blueprint,o,!1)}}function Nc(e,t,n,r,o){if(e=we(e),Array.isArray(e))for(let i=0;i<e.length;i++)Nc(e[i],t,n,r,o);else{let i=Ge(),s=X(),a=Ne(),c=Gn(e)?e:we(e.provide),l=Gf(e),u=a.providerIndexes&1048575,d=a.directiveStart,m=a.providerIndexes>>20;if(Gn(e)||!e.multi){let p=new hn(l,o,j),v=Qa(c,t,o?u:u+m,d);v===-1?(fc(Oi(a,s),i,c),Ya(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(p),s.push(p)):(n[v]=p,s[v]=p)}else{let p=Qa(c,t,u+m,d),v=Qa(c,t,u,u+m),D=p>=0&&n[p],_=v>=0&&n[v];if(o&&!_||!o&&!D){fc(Oi(a,s),i,c);let F=NC(o?FC:TC,n.length,o,r,l);!o&&_&&(n[v].providerFactory=F),Ya(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(F),s.push(F)}else{let F=Ap(n[o?v:p],l,!o&&r);Ya(i,e,p>-1?p:v,F)}!o&&r&&_&&n[v].componentProviders++}}}function Ya(e,t,n,r){let o=Gn(t),i=b1(t);if(o||i){let c=(i?we(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function Ap(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Qa(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function TC(e,t,n,r){return Oc(this.multi,[])}function FC(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Xn(n,n[P],this.providerFactory.index,r);i=a.slice(0,s),Oc(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],Oc(o,i);return i}function Oc(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function NC(e,t,n,r,o){let i=new hn(e,n,j);return i.multi=[],i.index=t,i.componentProviders=0,Ap(i,o,r&&!n),i}function xp(e,t=[]){return n=>{n.providersResolver=(r,o)=>xC(r,o?o(e):e,t)}}var OC=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=$f(!1,n.type),o=r.length>0?hs([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(E(be))})}}return e})();function Tp(e){ar("NgStandalone"),e.getStandaloneInjector=t=>t.get(OC).getOrCreateStandaloneInjector(e)}function Fp(e,t,n,r,o){return Np(X(),dh(),e,t,n,r,o)}function RC(e,t){let n=e[t];return n===_t?void 0:n}function Np(e,t,n,r,o,i,s){let a=t+n;return Mp(e,a,o,i)?qv(e,a+2,s?r.call(s,o,i):r(o,i)):RC(e,a+2)}function Kr(e,t){let n=Ge(),r,o=e+ut;n.firstCreatePass?(r=UC(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=ln(r.type,!0)),s,a=Te(j);try{let c=Ni(!1),l=i();return Ni(c),SC(n,X(),o,l),l}finally{Te(a)}}function UC(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function Yr(e,t,n,r){let o=e+ut,i=X(),s=k1(i,o);return PC(i,o)?Np(i,dh(),t,s.transform,n,r,s):s.transform(n,r)}function PC(e,t){return e[P].data[t].pure}var gi=null;function kC(e){gi!==null&&(e.defaultEncapsulation!==gi.defaultEncapsulation||e.preserveWhitespaces!==gi.preserveWhitespaces)||(gi=e)}var ms=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var wl=new b(""),Qr=new b(""),ys=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,El||(LC(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{W.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static{this.\u0275fac=function(r){return new(r||e)(E(W),E(vs),E(Qr))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),vs=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return El?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function LC(e){El=e}var El;function Dn(e){return!!e&&typeof e.then=="function"}function Op(e){return!!e&&typeof e.subscribe=="function"}var Cs=new b(""),Rp=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=y(Cs,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(Dn(i))n.push(i);else if(Op(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Ds=new b("");function VC(){od(()=>{throw new I(600,!1)})}function jC(e){return e.isBoundToModule}var BC=10;function $C(e,t,n){try{let r=n();return Dn(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Up(e,t){return Array.isArray(t)?t.reduce(Up,e):C(C({},e),t)}var Xt=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=y(x2),this.afterRenderEffectManager=y(vl),this.zonelessEnabled=y(Ep),this.externalTestViews=new Set,this.beforeRender=new ce,this.afterTick=new ce,this.componentTypes=[],this.components=[],this.isStable=y(It).hasPendingTasks.pipe(T(n=>!n)),this._injector=y(be)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Vi;if(!this._injector.get(Rp).done){let m=!o&&Lf(n),p=!1;throw new I(405,p)}let s;o?s=n:s=this._injector.get(Yn).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=jC(s)?void 0:this._injector.get(Wt),c=r||s.selector,l=s.create(Ue.NULL,[],c,a),u=l.location.nativeElement,d=l.injector.get(wl,null);return d?.registerApplication(u),l.onDestroy(()=>{this.detachView(l.hostView),Ci(this.components,l),d?.unregisterApplication(u)}),this._loadComponent(l),l}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new I(101,!1);let r=$(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this._runningTick=!1,$(r),this.afterTick.next()}}detectChangesInAttachedViews(n){let r=null;this._injector.destroyed||(r=this._injector.get(Qn,null,{optional:!0}));let o=0,i=this.afterRenderEffectManager;for(;o<BC;){let s=o===0;if(n||!s){this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:c}of this._views)HC(a,c,s,this.zonelessEnabled)}else r?.begin?.(),r?.end?.();if(o++,i.executeInternalCallbacks(),!this.allViews.some(({_lView:a})=>$r(a))&&(i.execute(),!this.allViews.some(({_lView:a})=>$r(a))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Ci(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(Ds,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Ci(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new I(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Ci(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function HC(e,t,n,r){if(!n&&!$r(e))return;vp(e,t,n&&!r?0:1)}var Rc=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ws=(()=>{class e{compileModuleSync(n){return new Hi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Vf(n),i=zh(o.declarations).reduce((s,a)=>{let c=zt(a);return c&&s.push(new Jn(c)),s},[]);return new Rc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),zC=new b("");function GC(e,t,n){let r=new Hi(n);return Promise.resolve(r)}function mf(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var WC=(()=>{class e{constructor(){this.zone=y(W),this.changeDetectionScheduler=y(Kn),this.applicationRef=y(Xt)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function qC({ngZoneFactory:e,ignoreChangesOutsideZone:t}){return e??=()=>new W(Pp()),[{provide:W,useFactory:e},{provide:zn,multi:!0,useFactory:()=>{let n=y(WC,{optional:!0});return()=>n.initialize()}},{provide:zn,multi:!0,useFactory:()=>{let n=y(ZC);return()=>{n.initialize()}}},t===!0?{provide:bp,useValue:!0}:[]]}function Pp(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var ZC=(()=>{class e{constructor(){this.subscription=new ee,this.initialized=!1,this.zone=y(W),this.pendingTasks=y(It)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{W.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{W.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var XC=(()=>{class e{constructor(){this.appRef=y(Xt),this.taskService=y(It),this.ngZone=y(W),this.zonelessEnabled=y(Ep),this.disableScheduling=y(bp,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new ee,this.cancelScheduledCallback=null,this.shouldRefreshViews=!1,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof Ui||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 3:case 2:case 0:case 4:case 5:case 1:{this.shouldRefreshViews=!0;break}case 8:case 7:case 6:case 9:default:}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?Xd:Nh;this.pendingRenderTaskId=this.taskService.add(),this.zoneIsDefined?Zone.root.run(()=>{this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}):this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&W.isInAngularZone())}tick(n){if(this.runningTick||this.appRef.destroyed)return;let r=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick(n)},void 0,this.schedulerTickApplyArgs)}catch(o){throw this.taskService.remove(r),o}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Xd(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(r)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.shouldRefreshViews=!1,this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function KC(){return typeof $localize<"u"&&$localize.locale||Gi}var Es=new b("",{providedIn:"root",factory:()=>y(Es,R.Optional|R.SkipSelf)||KC()});var kp=new b(""),Lp=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let o=A2(r?.ngZone,Pp({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{let i=r?.ignoreChangesOutsideZone,s=zv(n.moduleType,this.injector,[...qC({ngZoneFactory:()=>o,ignoreChangesOutsideZone:i}),{provide:Kn,useExisting:XC}]),a=s.injector.get(Dt,null);return o.runOutsideAngular(()=>{let c=o.onError.subscribe({next:l=>{a.handleError(l)}});s.onDestroy(()=>{Ci(this._modules,s),c.unsubscribe()})}),$C(a,o,()=>{let c=s.injector.get(Rp);return c.runInitializers(),c.donePromise.then(()=>{let l=s.injector.get(Es,Gi);return bC(l||Gi),this._moduleDoBootstrap(s),s})})})}bootstrapModule(n,r=[]){let o=Up({},r);return GC(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){let r=n.injector.get(Xt);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(r);else throw new I(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new I(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(kp,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(E(Ue))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),kr=null,Vp=new b("");function YC(e){if(kr&&!kr.get(Vp,!1))throw new I(400,!1);VC(),kr=e;let t=e.get(Lp);return eD(e),t}function bl(e,t,n=[]){let r=`Platform: ${t}`,o=new b(r);return(i=[])=>{let s=jp();if(!s||s.injector.get(Vp,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):YC(QC(a,r))}return JC(o)}}function QC(e=[],t){return Ue.create({name:t,providers:[{provide:Ji,useValue:"platform"},{provide:kp,useValue:new Set([()=>kr=null])},...e]})}function JC(e){let t=jp();if(!t)throw new I(401,!1);return t}function jp(){return kr?.get(Lp)??null}function eD(e){e.get(il,null)?.forEach(n=>n())}var ur=(()=>{class e{static{this.__NG_ELEMENT_ID__=tD}}return e})();function tD(e){return nD(Ne(),X(),(e&16)===16)}function nD(e,t,n){if(ts(e)&&!n){let r=qt(e.index,t);return new gn(r,r)}else if(e.type&175){let r=t[lt];return new gn(r,t)}return null}var Uc=class{constructor(){}supports(t){return _p(t)}create(t){return new Pc(t)}},rD=(e,t)=>t,Pc=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||rD}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<yf(r,o,i)?n:r,a=yf(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let m=0;m<l;m++){let p=m<i.length?i[m]:i[m]=0,v=p+m;u<=v&&v<l&&(i[m]=p+1)}let d=s.previousIndex;i[d]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!_p(t))throw new I(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,Gv(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new kc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Wi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Wi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},kc=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Lc=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Wi=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Lc,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function yf(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function vf(){return new Il([new Uc])}var Il=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:vf})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||vf()),deps:[[e,new jc,new Yi]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new I(901,!1)}}return e})();var Bp=bl(null,"core",[]),$p=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(E(Xt))}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({})}}return e})();var Hp=new b("");function dr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Jr(e,t){ar("NgSignals");let n=td(e);return t?.equal&&(n[Ut].equal=t.equal),n}function Mt(e){let t=$(null);try{return e()}finally{$(t)}}function zp(e){let t=zt(e);if(!t)return null;let n=new Jn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var Yp=null;function wn(){return Yp}function Qp(e){Yp??=e}var Fs=class{};var ye=new b(""),Rl=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(iD),providedIn:"platform"})}}return e})(),Jp=new b(""),iD=(()=>{class e extends Rl{constructor(){super(),this._doc=y(ye),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return wn().getBaseHref(this._doc)}onPopState(n){let r=wn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=wn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function Ul(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function Gp(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function At(e){return e&&e[0]!=="?"?"?"+e:e}var Tt=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(Pl),providedIn:"root"})}}return e})(),eg=new b(""),Pl=(()=>{class e extends Tt{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??y(ye).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return Ul(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+At(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+At(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+At(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(Rl),E(eg,8))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),tg=(()=>{class e extends Tt{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=Ul(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+At(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+At(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(Rl),E(eg,8))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),hr=(()=>{class e{constructor(n){this._subject=new se,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=cD(Gp(Wp(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+At(r))}normalize(n){return e.stripTrailingSlash(aD(this._basePath,Wp(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+At(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+At(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=At}static{this.joinWithSlash=Ul}static{this.stripTrailingSlash=Gp}static{this.\u0275fac=function(r){return new(r||e)(E(Tt))}}static{this.\u0275prov=w({token:e,factory:()=>sD(),providedIn:"root"})}}return e})();function sD(){return new hr(E(Tt))}function aD(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function Wp(e){return e.replace(/\/index.html$/,"")}function cD(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var _e=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(_e||{}),K=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(K||{}),Le=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Le||{}),Kt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function lD(e){return qe(e)[J.LocaleId]}function uD(e,t,n){let r=qe(e),o=[r[J.DayPeriodsFormat],r[J.DayPeriodsStandalone]],i=Ze(o,t);return Ze(i,n)}function dD(e,t,n){let r=qe(e),o=[r[J.DaysFormat],r[J.DaysStandalone]],i=Ze(o,t);return Ze(i,n)}function fD(e,t,n){let r=qe(e),o=[r[J.MonthsFormat],r[J.MonthsStandalone]],i=Ze(o,t);return Ze(i,n)}function hD(e,t){let r=qe(e)[J.Eras];return Ze(r,t)}function bs(e,t){let n=qe(e);return Ze(n[J.DateFormat],t)}function Is(e,t){let n=qe(e);return Ze(n[J.TimeFormat],t)}function _s(e,t){let r=qe(e)[J.DateTimeFormat];return Ze(r,t)}function Os(e,t){let n=qe(e),r=n[J.NumberSymbols][t];if(typeof r>"u"){if(t===Kt.CurrencyDecimal)return n[J.NumberSymbols][Kt.Decimal];if(t===Kt.CurrencyGroup)return n[J.NumberSymbols][Kt.Group]}return r}function ng(e){if(!e[J.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[J.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function pD(e){let t=qe(e);return ng(t),(t[J.ExtraData][2]||[]).map(r=>typeof r=="string"?_l(r):[_l(r[0]),_l(r[1])])}function gD(e,t,n){let r=qe(e);ng(r);let o=[r[J.ExtraData][0],r[J.ExtraData][1]],i=Ze(o,t)||[];return Ze(i,n)||[]}function Ze(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function _l(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var mD=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ms={},yD=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,xt=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(xt||{}),z=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(z||{}),H=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(H||{});function vD(e,t,n,r){let o=SD(e);t=St(n,t)||t;let s=[],a;for(;t;)if(a=yD.exec(t),a){s=s.concat(a.slice(1));let u=s.pop();if(!u)break;t=u}else{s.push(t);break}let c=o.getTimezoneOffset();r&&(c=og(r,c),o=MD(o,r,!0));let l="";return s.forEach(u=>{let d=ID(u);l+=d?d(o,n,c):u==="''"?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),l}function Ns(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function St(e,t){let n=lD(e);if(Ms[n]??={},Ms[n][t])return Ms[n][t];let r="";switch(t){case"shortDate":r=bs(e,Le.Short);break;case"mediumDate":r=bs(e,Le.Medium);break;case"longDate":r=bs(e,Le.Long);break;case"fullDate":r=bs(e,Le.Full);break;case"shortTime":r=Is(e,Le.Short);break;case"mediumTime":r=Is(e,Le.Medium);break;case"longTime":r=Is(e,Le.Long);break;case"fullTime":r=Is(e,Le.Full);break;case"short":let o=St(e,"shortTime"),i=St(e,"shortDate");r=Ss(_s(e,Le.Short),[o,i]);break;case"medium":let s=St(e,"mediumTime"),a=St(e,"mediumDate");r=Ss(_s(e,Le.Medium),[s,a]);break;case"long":let c=St(e,"longTime"),l=St(e,"longDate");r=Ss(_s(e,Le.Long),[c,l]);break;case"full":let u=St(e,"fullTime"),d=St(e,"fullDate");r=Ss(_s(e,Le.Full),[u,d]);break}return r&&(Ms[n][t]=r),r}function Ss(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function nt(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function CD(e,t){return nt(e,3).substring(0,t)}function oe(e,t,n=0,r=!1,o=!1){return function(i,s){let a=DD(e,i);if((n>0||a>-n)&&(a+=n),e===z.Hours)a===0&&n===-12&&(a=12);else if(e===z.FractionalSeconds)return CD(a,t);let c=Os(s,Kt.MinusSign);return nt(a,t,c,r,o)}}function DD(e,t){switch(e){case z.FullYear:return t.getFullYear();case z.Month:return t.getMonth();case z.Date:return t.getDate();case z.Hours:return t.getHours();case z.Minutes:return t.getMinutes();case z.Seconds:return t.getSeconds();case z.FractionalSeconds:return t.getMilliseconds();case z.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function Y(e,t,n=_e.Format,r=!1){return function(o,i){return wD(o,i,e,t,n,r)}}function wD(e,t,n,r,o,i){switch(n){case H.Months:return fD(t,o,r)[e.getMonth()];case H.Days:return dD(t,o,r)[e.getDay()];case H.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(i){let l=pD(t),u=gD(t,o,r),d=l.findIndex(m=>{if(Array.isArray(m)){let[p,v]=m,D=s>=p.hours&&a>=p.minutes,_=s<v.hours||s===v.hours&&a<v.minutes;if(p.hours<v.hours){if(D&&_)return!0}else if(D||_)return!0}else if(m.hours===s&&m.minutes===a)return!0;return!1});if(d!==-1)return u[d]}return uD(t,o,r)[s<12?0:1];case H.Eras:return hD(t,r)[e.getFullYear()<=0?0:1];default:let c=n;throw new Error(`unexpected translation type ${c}`)}}function As(e){return function(t,n,r){let o=-1*r,i=Os(n,Kt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case xt.Short:return(o>=0?"+":"")+nt(s,2,i)+nt(Math.abs(o%60),2,i);case xt.ShortGMT:return"GMT"+(o>=0?"+":"")+nt(s,1,i);case xt.Long:return"GMT"+(o>=0?"+":"")+nt(s,2,i)+":"+nt(Math.abs(o%60),2,i);case xt.Extended:return r===0?"Z":(o>=0?"+":"")+nt(s,2,i)+":"+nt(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var ED=0,Ts=4;function bD(e){let t=Ns(e,ED,1).getDay();return Ns(e,0,1+(t<=Ts?Ts:Ts+7)-t)}function rg(e){let t=e.getDay(),n=t===0?-3:Ts-t;return Ns(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Ml(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=rg(n),s=bD(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return nt(o,e,Os(r,Kt.MinusSign))}}function xs(e,t=!1){return function(n,r){let i=rg(n).getFullYear();return nt(i,e,Os(r,Kt.MinusSign),t)}}var Sl={};function ID(e){if(Sl[e])return Sl[e];let t;switch(e){case"G":case"GG":case"GGG":t=Y(H.Eras,K.Abbreviated);break;case"GGGG":t=Y(H.Eras,K.Wide);break;case"GGGGG":t=Y(H.Eras,K.Narrow);break;case"y":t=oe(z.FullYear,1,0,!1,!0);break;case"yy":t=oe(z.FullYear,2,0,!0,!0);break;case"yyy":t=oe(z.FullYear,3,0,!1,!0);break;case"yyyy":t=oe(z.FullYear,4,0,!1,!0);break;case"Y":t=xs(1);break;case"YY":t=xs(2,!0);break;case"YYY":t=xs(3);break;case"YYYY":t=xs(4);break;case"M":case"L":t=oe(z.Month,1,1);break;case"MM":case"LL":t=oe(z.Month,2,1);break;case"MMM":t=Y(H.Months,K.Abbreviated);break;case"MMMM":t=Y(H.Months,K.Wide);break;case"MMMMM":t=Y(H.Months,K.Narrow);break;case"LLL":t=Y(H.Months,K.Abbreviated,_e.Standalone);break;case"LLLL":t=Y(H.Months,K.Wide,_e.Standalone);break;case"LLLLL":t=Y(H.Months,K.Narrow,_e.Standalone);break;case"w":t=Ml(1);break;case"ww":t=Ml(2);break;case"W":t=Ml(1,!0);break;case"d":t=oe(z.Date,1);break;case"dd":t=oe(z.Date,2);break;case"c":case"cc":t=oe(z.Day,1);break;case"ccc":t=Y(H.Days,K.Abbreviated,_e.Standalone);break;case"cccc":t=Y(H.Days,K.Wide,_e.Standalone);break;case"ccccc":t=Y(H.Days,K.Narrow,_e.Standalone);break;case"cccccc":t=Y(H.Days,K.Short,_e.Standalone);break;case"E":case"EE":case"EEE":t=Y(H.Days,K.Abbreviated);break;case"EEEE":t=Y(H.Days,K.Wide);break;case"EEEEE":t=Y(H.Days,K.Narrow);break;case"EEEEEE":t=Y(H.Days,K.Short);break;case"a":case"aa":case"aaa":t=Y(H.DayPeriods,K.Abbreviated);break;case"aaaa":t=Y(H.DayPeriods,K.Wide);break;case"aaaaa":t=Y(H.DayPeriods,K.Narrow);break;case"b":case"bb":case"bbb":t=Y(H.DayPeriods,K.Abbreviated,_e.Standalone,!0);break;case"bbbb":t=Y(H.DayPeriods,K.Wide,_e.Standalone,!0);break;case"bbbbb":t=Y(H.DayPeriods,K.Narrow,_e.Standalone,!0);break;case"B":case"BB":case"BBB":t=Y(H.DayPeriods,K.Abbreviated,_e.Format,!0);break;case"BBBB":t=Y(H.DayPeriods,K.Wide,_e.Format,!0);break;case"BBBBB":t=Y(H.DayPeriods,K.Narrow,_e.Format,!0);break;case"h":t=oe(z.Hours,1,-12);break;case"hh":t=oe(z.Hours,2,-12);break;case"H":t=oe(z.Hours,1);break;case"HH":t=oe(z.Hours,2);break;case"m":t=oe(z.Minutes,1);break;case"mm":t=oe(z.Minutes,2);break;case"s":t=oe(z.Seconds,1);break;case"ss":t=oe(z.Seconds,2);break;case"S":t=oe(z.FractionalSeconds,1);break;case"SS":t=oe(z.FractionalSeconds,2);break;case"SSS":t=oe(z.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=As(xt.Short);break;case"ZZZZZ":t=As(xt.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=As(xt.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=As(xt.Long);break;default:return null}return Sl[e]=t,t}function og(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function _D(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function MD(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=og(t,o);return _D(e,r*(i-o))}function SD(e){if(qp(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ns(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(mD))return AD(r)}let t=new Date(e);if(!qp(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function AD(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,c=Number(e[6]||0),l=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,c,l),t}function qp(e){return e instanceof Date&&!isNaN(e.valueOf())}function Rs(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var Al=/\s+/,Zp=[],ig=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=Zp,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(Al):Zp}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(Al):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(Al).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static{this.\u0275fac=function(r){return new(r||e)(j(vn),j(sr))}}static{this.\u0275dir=et({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var xl=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},pr=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new xl(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Xp(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Xp(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(j(cr),j(ds),j(Il))}}static{this.\u0275dir=et({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function Xp(e,t){e.context.$implicit=t.item}var Us=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Tl,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){Kp("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){Kp("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(j(cr),j(ds))}}static{this.\u0275dir=et({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Tl=class{constructor(){this.$implicit=null,this.ngIf=null}};function Kp(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${Ee(t)}'.`)}function xD(e,t){return new I(2100,!1)}var TD="mediumDate",FD=new b(""),ND=new b(""),sg=(()=>{class e{constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??TD,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return vD(n,s,i||this.locale,a)}catch(s){throw xD(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(j(Es,16),j(FD,24),j(ND,24))}}static{this.\u0275pipe=Uf({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var ag=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({})}}return e})(),kl="browser",OD="server";function RD(e){return e===kl}function Ps(e){return e===OD}var cg=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>RD(y(ft))?new Fl(y(ye),window):new Nl})}}return e})(),Fl=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=UD(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function UD(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Nl=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},fr=class{};var to=class{},Ls=class{},Ft=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var Vl=class{encodeKey(t){return lg(t)}encodeValue(t){return lg(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function PD(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var kD=/%(\d[a-f0-9])/gi,LD={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function lg(e){return encodeURIComponent(e).replace(kD,(t,n)=>LD[n]??t)}function ks(e){return`${e}`}var Qt=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new Vl,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=PD(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(ks):[ks(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ks(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(ks(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var jl=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function VD(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function ug(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function dg(e){return typeof Blob<"u"&&e instanceof Blob}function fg(e){return typeof FormData<"u"&&e instanceof FormData}function jD(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var eo=class e{constructor(t,n,r,o){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let i;if(VD(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new Ft,this.context??=new jl,!this.params)this.params=new Qt,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||ug(this.body)||dg(this.body)||fg(this.body)||jD(this.body)?this.body:this.body instanceof Qt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||fg(this.body)?null:dg(this.body)?this.body.type||null:ug(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof Qt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((m,p)=>m.set(p,t.setHeaders[p]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((m,p)=>m.set(p,t.setParams[p]),u)),new e(n,r,s,{params:u,headers:l,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Jt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Jt||{}),no=class{constructor(t,n=200,r="OK"){this.headers=t.headers||new Ft,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},Vs=class e extends no{constructor(t={}){super(t),this.type=Jt.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},ro=class e extends no{constructor(t={}){super(t),this.type=Jt.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Yt=class extends no{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},yg=200,BD=204;function Ll(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var Nt=(()=>{class e{constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof eo)i=n;else{let c;o.headers instanceof Ft?c=o.headers:c=new Ft(o.headers);let l;o.params&&(o.params instanceof Qt?l=o.params:l=new Qt({fromObject:o.params})),i=new eo(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=M(i).pipe(vt(c=>this.handler.handle(c)));if(n instanceof eo||o.observe==="events")return s;let a=s.pipe(Ae(c=>c instanceof ro));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return a.pipe(T(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return a.pipe(T(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return a.pipe(T(c=>c.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Qt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Ll(o,r))}post(n,r,o={}){return this.request("POST",n,Ll(o,r))}put(n,r,o={}){return this.request("PUT",n,Ll(o,r))}static{this.\u0275fac=function(r){return new(r||e)(E(to))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),$D=/^\)\]\}',?\n/,HD="X-Request-URL";function hg(e){if(e.url)return e.url;let t=HD.toLocaleLowerCase();return e.headers.get(t)}var zD=(()=>{class e{constructor(){this.fetchImpl=y(Bl,{optional:!0})?.fetch??fetch.bind(globalThis),this.ngZone=y(W)}handle(n){return new L(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then($l,i=>r.error(new Yt({error:i}))),()=>o.abort()})}doRequest(n,r,o){return Uo(this,null,function*(){let i=this.createRequestInit(n),s;try{let p=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,C({signal:r},i)));GD(p),o.next({type:Jt.Sent}),s=yield p}catch(p){o.error(new Yt({error:p,status:p.status??0,statusText:p.statusText,url:n.urlWithParams,headers:p.headers}));return}let a=new Ft(s.headers),c=s.statusText,l=hg(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&o.next(new Vs({headers:a,status:u,statusText:c,url:l})),s.body){let p=s.headers.get("content-length"),v=[],D=s.body.getReader(),_=0,F,ve,U=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>Uo(this,null,function*(){for(;;){let{done:Me,value:de}=yield D.read();if(Me)break;if(v.push(de),_+=de.length,n.reportProgress){ve=n.responseType==="text"?(ve??"")+(F??=new TextDecoder).decode(de,{stream:!0}):void 0;let mt=()=>o.next({type:Jt.DownloadProgress,total:p?+p:void 0,loaded:_,partialText:ve});U?U.run(mt):mt()}}}));let Ce=this.concatChunks(v,_);try{let Me=s.headers.get("Content-Type")??"";d=this.parseBody(n,Ce,Me)}catch(Me){o.error(new Yt({error:Me,headers:new Ft(s.headers),status:s.status,statusText:s.statusText,url:hg(s)??n.urlWithParams}));return}}u===0&&(u=d?yg:0),u>=200&&u<300?(o.next(new ro({body:d,headers:a,status:u,statusText:c,url:l})),o.complete()):o.error(new Yt({error:d,headers:a,status:u,statusText:c,url:l}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace($D,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!n.headers.has("Content-Type")){let i=n.detectContentTypeHeader();i!==null&&(r["Content-Type"]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Bl=class{};function $l(){}function GD(e){e.then($l,$l)}function vg(e,t){return t(e)}function WD(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function qD(e,t,n){return(r,o)=>Pe(n,()=>t(r,i=>e(i,o)))}var ZD=new b(""),Hl=new b(""),XD=new b(""),Cg=new b("",{providedIn:"root",factory:()=>!0});function KD(){let e=null;return(t,n)=>{e===null&&(e=(y(ZD,{optional:!0})??[]).reduceRight(WD,vg));let r=y(It);if(y(Cg)){let i=r.add();return e(t,n).pipe(Vt(()=>r.remove(i)))}else return e(t,n)}}var pg=(()=>{class e extends to{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=y(It),this.contributeToStability=y(Cg)}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Hl),...this.injector.get(XD,[])]));this.chain=r.reduceRight((o,i)=>qD(o,i,this.injector),vg)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Vt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(E(Ls),E(be))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();var YD=/^\)\]\}',?\n/;function QD(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var gg=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new I(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?Q(r.\u0275loadImpl()):M(null)).pipe(xe(()=>new L(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((D,_)=>s.setRequestHeader(D,_.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let D=n.detectContentTypeHeader();D!==null&&s.setRequestHeader("Content-Type",D)}if(n.responseType){let D=n.responseType.toLowerCase();s.responseType=D!=="json"?D:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let D=s.statusText||"OK",_=new Ft(s.getAllResponseHeaders()),F=QD(s)||n.url;return c=new Vs({headers:_,status:s.status,statusText:D,url:F}),c},u=()=>{let{headers:D,status:_,statusText:F,url:ve}=l(),U=null;_!==BD&&(U=typeof s.response>"u"?s.responseText:s.response),_===0&&(_=U?yg:0);let Ce=_>=200&&_<300;if(n.responseType==="json"&&typeof U=="string"){let Me=U;U=U.replace(YD,"");try{U=U!==""?JSON.parse(U):null}catch(de){U=Me,Ce&&(Ce=!1,U={error:de,text:U})}}Ce?(i.next(new ro({body:U,headers:D,status:_,statusText:F,url:ve||void 0})),i.complete()):i.error(new Yt({error:U,headers:D,status:_,statusText:F,url:ve||void 0}))},d=D=>{let{url:_}=l(),F=new Yt({error:D,status:s.status||0,statusText:s.statusText||"Unknown Error",url:_||void 0});i.error(F)},m=!1,p=D=>{m||(i.next(l()),m=!0);let _={type:Jt.DownloadProgress,loaded:D.loaded};D.lengthComputable&&(_.total=D.total),n.responseType==="text"&&s.responseText&&(_.partialText=s.responseText),i.next(_)},v=D=>{let _={type:Jt.UploadProgress,loaded:D.loaded};D.lengthComputable&&(_.total=D.total),i.next(_)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",p),a!==null&&s.upload&&s.upload.addEventListener("progress",v)),s.send(a),i.next({type:Jt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",p),a!==null&&s.upload&&s.upload.removeEventListener("progress",v)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(E(fr))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Dg=new b(""),JD="XSRF-TOKEN",ew=new b("",{providedIn:"root",factory:()=>JD}),tw="X-XSRF-TOKEN",nw=new b("",{providedIn:"root",factory:()=>tw}),js=class{},rw=(()=>{class e{constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=Rs(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(E(ye),E(ft),E(ew))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function ow(e,t){let n=e.url.toLowerCase();if(!y(Dg)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=y(js).getToken(),o=y(nw);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var wg=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(wg||{});function iw(e,t){return{\u0275kind:e,\u0275providers:t}}function sw(...e){let t=[Nt,gg,pg,{provide:to,useExisting:pg},{provide:Ls,useFactory:()=>y(zD,{optional:!0})??y(gg)},{provide:Hl,useValue:ow,multi:!0},{provide:Dg,useValue:!0},{provide:js,useClass:rw}];for(let n of e)t.push(...n.\u0275providers);return Qi(t)}var mg=new b("");function aw(){return iw(wg.LegacyInterceptors,[{provide:mg,useFactory:KD},{provide:Hl,useExisting:mg,multi:!0}])}var Eg=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({providers:[sw(aw())]})}}return e})();var Wl=class extends Fs{constructor(){super(...arguments),this.supportsDOMEvents=!0}},ql=class e extends Wl{static makeCurrent(){Qp(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=cw();return n==null?null:lw(n)}resetBaseElement(){oo=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return Rs(document.cookie,t)}},oo=null;function cw(){return oo=oo||document.querySelector("base"),oo?oo.getAttribute("href"):null}function lw(e){return new URL(e,document.baseURI).pathname}var Zl=class{addToWindow(t){De.getAngularTestability=(r,o=!0)=>{let i=t.findTestabilityInTree(r,o);if(i==null)throw new I(5103,!1);return i},De.getAllAngularTestabilities=()=>t.getAllTestabilities(),De.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let o=De.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};De.frameworkStabilizers||(De.frameworkStabilizers=[]),De.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let o=t.getTestability(n);return o??(r?wn().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},uw=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),$s=new b(""),Mg=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o){return this._findPluginFor(r).addEventListener(n,r,o)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new I(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(E($s),E(W))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),io=class{constructor(t){this._doc=t}},zl="ng-app-id",Sg=(()=>{class e{constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=Ps(i),this.resetHostNodes()}addStyles(n){for(let r of n)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(n){for(let r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){let r=this.styleRef;r.get(n)?.elements?.forEach(o=>o.remove()),r.delete(n)}collectServerRenderedStyles(){let n=this.doc.head?.querySelectorAll(`style[${zl}="${this.appId}"]`);if(n?.length){let r=new Map;return n.forEach(o=>{o.textContent!=null&&r.set(o.textContent,o)}),r}return null}changeUsageCount(n,r){let o=this.styleRef;if(o.has(n)){let i=o.get(n);return i.usage+=r,i.usage}return o.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){let o=this.styleNodesInDOM,i=o?.get(r);if(i?.parentNode===n)return o.delete(r),i.removeAttribute(zl),i;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(zl,this.appId),n.appendChild(s),s}}addStyleToHost(n,r){let o=this.getStyleElement(n,r),i=this.styleRef,s=i.get(r)?.elements;s?s.push(o):i.set(r,{elements:[o],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(E(ye),E(is),E(sl,8),E(ft))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Gl={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Kl=/%COMP%/g,Ag="%COMP%",dw=`_nghost-${Ag}`,fw=`_ngcontent-${Ag}`,hw=!0,pw=new b("",{providedIn:"root",factory:()=>hw});function gw(e){return fw.replace(Kl,e)}function mw(e){return dw.replace(Kl,e)}function xg(e,t){return t.map(n=>n.replace(Kl,e))}var bg=(()=>{class e{constructor(n,r,o,i,s,a,c,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=Ps(a),this.defaultRenderer=new so(n,s,c,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===at.ShadowDom&&(r=B(C({},r),{encapsulation:at.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof Hs?o.applyToHost(n):o instanceof ao&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case at.Emulated:i=new Hs(c,l,r,this.appId,u,s,a,d);break;case at.ShadowDom:return new Xl(c,l,n,r,s,a,this.nonce,d);default:i=new ao(c,l,r,u,s,a,d);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(E(Mg),E(Sg),E(is),E(pw),E(ye),E(ft),E(W),E(sl))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),so=class{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(Gl[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Ig(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Ig(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new I(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=Gl[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=Gl[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(wt.DashCase|wt.Important)?t.style.setProperty(n,r,o&wt.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&wt.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=wn().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Ig(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var Xl=class extends so{constructor(t,n,r,o,i,s,a,c){super(t,i,s,c),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=xg(o.id,o.styles);for(let u of l){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},ao=class extends so{constructor(t,n,r,o,i,s,a,c){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=c?xg(c,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Hs=class extends ao{constructor(t,n,r,o,i,s,a,c){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,l),this.contentAttr=gw(l),this.hostAttr=mw(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},yw=(()=>{class e extends io{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o){return n.addEventListener(r,o,!1),()=>this.removeEventListener(n,r,o)}removeEventListener(n,r,o){return n.removeEventListener(r,o)}static{this.\u0275fac=function(r){return new(r||e)(E(ye))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),vw=(()=>{class e extends io{constructor(n){super(n),this.delegate=y(Hp,{optional:!0})}supports(n){return this.delegate?this.delegate.supports(n):!1}addEventListener(n,r,o){return this.delegate.addEventListener(n,r,o)}removeEventListener(n,r,o){return this.delegate.removeEventListener(n,r,o)}static{this.\u0275fac=function(r){return new(r||e)(E(ye))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),_g=["alt","control","meta","shift"],Cw={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Dw={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},ww=(()=>{class e extends io{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o){let i=e.parseEventName(r),s=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>wn().onAndCancel(n,i.domEventName,s))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),_g.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=Cw[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),_g.forEach(s=>{if(s!==o){let a=Dw[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(E(ye))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function Ew(){ql.makeCurrent()}function bw(){return new Dt}function Iw(){return Vh(document),document}var _w=[{provide:ft,useValue:kl},{provide:il,useValue:Ew,multi:!0},{provide:ye,useFactory:Iw,deps:[]}],Tg=bl(Bp,"browser",_w),Mw=new b(""),Sw=[{provide:Qr,useClass:Zl,deps:[]},{provide:wl,useClass:ys,deps:[W,vs,Qr]},{provide:ys,useClass:ys,deps:[W,vs,Qr]}],Aw=[{provide:Ji,useValue:"root"},{provide:Dt,useFactory:bw,deps:[]},{provide:$s,useClass:yw,multi:!0,deps:[ye,W,ft]},{provide:$s,useClass:ww,multi:!0,deps:[ye]},{provide:$s,useClass:vw,multi:!0},bg,Sg,Mg,{provide:Qn,useExisting:bg},{provide:fr,useClass:uw,deps:[]},[]],Fg=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:is,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(E(Mw,12))}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({providers:[...Aw,...Sw],imports:[ag,$p]})}}return e})();var Ng=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(E(ye))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var xw=new b(""),Tw=new b("");function Lg(e){return e!=null}function Vg(e){return Dn(e)?Q(e):e}function jg(e){let t={};return e.forEach(n=>{t=n!=null?C(C({},t),n):t}),Object.keys(t).length===0?null:t}function Bg(e,t){return t.map(n=>n(e))}function Fw(e){return!e.validate}function $g(e){return e.map(t=>Fw(t)?t:n=>t.validate(n))}function Nw(e){if(!e)return null;let t=e.filter(Lg);return t.length==0?null:function(n){return jg(Bg(n,t))}}function tu(e){return e!=null?Nw($g(e)):null}function Ow(e){if(!e)return null;let t=e.filter(Lg);return t.length==0?null:function(n){let r=Bg(n,t).map(Vg);return Oa(r).pipe(T(jg))}}function nu(e){return e!=null?Ow($g(e)):null}function Rg(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function Rw(e){return e._rawValidators}function Uw(e){return e._rawAsyncValidators}function Yl(e){return e?Array.isArray(e)?e:[e]:[]}function Gs(e,t){return Array.isArray(e)?e.includes(t):e===t}function Ug(e,t){let n=Yl(t);return Yl(e).forEach(o=>{Gs(n,o)||n.push(o)}),n}function Pg(e,t){return Yl(t).filter(n=>!Gs(e,n))}var Ql=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=tu(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=nu(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},po=class extends Ql{get formDirective(){return null}get path(){return null}};var Jl=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},Pw={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},b8=B(C({},Pw),{"[class.ng-submitted]":"isSubmitted"});var Hg=(()=>{class e extends Jl{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)(j(po,10))}}static{this.\u0275dir=et({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Dl("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},features:[fs]})}}return e})();var co="VALID",zs="INVALID",mr="PENDING",lo="DISABLED",vr=class{},Ws=class extends vr{constructor(t,n){super(),this.value=t,this.source=n}},fo=class extends vr{constructor(t,n){super(),this.pristine=t,this.source=n}},ho=class extends vr{constructor(t,n){super(),this.touched=t,this.source=n}},yr=class extends vr{constructor(t,n){super(),this.status=t,this.source=n}};function kw(e){return(ru(e)?e.validators:e)||null}function Lw(e){return Array.isArray(e)?tu(e):e||null}function Vw(e,t){return(ru(t)?t.asyncValidators:e)||null}function jw(e){return Array.isArray(e)?nu(e):e||null}function ru(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function Bw(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new I(1e3,"");if(!r[n])throw new I(1001,"")}function $w(e,t,n){e._forEachChild((r,o)=>{if(n[o]===void 0)throw new I(1002,"")})}var eu=class{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Jr(()=>this.statusReactive()),this.statusReactive=Xr(void 0),this._pristine=Jr(()=>this.pristineReactive()),this.pristineReactive=Xr(!0),this._touched=Jr(()=>this.touchedReactive()),this.touchedReactive=Xr(!1),this._events=new ce,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return Mt(this.statusReactive)}set status(t){Mt(()=>this.statusReactive.set(t))}get valid(){return this.status===co}get invalid(){return this.status===zs}get pending(){return this.status==mr}get disabled(){return this.status===lo}get enabled(){return this.status!==lo}get pristine(){return Mt(this.pristineReactive)}set pristine(t){Mt(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return Mt(this.touchedReactive)}set touched(t){Mt(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Ug(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Ug(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Pg(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Pg(t,this._rawAsyncValidators))}hasValidator(t){return Gs(this._rawValidators,t)}hasAsyncValidator(t){return Gs(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(B(C({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new ho(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new ho(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(B(C({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new fo(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new fo(!0,r))}markAsPending(t={}){this.status=mr;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new yr(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(B(C({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=lo,this.errors=null,this._forEachChild(o=>{o.disable(B(C({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ws(this.value,r)),this._events.next(new yr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(B(C({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=co,this._forEachChild(r=>{r.enable(B(C({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(B(C({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===co||this.status===mr)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Ws(this.value,n)),this._events.next(new yr(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(B(C({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?lo:co}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=mr,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=Vg(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new yr(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new se,this.statusChanges=new se}_calculateStatus(){return this._allControlsDisabled()?lo:this.errors?zs:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(mr)?mr:this._anyControlsHaveStatus(zs)?zs:co}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new fo(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new ho(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){ru(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=Lw(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=jw(this._rawAsyncValidators)}},qs=class extends eu{constructor(t,n,r){super(kw(n),Vw(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){$w(this,!0,t),Object.keys(t).forEach(r=>{Bw(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let o=this.controls[r];o&&o.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,o)=>{r.reset(t?t[o]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,o)=>((r.enabled||this.disabled)&&(n[o]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((o,i)=>{r=n(r,o,i)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var ou=new b("CallSetDisabledState",{providedIn:"root",factory:()=>Zs}),Zs="always";function Hw(e,t,n=Zs){zg(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),Gw(e,t),qw(e,t),Ww(e,t),zw(e,t)}function kg(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function zw(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function zg(e,t){let n=Rw(e);t.validator!==null?e.setValidators(Rg(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=Uw(e);t.asyncValidator!==null?e.setAsyncValidators(Rg(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();kg(t._rawValidators,o),kg(t._rawAsyncValidators,o)}function Gw(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Gg(e,t)})}function Ww(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Gg(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function Gg(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function qw(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function Zw(e,t){e==null,zg(e,t)}function Xw(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}var Kw={provide:po,useExisting:Zi(()=>iu)},uo=Promise.resolve(),iu=(()=>{class e extends po{get submitted(){return Mt(this.submittedReactive)}constructor(n,r,o){super(),this.callSetDisabledState=o,this._submitted=Jr(()=>this.submittedReactive()),this.submittedReactive=Xr(!1),this._directives=new Set,this.ngSubmit=new se,this.form=new qs({},tu(n),nu(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){uo.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),Hw(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){uo.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){uo.then(()=>{let r=this._findContainer(n.path),o=new qs({});Zw(o,n),r.registerControl(n.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){uo.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){uo.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),Xw(this.form,this._directives),this.ngSubmit.emit(n),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static{this.\u0275fac=function(r){return new(r||e)(j(xw,10),j(Tw,10),j(ou,8))}}static{this.\u0275dir=et({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&ht("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[xp([Kw]),fs]})}}return e})();var Wg=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=et({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();var Yw=new b("");var qg=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({})}}return e})();var Zg=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:ou,useValue:n.callSetDisabledState??Zs}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({imports:[qg]})}}return e})(),Xg=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Yw,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:ou,useValue:n.callSetDisabledState??Zs}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({imports:[qg]})}}return e})();var N="primary",Ao=Symbol("RouteTitle"),uu=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function Ir(e){return new uu(e)}function Jw(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function e3(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!pt(e[n],t[n]))return!1;return!0}function pt(e,t){let n=e?du(e):void 0,r=t?du(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!sm(e[o],t[o]))return!1;return!0}function du(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function sm(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function am(e){return e.length>0?e[e.length-1]:null}function nn(e){return Na(e)?e:Dn(e)?Q(Promise.resolve(e)):M(e)}var t3={exact:lm,subset:um},cm={exact:n3,subset:r3,ignored:()=>!0};function Kg(e,t,n){return t3[n.paths](e.root,t.root,n.matrixParams)&&cm[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function n3(e,t){return pt(e,t)}function lm(e,t,n){if(!bn(e.segments,t.segments)||!Ys(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!lm(e.children[r],t.children[r],n))return!1;return!0}function r3(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>sm(e[n],t[n]))}function um(e,t,n){return dm(e,t,t.segments,n)}function dm(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!bn(o,n)||t.hasChildren()||!Ys(o,n,r))}else if(e.segments.length===n.length){if(!bn(e.segments,n)||!Ys(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!um(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!bn(e.segments,o)||!Ys(e.segments,o,r)||!e.children[N]?!1:dm(e.children[N],t,i,r)}}function Ys(e,t,n){return t.every((r,o)=>cm[n](e[o].parameters,r.parameters))}var Rt=class{constructor(t=new G([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Ir(this.queryParams),this._queryParamMap}toString(){return s3.serialize(this)}},G=class{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Qs(this)}},En=class{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=Ir(this.parameters),this._parameterMap}toString(){return hm(this)}};function o3(e,t){return bn(e,t)&&e.every((n,r)=>pt(n.parameters,t[r].parameters))}function bn(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function i3(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===N&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==N&&(n=n.concat(t(o,r)))}),n}var xo=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>new _r,providedIn:"root"})}}return e})(),_r=class{parse(t){let n=new hu(t);return new Rt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${go(t.root,!0)}`,r=l3(t.queryParams),o=typeof t.fragment=="string"?`#${a3(t.fragment)}`:"";return`${n}${r}${o}`}},s3=new _r;function Qs(e){return e.segments.map(t=>hm(t)).join("/")}function go(e,t){if(!e.hasChildren())return Qs(e);if(t){let n=e.children[N]?go(e.children[N],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==N&&r.push(`${o}:${go(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=i3(e,(r,o)=>o===N?[go(e.children[N],!1)]:[`${o}:${go(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[N]!=null?`${Qs(e)}/${n[0]}`:`${Qs(e)}/(${n.join("//")})`}}function fm(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Xs(e){return fm(e).replace(/%3B/gi,";")}function a3(e){return encodeURI(e)}function fu(e){return fm(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Js(e){return decodeURIComponent(e)}function Yg(e){return Js(e.replace(/\+/g,"%20"))}function hm(e){return`${fu(e.path)}${c3(e.parameters)}`}function c3(e){return Object.entries(e).map(([t,n])=>`;${fu(t)}=${fu(n)}`).join("")}function l3(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${Xs(n)}=${Xs(o)}`).join("&"):`${Xs(n)}=${Xs(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var u3=/^[^\/()?;#]+/;function su(e){let t=e.match(u3);return t?t[0]:""}var d3=/^[^\/()?;=#]+/;function f3(e){let t=e.match(d3);return t?t[0]:""}var h3=/^[^=?&#]+/;function p3(e){let t=e.match(h3);return t?t[0]:""}var g3=/^[^&#]+/;function m3(e){let t=e.match(g3);return t?t[0]:""}var hu=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new G([],{}):new G([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[N]=new G(t,n)),r}parseSegment(){let t=su(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new I(4009,!1);return this.capture(t),new En(Js(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=f3(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=su(this.remaining);o&&(r=o,this.capture(r))}t[Js(n)]=Js(r)}parseQueryParam(t){let n=p3(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=m3(this.remaining);s&&(r=s,this.capture(r))}let o=Yg(n),i=Yg(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=su(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new I(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=N);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[N]:new G([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new I(4011,!1)}};function pm(e){return e.segments.length>0?new G([],{[N]:e}):e}function gm(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=gm(o);if(r===N&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new G(e.segments,t);return y3(n)}function y3(e){if(e.numberOfChildren===1&&e.children[N]){let t=e.children[N];return new G(e.segments.concat(t.segments),t.children)}return e}function In(e){return e instanceof Rt}function v3(e,t,n=null,r=null){let o=mm(e);return ym(o,t,n,r)}function mm(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new G(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=pm(r);return t??o}function ym(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return au(o,o,o,n,r);let i=C3(t);if(i.toRoot())return au(o,o,new G([],{}),n,r);let s=D3(i,o,e),a=s.processChildren?vo(s.segmentGroup,s.index,i.commands):Cm(s.segmentGroup,s.index,i.commands);return au(o,s.segmentGroup,a,n,r)}function ea(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function wo(e){return typeof e=="object"&&e!=null&&e.outlets}function au(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=vm(e,t,n);let a=pm(gm(s));return new Rt(a,i,o)}function vm(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=vm(i,t,n)}),new G(e.segments,r)}var ta=class{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&ea(r[0]))throw new I(4003,!1);let o=r.find(wo);if(o&&o!==am(r))throw new I(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function C3(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new ta(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new ta(n,t,r)}var wr=class{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function D3(e,t,n){if(e.isAbsolute)return new wr(t,!0,0);if(!n)return new wr(t,!1,NaN);if(n.parent===null)return new wr(n,!0,0);let r=ea(e.commands[0])?0:1,o=n.segments.length-1+r;return w3(n,o,e.numberOfDoubleDots)}function w3(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new I(4005,!1);o=r.segments.length}return new wr(r,!1,o-i)}function E3(e){return wo(e[0])?e[0].outlets:{[N]:e}}function Cm(e,t,n){if(e??=new G([],{}),e.segments.length===0&&e.hasChildren())return vo(e,t,n);let r=b3(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new G(e.segments.slice(0,r.pathIndex),{});return i.children[N]=new G(e.segments.slice(r.pathIndex),e.children),vo(i,0,o)}else return r.match&&o.length===0?new G(e.segments,{}):r.match&&!e.hasChildren()?pu(e,t,n):r.match?vo(e,0,o):pu(e,t,n)}function vo(e,t,n){if(n.length===0)return new G(e.segments,{});{let r=E3(n),o={};if(Object.keys(r).some(i=>i!==N)&&e.children[N]&&e.numberOfChildren===1&&e.children[N].segments.length===0){let i=vo(e.children[N],t,n);return new G(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Cm(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new G(e.segments,o)}}function b3(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(wo(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!Jg(c,l,s))return i;r+=2}else{if(!Jg(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function pu(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(wo(i)){let c=I3(i.outlets);return new G(r,c)}if(o===0&&ea(n[0])){let c=e.segments[t];r.push(new En(c.path,Qg(n[0]))),o++;continue}let s=wo(i)?i.outlets[N]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&ea(a)?(r.push(new En(s,Qg(a))),o+=2):(r.push(new En(s,{})),o++)}return new G(r,{})}function I3(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=pu(new G([],{}),0,r))}),t}function Qg(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function Jg(e,t,n){return e==n.path&&pt(t,n.parameters)}var Co="imperative",ue=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ue||{}),Xe=class{constructor(t,n){this.id=t,this.url=n}},Mr=class extends Xe{constructor(t,n,r="imperative",o=null){super(t,n),this.type=ue.NavigationStart,this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},gt=class extends Xe{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=ue.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},je=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(je||{}),na=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(na||{}),Ot=class extends Xe{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=ue.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},en=class extends Xe{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=ue.NavigationSkipped}},Eo=class extends Xe{constructor(t,n,r,o){super(t,n),this.error=r,this.target=o,this.type=ue.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ra=class extends Xe{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=ue.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},gu=class extends Xe{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=ue.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},mu=class extends Xe{constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i,this.type=ue.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},yu=class extends Xe{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=ue.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},vu=class extends Xe{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=ue.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Cu=class{constructor(t){this.route=t,this.type=ue.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Du=class{constructor(t){this.route=t,this.type=ue.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},wu=class{constructor(t){this.snapshot=t,this.type=ue.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Eu=class{constructor(t){this.snapshot=t,this.type=ue.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},bu=class{constructor(t){this.snapshot=t,this.type=ue.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Iu=class{constructor(t){this.snapshot=t,this.type=ue.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},oa=class{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=ue.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},bo=class{},Sr=class{constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function _3(e,t){return e.providers&&!e._injector&&(e._injector=hs(e.providers,t,`Route: ${e.path}`)),e._injector??t}function rt(e){return e.outlet||N}function M3(e,t){let n=e.filter(r=>rt(r)===t);return n.push(...e.filter(r=>rt(r)!==t)),n}function To(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var _u=class{get injector(){return To(this.route?.snapshot)??this.rootInjector}set injector(t){}constructor(t){this.rootInjector=t,this.outlet=null,this.route=null,this.children=new Fo(this.rootInjector),this.attachRef=null}},Fo=(()=>{class e{constructor(n){this.rootInjector=n,this.contexts=new Map}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new _u(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)(E(be))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),ia=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Mu(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Mu(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=Su(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Su(t,this._root).map(n=>n.value)}};function Mu(e,t){if(e===t.value)return t;for(let n of t.children){let r=Mu(e,n);if(r)return r}return null}function Su(e,t){if(e===t.value)return[t];for(let n of t.children){let r=Su(e,n);if(r.length)return r.unshift(t),r}return[]}var Ve=class{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function Dr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var sa=class extends ia{constructor(t,n){super(t),this.snapshot=n,Pu(this,t)}toString(){return this.snapshot.toString()}};function Dm(e){let t=S3(e),n=new fe([new En("",{})]),r=new fe({}),o=new fe({}),i=new fe({}),s=new fe(""),a=new _n(n,r,i,s,o,N,e,t.root);return a.snapshot=t.root,new sa(new Ve(a,[]),t)}function S3(e){let t={},n={},r={},o="",i=new Er([],t,r,o,n,N,e,null,{});return new ca("",new Ve(i,[]))}var _n=class{constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(T(l=>l[Ao]))??M(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(T(t=>Ir(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(T(t=>Ir(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function aa(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:C(C({},t.params),e.params),data:C(C({},t.data),e.data),resolve:C(C(C(C({},e.data),t.data),o?.data),e._resolvedData)}:r={params:C({},e.params),data:C({},e.data),resolve:C(C({},e.data),e._resolvedData??{})},o&&Em(o)&&(r.resolve[Ao]=o.title),r}var Er=class{get title(){return this.data?.[Ao]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Ir(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Ir(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},ca=class extends ia{constructor(t,n){super(n),this.url=t,Pu(this,n)}toString(){return wm(this._root)}};function Pu(e,t){t.value._routerState=e,t.children.forEach(n=>Pu(e,n))}function wm(e){let t=e.children.length>0?` { ${e.children.map(wm).join(", ")} } `:"";return`${e.value}${t}`}function cu(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,pt(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),pt(t.params,n.params)||e.paramsSubject.next(n.params),e3(t.url,n.url)||e.urlSubject.next(n.url),pt(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Au(e,t){let n=pt(e.params,t.params)&&o3(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Au(e.parent,t.parent))}function Em(e){return typeof e.title=="string"||e.title===null}var ku=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=N,this.activateEvents=new se,this.deactivateEvents=new se,this.attachEvents=new se,this.detachEvents=new se,this.parentContexts=y(Fo),this.location=y(cr),this.changeDetector=y(ur),this.inputBinder=y(ha,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new I(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new I(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new I(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new I(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new xu(n,a,o.injector);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=et({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[rr]})}}return e})(),xu=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===_n?this.route:t===Fo?this.childContexts:this.parent.get(t,n)}},ha=new b(""),em=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=Rr([r.queryParams,r.params,r.data]).pipe(xe(([i,s,a],c)=>(a=C(C(C({},i),s),a),c===0?M(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=zp(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function A3(e,t,n){let r=Io(e,t._root,n?n._root:void 0);return new sa(r,t)}function Io(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=x3(e,t,n);return new Ve(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>Io(e,a)),s}}let r=T3(t.value),o=t.children.map(i=>Io(e,i));return new Ve(r,o)}}function x3(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return Io(e,r,o);return Io(e,r)})}function T3(e){return new _n(new fe(e.url),new fe(e.params),new fe(e.queryParams),new fe(e.fragment),new fe(e.data),e.outlet,e.component,e)}var _o=class{constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},bm="ngNavigationCancelingError";function la(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=In(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Im(!1,je.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Im(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[bm]=!0,n.cancellationCode=t,n}function F3(e){return _m(e)&&In(e.url)}function _m(e){return!!e&&e[bm]}var N3=(e,t,n,r)=>T(o=>(new Tu(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Tu=class{constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),cu(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=Dr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Dr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Dr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=Dr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Iu(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Eu(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(cu(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),cu(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},ua=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},br=class{constructor(t,n){this.component=t,this.route=n}};function O3(e,t,n){let r=e._root,o=t?t._root:null;return mo(r,o,n,[r.value])}function R3(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function xr(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Ef(e)?e:t.get(e):r}function mo(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Dr(t);return e.children.forEach(s=>{U3(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Do(a,n.getContext(s),o)),o}function U3(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=P3(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new ua(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?mo(e,t,a?a.children:null,r,o):mo(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new br(a.outlet.component,s))}else s&&Do(t,a,o),o.canActivateChecks.push(new ua(r)),i.component?mo(e,null,a?a.children:null,r,o):mo(e,null,n,r,o);return o}function P3(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!bn(e.url,t.url);case"pathParamsOrQueryParamsChange":return!bn(e.url,t.url)||!pt(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Au(e,t)||!pt(e.queryParams,t.queryParams);case"paramsChange":default:return!Au(e,t)}}function Do(e,t,n){let r=Dr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?Do(s,t.children.getContext(i),n):Do(s,null,n):Do(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new br(t.outlet.component,o)):n.canDeactivateChecks.push(new br(null,o)):n.canDeactivateChecks.push(new br(null,o))}function No(e){return typeof e=="function"}function k3(e){return typeof e=="boolean"}function L3(e){return e&&No(e.canLoad)}function V3(e){return e&&No(e.canActivate)}function j3(e){return e&&No(e.canActivateChild)}function B3(e){return e&&No(e.canDeactivate)}function $3(e){return e&&No(e.canMatch)}function Mm(e){return e instanceof yt||e?.name==="EmptyError"}var Ks=Symbol("INITIAL_VALUE");function Ar(){return xe(e=>Rr(e.map(t=>t.pipe(Ct(1),ka(Ks)))).pipe(T(t=>{for(let n of t)if(n!==!0){if(n===Ks)return Ks;if(n===!1||H3(n))return n}return!0}),Ae(t=>t!==Ks),Ct(1)))}function H3(e){return In(e)||e instanceof _o}function z3(e,t){return ne(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?M(B(C({},n),{guardsResult:!0})):G3(s,r,o,e).pipe(ne(a=>a&&k3(a)?W3(r,i,e,t):M(a)),T(a=>B(C({},n),{guardsResult:a})))})}function G3(e,t,n,r){return Q(e).pipe(ne(o=>Y3(o.component,o.route,n,t,r)),ot(o=>o!==!0,!0))}function W3(e,t,n,r){return Q(t).pipe(vt(o=>Un(Z3(o.route.parent,r),q3(o.route,r),K3(e,o.path,n),X3(e,o.route,n))),ot(o=>o!==!0,!0))}function q3(e,t){return e!==null&&t&&t(new bu(e)),M(!0)}function Z3(e,t){return e!==null&&t&&t(new wu(e)),M(!0)}function X3(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return M(!0);let o=r.map(i=>li(()=>{let s=To(t)??n,a=xr(i,s),c=V3(a)?a.canActivate(t,e):Pe(s,()=>a(t,e));return nn(c).pipe(ot())}));return M(o).pipe(Ar())}function K3(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>R3(s)).filter(s=>s!==null).map(s=>li(()=>{let a=s.guards.map(c=>{let l=To(s.node)??n,u=xr(c,l),d=j3(u)?u.canActivateChild(r,e):Pe(l,()=>u(r,e));return nn(d).pipe(ot())});return M(a).pipe(Ar())}));return M(i).pipe(Ar())}function Y3(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return M(!0);let s=i.map(a=>{let c=To(t)??o,l=xr(a,c),u=B3(l)?l.canDeactivate(e,t,n,r):Pe(c,()=>l(e,t,n,r));return nn(u).pipe(ot())});return M(s).pipe(Ar())}function Q3(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return M(!0);let i=o.map(s=>{let a=xr(s,e),c=L3(a)?a.canLoad(t,n):Pe(e,()=>a(t,n));return nn(c)});return M(i).pipe(Ar(),Sm(r))}function Sm(e){return Aa(le(t=>{if(typeof t!="boolean")throw la(e,t)}),T(t=>t===!0))}function J3(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return M(!0);let i=o.map(s=>{let a=xr(s,e),c=$3(a)?a.canMatch(t,n):Pe(e,()=>a(t,n));return nn(c)});return M(i).pipe(Ar(),Sm(r))}var Mo=class{constructor(t){this.segmentGroup=t||null}},So=class extends Error{constructor(t){super(),this.urlTree=t}};function Cr(e){return On(new Mo(e))}function eE(e){return On(new I(4e3,!1))}function tE(e){return On(Im(!1,je.GuardRejected))}var Fu=class{constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return M(r);if(o.numberOfChildren>1||!o.children[N])return eE(`${t.redirectTo}`);o=o.children[N]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:l,routeConfig:u,url:d,outlet:m,params:p,data:v,title:D}=o,_=Pe(i,()=>a({params:p,data:v,queryParams:c,fragment:l,routeConfig:u,url:d,outlet:m,title:D}));if(_ instanceof Rt)throw new So(_);n=_}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new So(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new Rt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new G(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new I(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Nu={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function nE(e,t,n,r,o){let i=Lu(e,t,n);return i.matched?(r=_3(t,r),J3(r,t,n,o).pipe(T(s=>s===!0?i:C({},Nu)))):M(i)}function Lu(e,t,n){if(t.path==="**")return rE(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?C({},Nu):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||Jw)(n,e,t);if(!o)return C({},Nu);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?C(C({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function rE(e){return{matched:!0,parameters:e.length>0?am(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function tm(e,t,n,r){return n.length>0&&sE(e,n,r)?{segmentGroup:new G(t,iE(r,new G(n,e.children))),slicedSegments:[]}:n.length===0&&aE(e,n,r)?{segmentGroup:new G(e.segments,oE(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new G(e.segments,e.children),slicedSegments:n}}function oE(e,t,n,r){let o={};for(let i of n)if(pa(e,t,i)&&!r[rt(i)]){let s=new G([],{});o[rt(i)]=s}return C(C({},r),o)}function iE(e,t){let n={};n[N]=t;for(let r of e)if(r.path===""&&rt(r)!==N){let o=new G([],{});n[rt(r)]=o}return n}function sE(e,t,n){return n.some(r=>pa(e,t,r)&&rt(r)!==N)}function aE(e,t,n){return n.some(r=>pa(e,t,r))}function pa(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function cE(e,t,n,r){return rt(e)!==r&&(r===N||!pa(t,n,e))?!1:Lu(t,e,n).matched}function lE(e,t,n){return t.length===0&&!e.children[n]}var Ou=class{};function uE(e,t,n,r,o,i,s="emptyOnly"){return new Ru(e,t,n,r,o,s,i).recognize()}var dE=31,Ru=class{constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Fu(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new I(4002,`'${t.segmentGroup}'`)}recognize(){let t=tm(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(T(({children:n,rootSnapshot:r})=>{let o=new Ve(r,n),i=new ca("",o),s=v3(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new Er([],Object.freeze({}),Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),N,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,N,n).pipe(T(r=>({children:r,rootSnapshot:n})),kt(r=>{if(r instanceof So)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof Mo?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(T(s=>s instanceof Ve?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return Q(i).pipe(vt(s=>{let a=r.children[s],c=M3(n,s);return this.processSegmentGroup(t,c,a,s,o)}),Pa((s,a)=>(s.push(...a),s)),Lt(null),Ua(),ne(s=>{if(s===null)return Cr(r);let a=Am(s);return fE(a),M(a)}))}processSegment(t,n,r,o,i,s,a){return Q(n).pipe(vt(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(kt(l=>{if(l instanceof Mo)return M(null);throw l}))),ot(c=>!!c),kt(c=>{if(Mm(c))return lE(r,o,i)?M(new Ou):Cr(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return cE(r,o,i,s)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):Cr(o):Cr(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:d,remainingSegments:m}=Lu(n,o,i);if(!c)return Cr(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>dE&&(this.allowRedirects=!1));let p=new Er(i,l,Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,nm(o),rt(o),o.component??o._loadedComponent??null,o,rm(o)),v=aa(p,a,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let D=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,d,p,t);return this.applyRedirects.lineralizeSegments(o,D).pipe(ne(_=>this.processSegment(t,r,n,_.concat(m),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=nE(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(xe(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(xe(({routes:l})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:m,remainingSegments:p}=c,v=new Er(m,d,Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,nm(r),rt(r),r.component??r._loadedComponent??null,r,rm(r)),D=aa(v,s,this.paramsInheritanceStrategy);v.params=Object.freeze(D.params),v.data=Object.freeze(D.data);let{segmentGroup:_,slicedSegments:F}=tm(n,m,p,l);if(F.length===0&&_.hasChildren())return this.processChildren(u,l,_,v).pipe(T(U=>new Ve(v,U)));if(l.length===0&&F.length===0)return M(new Ve(v,[]));let ve=rt(r)===i;return this.processSegment(u,l,_,F,ve?N:i,!0,v).pipe(T(U=>new Ve(v,U instanceof Ve?[U]:[])))}))):Cr(n)))}getChildConfig(t,n,r){return n.children?M({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?M({routes:n._loadedRoutes,injector:n._loadedInjector}):Q3(t,n,r,this.urlSerializer).pipe(ne(o=>o?this.configLoader.loadChildren(t,n).pipe(le(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):tE(n))):M({routes:[],injector:t})}};function fE(e){e.sort((t,n)=>t.value.outlet===N?-1:n.value.outlet===N?1:t.value.outlet.localeCompare(n.value.outlet))}function hE(e){let t=e.value.routeConfig;return t&&t.path===""}function Am(e){let t=[],n=new Set;for(let r of e){if(!hE(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Am(r.children);t.push(new Ve(r.value,o))}return t.filter(r=>!n.has(r))}function nm(e){return e.data||{}}function rm(e){return e.resolve||{}}function pE(e,t,n,r,o,i){return ne(s=>uE(e,t,n,r,s.extractedUrl,o,i).pipe(T(({state:a,tree:c})=>B(C({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function gE(e,t){return ne(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return M(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of xm(c))s.add(l);let a=0;return Q(s).pipe(vt(c=>i.has(c)?mE(c,r,e,t):(c.data=aa(c,c.parent,e).resolve,M(void 0))),le(()=>a++),Pn(1),ne(c=>a===s.size?M(n):Re))})}function xm(e){let t=e.children.map(n=>xm(n)).flat();return[e,...t]}function mE(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Em(o)&&(i[Ao]=o.title),yE(i,e,t,r).pipe(T(s=>(e._resolvedData=s,e.data=aa(e,e.parent,n).resolve,null)))}function yE(e,t,n,r){let o=du(e);if(o.length===0)return M({});let i={};return Q(o).pipe(ne(s=>vE(e[s],t,n,r).pipe(ot(),le(a=>{if(a instanceof _o)throw la(new _r,a);i[s]=a}))),Pn(1),Ra(i),kt(s=>Mm(s)?Re:On(s)))}function vE(e,t,n,r){let o=To(t)??r,i=xr(e,o),s=i.resolve?i.resolve(t,n):Pe(o,()=>i(t,n));return nn(s)}function lu(e){return xe(t=>{let n=e(t);return n?Q(n).pipe(T(()=>t)):M(t)})}var Tm=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===N);return r}getResolvedTitleForRoute(n){return n.data[Ao]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(CE),providedIn:"root"})}}return e})(),CE=(()=>{class e extends Tm{constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(E(Ng))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Oo=new b("",{providedIn:"root",factory:()=>({})}),DE=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=re({type:e,selectors:[["ng-component"]],standalone:!0,features:[Tp],decls:1,vars:0,template:function(r,o){r&1&&g(0,"router-outlet")},dependencies:[ku],encapsulation:2})}}return e})();function Vu(e){let t=e.children&&e.children.map(Vu),n=t?B(C({},e),{children:t}):C({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==N&&(n.component=DE),n}var da=new b(""),ju=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=y(ws)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return M(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=nn(n.loadComponent()).pipe(T(Fm),le(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Vt(()=>{this.componentLoaders.delete(n)})),o=new Nn(r,()=>new ce).pipe(Fn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return M({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=wE(r,this.compiler,n,this.onLoadEndListener).pipe(Vt(()=>{this.childrenLoaders.delete(r)})),s=new Nn(i,()=>new ce).pipe(Fn());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function wE(e,t,n,r){return nn(e.loadChildren()).pipe(T(Fm),ne(o=>o instanceof Hr||Array.isArray(o)?M(o):Q(t.compileModuleAsync(o))),T(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(da,[],{optional:!0,self:!0}).flat()),{routes:s.map(Vu),injector:i}}))}function EE(e){return e&&typeof e=="object"&&"default"in e}function Fm(e){return EE(e)?e.default:e}var Bu=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(bE),providedIn:"root"})}}return e})(),bE=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Nm=new b(""),Om=new b("");function IE(e,t,n){let r=e.get(Om),o=e.get(ye);return e.get(W).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),_E(e))),{onViewTransitionCreated:c}=r;return c&&Pe(e,()=>c({transition:a,from:t,to:n})),s})}function _E(e){return new Promise(t=>{yl({read:()=>setTimeout(t)},{injector:e})})}var ME=new b(""),$u=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new ce,this.transitionAbortSubject=new ce,this.configLoader=y(ju),this.environmentInjector=y(be),this.urlSerializer=y(xo),this.rootContexts=y(Fo),this.location=y(hr),this.inputBindingEnabled=y(ha,{optional:!0})!==null,this.titleStrategy=y(Tm),this.options=y(Oo,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=y(Bu),this.createViewTransition=y(Nm,{optional:!0}),this.navigationErrorHandler=y(ME,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>M(void 0),this.rootComponentType=null;let n=o=>this.events.next(new Cu(o)),r=o=>this.events.next(new Du(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(B(C(C({},this.transitions.value),n),{id:r}))}setupNavigations(n,r,o){return this.transitions=new fe({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Co,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Ae(i=>i.id!==0),T(i=>B(C({},i),{extractedUrl:this.urlHandlingStrategy.extract(i.rawUrl)})),xe(i=>{let s=!1,a=!1;return M(i).pipe(xe(c=>{if(this.navigationId>i.id)return this.cancelNavigationTransition(i,"",je.SupersededByNewNavigation),Re;this.currentTransition=i,this.currentNavigation={id:c.id,initialUrl:c.rawUrl,extractedUrl:c.extractedUrl,targetBrowserUrl:typeof c.extras.browserUrl=="string"?this.urlSerializer.parse(c.extras.browserUrl):c.extras.browserUrl,trigger:c.source,extras:c.extras,previousNavigation:this.lastSuccessfulNavigation?B(C({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=c.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!l&&u!=="reload"){let d="";return this.events.next(new en(c.id,this.urlSerializer.serialize(c.rawUrl),d,na.IgnoredSameUrlNavigation)),c.resolve(!1),Re}if(this.urlHandlingStrategy.shouldProcessUrl(c.rawUrl))return M(c).pipe(xe(d=>{let m=this.transitions?.getValue();return this.events.next(new Mr(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),m!==this.transitions?.getValue()?Re:Promise.resolve(d)}),pE(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),le(d=>{i.targetSnapshot=d.targetSnapshot,i.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=B(C({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let m=new ra(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(m)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(c.currentRawUrl)){let{id:d,extractedUrl:m,source:p,restoredState:v,extras:D}=c,_=new Mr(d,this.urlSerializer.serialize(m),p,v);this.events.next(_);let F=Dm(this.rootComponentType).snapshot;return this.currentTransition=i=B(C({},c),{targetSnapshot:F,urlAfterRedirects:m,extras:B(C({},D),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=m,M(i)}else{let d="";return this.events.next(new en(c.id,this.urlSerializer.serialize(c.extractedUrl),d,na.IgnoredByUrlHandlingStrategy)),c.resolve(!1),Re}}),le(c=>{let l=new gu(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),T(c=>(this.currentTransition=i=B(C({},c),{guards:O3(c.targetSnapshot,c.currentSnapshot,this.rootContexts)}),i)),z3(this.environmentInjector,c=>this.events.next(c)),le(c=>{if(i.guardsResult=c.guardsResult,c.guardsResult&&typeof c.guardsResult!="boolean")throw la(this.urlSerializer,c.guardsResult);let l=new mu(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot,!!c.guardsResult);this.events.next(l)}),Ae(c=>c.guardsResult?!0:(this.cancelNavigationTransition(c,"",je.GuardRejected),!1)),lu(c=>{if(c.guards.canActivateChecks.length)return M(c).pipe(le(l=>{let u=new yu(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}),xe(l=>{let u=!1;return M(l).pipe(gE(this.paramsInheritanceStrategy,this.environmentInjector),le({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(l,"",je.NoDataFromResolver)}}))}),le(l=>{let u=new vu(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}))}),lu(c=>{let l=u=>{let d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe(le(m=>{u.component=m}),T(()=>{})));for(let m of u.children)d.push(...l(m));return d};return Rr(l(c.targetSnapshot.root)).pipe(Lt(null),Ct(1))}),lu(()=>this.afterPreactivation()),xe(()=>{let{currentSnapshot:c,targetSnapshot:l}=i,u=this.createViewTransition?.(this.environmentInjector,c.root,l.root);return u?Q(u).pipe(T(()=>i)):M(i)}),T(c=>{let l=A3(n.routeReuseStrategy,c.targetSnapshot,c.currentRouterState);return this.currentTransition=i=B(C({},c),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,i}),le(()=>{this.events.next(new bo)}),N3(this.rootContexts,n.routeReuseStrategy,c=>this.events.next(c),this.inputBindingEnabled),Ct(1),le({next:c=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new gt(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects))),this.titleStrategy?.updateTitle(c.targetRouterState.snapshot),c.resolve(!0)},complete:()=>{s=!0}}),La(this.transitionAbortSubject.pipe(le(c=>{throw c}))),Vt(()=>{!s&&!a&&this.cancelNavigationTransition(i,"",je.SupersededByNewNavigation),this.currentTransition?.id===i.id&&(this.currentNavigation=null,this.currentTransition=null)}),kt(c=>{if(a=!0,_m(c))this.events.next(new Ot(i.id,this.urlSerializer.serialize(i.extractedUrl),c.message,c.cancellationCode)),F3(c)?this.events.next(new Sr(c.url,c.navigationBehaviorOptions)):i.resolve(!1);else{let l=new Eo(i.id,this.urlSerializer.serialize(i.extractedUrl),c,i.targetSnapshot??void 0);try{let u=Pe(this.environmentInjector,()=>this.navigationErrorHandler?.(l));if(u instanceof _o){let{message:d,cancellationCode:m}=la(this.urlSerializer,u);this.events.next(new Ot(i.id,this.urlSerializer.serialize(i.extractedUrl),d,m)),this.events.next(new Sr(u.redirectTo,u.navigationBehaviorOptions))}else{this.events.next(l);let d=n.errorHandler(c);i.resolve(!!d)}}catch(u){this.options.resolveNavigationPromiseOnError?i.resolve(!1):i.reject(u)}}return Re}))}))}cancelNavigationTransition(n,r,o){let i=new Ot(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function SE(e){return e!==Co}var AE=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(xE),providedIn:"root"})}}return e})(),Uu=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},xE=(()=>{class e extends Uu{static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=rs(e)))(o||e)}})()}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Rm=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(TE),providedIn:"root"})}}return e})(),TE=(()=>{class e extends Rm{constructor(){super(...arguments),this.location=y(hr),this.urlSerializer=y(xo),this.options=y(Oo,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=y(Bu),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new Rt,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=Dm(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof Mr)this.stateMemento=this.createStateMemento();else if(n instanceof en)this.rawUrlTree=r.initialUrl;else if(n instanceof ra){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let o=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??o,r)}}else n instanceof bo?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof Ot&&(n.code===je.GuardRejected||n.code===je.NoDataFromResolver)?this.restoreHistory(r):n instanceof Eo?this.restoreHistory(r,!0):n instanceof gt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let o=n instanceof Rt?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){let i=this.browserPageId,s=C(C({},r.extras.state),this.generateNgRouterState(r.id,i));this.location.replaceState(o,"",s)}else{let i=C(C({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(o,"",i)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.currentUrlTree===n.finalUrl&&i===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=rs(e)))(o||e)}})()}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),yo=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(yo||{});function Um(e,t){e.events.pipe(Ae(n=>n instanceof gt||n instanceof Ot||n instanceof Eo||n instanceof en),T(n=>n instanceof gt||n instanceof en?yo.COMPLETE:(n instanceof Ot?n.code===je.Redirect||n.code===je.SupersededByNewNavigation:!1)?yo.REDIRECTING:yo.FAILED),Ae(n=>n!==yo.REDIRECTING),Ct(1)).subscribe(()=>{t()})}function FE(e){throw e}var NE={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},OE={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},tn=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=y(ms),this.stateManager=y(Rm),this.options=y(Oo,{optional:!0})||{},this.pendingTasks=y(It),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=y($u),this.urlSerializer=y(xo),this.location=y(hr),this.urlHandlingStrategy=y(Bu),this._events=new ce,this.errorHandler=this.options.errorHandler||FE,this.navigated=!1,this.routeReuseStrategy=y(AE),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=y(da,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!y(ha,{optional:!0}),this.eventsSubscription=new ee,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Ot&&r.code!==je.Redirect&&r.code!==je.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof gt)this.navigated=!0;else if(r instanceof Sr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=C({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||SE(o.source)},s);this.scheduleNavigation(a,Co,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}UE(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Co,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=C({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(Vu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a){case"merge":u=C(C({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let m=o?o.snapshot:this.routerState.snapshot.root;d=mm(m)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return ym(d,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=In(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Co,null,r)}navigate(n,r={skipLocationChange:!1}){return RE(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=C({},NE):r===!1?o=C({},OE):o=r,In(n))return Kg(this.currentUrlTree,n,o);let i=this.parseUrl(n);return Kg(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((d,m)=>{a=d,c=m});let u=this.pendingTasks.add();return Um(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function RE(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new I(4008,!1)}function UE(e){return!(e instanceof bo)&&!(e instanceof Sr)}var Pm=(()=>{class e{constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new ce,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(l=>{l instanceof gt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(In(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:$h(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:In(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)(j(tn),j(_n),nl("tabindex"),j(sr),j(vn),j(Tt))}}static{this.\u0275dir=et({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&ht("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&ps("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",dr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",dr],replaceUrl:[2,"replaceUrl","replaceUrl",dr],routerLink:"routerLink"},standalone:!0,features:[Cl,rr]})}}return e})();var fa=class{};var PE=(()=>{class e{constructor(n,r,o,i,s){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Ae(n=>n instanceof gt),vt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=hs(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return Q(o).pipe(Rn())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=M(null);let i=o.pipe(ne(s=>s===null?M(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return Q([i,s]).pipe(Rn())}else return i})}static{this.\u0275fac=function(r){return new(r||e)(E(tn),E(ws),E(be),E(fa),E(ju))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),km=new b(""),kE=(()=>{class e{constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Mr?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof gt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof en&&n.code===na.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof oa&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new oa(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){tp()}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function LE(e){return e.routerState.root}function Ro(e,t){return{\u0275kind:e,\u0275providers:t}}function VE(){let e=y(Ue);return t=>{let n=e.get(Xt);if(t!==n.components[0])return;let r=e.get(tn),o=e.get(Lm);e.get(Hu)===1&&r.initialNavigation(),e.get(Vm,null,R.Optional)?.setUpPreloading(),e.get(km,null,R.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Lm=new b("",{factory:()=>new ce}),Hu=new b("",{providedIn:"root",factory:()=>1});function jE(){return Ro(2,[{provide:Hu,useValue:0},{provide:Cs,multi:!0,deps:[Ue],useFactory:t=>{let n=t.get(Jp,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let o=t.get(tn),i=t.get(Lm);Um(o,()=>{r(!0)}),t.get($u).afterPreactivation=()=>(r(!0),i.closed?M(void 0):i),o.initialNavigation()}))}}])}function BE(){return Ro(3,[{provide:Cs,multi:!0,useFactory:()=>{let t=y(tn);return()=>{t.setUpLocationChangeListener()}}},{provide:Hu,useValue:2}])}var Vm=new b("");function $E(e){return Ro(0,[{provide:Vm,useExisting:PE},{provide:fa,useExisting:e}])}function HE(){return Ro(8,[em,{provide:ha,useExisting:em}])}function zE(e){let t=[{provide:Nm,useValue:IE},{provide:Om,useValue:C({skipNextTransition:!!e?.skipInitialTransition},e)}];return Ro(9,t)}var om=new b("ROUTER_FORROOT_GUARD"),GE=[hr,{provide:xo,useClass:_r},tn,Fo,{provide:_n,useFactory:LE,deps:[tn]},ju,[]],zu=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[GE,[],{provide:da,multi:!0,useValue:n},{provide:om,useFactory:XE,deps:[[tn,new Yi,new jc]]},{provide:Oo,useValue:r||{}},r?.useHash?qE():ZE(),WE(),r?.preloadingStrategy?$E(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?KE(r):[],r?.bindToComponentInputs?HE().\u0275providers:[],r?.enableViewTransitions?zE().\u0275providers:[],YE()]}}static forChild(n){return{ngModule:e,providers:[{provide:da,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(E(om,8))}}static{this.\u0275mod=ge({type:e})}static{this.\u0275inj=pe({})}}return e})();function WE(){return{provide:km,useFactory:()=>{let e=y(cg),t=y(W),n=y(Oo),r=y($u),o=y(xo);return n.scrollOffset&&e.setOffset(n.scrollOffset),new kE(o,r,e,t,n)}}}function qE(){return{provide:Tt,useClass:tg}}function ZE(){return{provide:Tt,useClass:Pl}}function XE(e){return"guarded"}function KE(e){return[e.initialNavigation==="disabled"?BE().\u0275providers:[],e.initialNavigation==="enabledBlocking"?jE().\u0275providers:[]]}var im=new b("");function YE(){return[{provide:im,useFactory:VE},{provide:Ds,multi:!0,useExisting:im}]}var Bm=(()=>{class e{http;about={name:"",description:"",photo:"",email:"",portfolioUrl:""};constructor(n){this.http=n}ngOnInit(){this.getAboutData()}getAboutData(){this.http.get("https://portflio-backend-uiv7.onrender.com/api/about").subscribe(n=>{this.about=n},n=>{console.error("Error fetching About data:",n)})}static \u0275fac=function(r){return new(r||e)(j(Nt))};static \u0275cmp=re({type:e,selectors:[["app-about"]],decls:22,vars:2,consts:[["id","about-me",1,"about-me"],[1,"container"],[1,"about-me-container"],[1,"about-me-title"],[1,"about-me-flex-container"],[1,"about-me-image"],[1,"back-div"],[1,"main-image"],["alt","Profile Picture",3,"src"],[1,"about-me-content"],["href","https://drive.google.com/file/d/1So0fhL4n2hvNY3CpCJktXAbe1ZYJg8be/view?usp=sharing","target","_blank"],[1,"cta"],["width","15px","height","10px","viewBox","0 0 13 10"],["d","M1,5 L11,5"],["points","8 1 12 5 8 9"],[1,"text"]],template:function(r,o){r&1&&(f(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3),S(4," About "),g(5,"br"),S(6," Roaa Ayman "),h(),f(7,"div",4)(8,"div",5),g(9,"div",6),f(10,"div",7),g(11,"img",8),h()(),f(12,"div",9)(13,"a",10)(14,"button",11)(15,"span"),S(16,"Resume"),h(),or(),f(17,"svg",12),g(18,"path",13)(19,"polyline",14),h()()(),ir(),f(20,"div",15),S(21),h()()()()()()),r&2&&(q(11),me("src",o.about.photo,Cn),q(10),lr(" ",o.about.description||"Loading description..."," "))},styles:['@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}*[_ngcontent-%COMP%]{box-sizing:border-box;margin:0;padding:0;font-family:Nunito,sans-serif}html[_ngcontent-%COMP%]{scroll-behavior:smooth}body[_ngcontent-%COMP%]{background-color:#08505d}.montserrat[_ngcontent-%COMP%]{font-family:Montserrat,sans-serif}img[_ngcontent-%COMP%]{width:100%;-o-object-fit:cover;object-fit:cover}.container[_ngcontent-%COMP%]{max-width:1300px;margin:0 auto;padding:0 40px}@media (min-width: 1200px) and (max-width: 1441px){.container[_ngcontent-%COMP%]{max-width:1250px;padding:0 36px}}@media (max-width: 767px){.container[_ngcontent-%COMP%]{padding:0 30px}}@media (max-width: 479px){.container[_ngcontent-%COMP%]{padding:0 30px 0 20px}}.about-me[_ngcontent-%COMP%]{padding-top:50px;padding-bottom:50px}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{position:relative}@media (max-width: 960px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{padding-bottom:100px}}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:55px;color:#9b6195;font-weight:700;margin-bottom:20px}@media (max-width: 500px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:30px}}.about-me-flex-container[_ngcontent-%COMP%]{margin-top:-25px;margin-left:150px;left:100px;display:flex;justify-content:space-between}@media (max-width: 960px){.about-me-flex-container[_ngcontent-%COMP%]{flex-direction:column;justify-content:center;align-items:center;margin-left:0;gap:50px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]{margin-top:-10px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]{position:relative;width:400px;height:400px}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]{width:300px;height:300px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .back-div[_ngcontent-%COMP%]{position:absolute;bottom:0;z-index:-3;background-color:#9b6195;width:80%;height:80%}.about-me-title[_ngcontent-%COMP%]{font-family:Pacifico,cursive}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .black-image[_ngcontent-%COMP%]{z-index:-2;position:absolute;left:10px;bottom:10px;height:100%}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .black-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:75%;height:75%;overflow:hidden;position:absolute;right:15%;top:15%;box-shadow:#967eb9 0 7px 50px;transition:all .2s ease-out}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover{transform-origin:top center;transform:scale(1.5);border-radius:25px}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform-origin:center center;transform:scale(2);-o-object-fit:cover;object-fit:cover;transition:all .2s ease-out}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1)}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:50px;flex:0 0 40%}@media (max-width: 960px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{flex-direction:row-reverse}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{max-width:200px}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:drop-shadow(0 0 25px rgb(0,0,0))}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:rotate(90deg)}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:#87a4b6;font-weight:600;font-size:18px}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:16px}}.card[_ngcontent-%COMP%]{width:fit-content;height:fit-content;background-color:#eee;display:flex;align-items:center;justify-content:center;padding:25px;gap:20px;box-shadow:0 0 20px #0000000e}.socialContainer[_ngcontent-%COMP%]{width:52px;height:52px;background-color:#2c2c2c;display:flex;align-items:center;justify-content:center;overflow:hidden;transition-duration:.3s}.containerOne[_ngcontent-%COMP%]:hover{background-color:#d62976;transition-duration:.3s}.containerThree[_ngcontent-%COMP%]:hover{background-color:#0072b1;transition-duration:.3s}.containerFour[_ngcontent-%COMP%]:hover{background-color:#128c7e;transition-duration:.3s}.socialContainer[_ngcontent-%COMP%]:active{transform:scale(.9);transition-duration:.3s}.socialSvg[_ngcontent-%COMP%]{width:17px}.socialSvg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.socialContainer[_ngcontent-%COMP%]:hover   .socialSvg[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slide-in-top .3s both}@keyframes _ngcontent-%COMP%_slide-in-top{0%{transform:translateY(-50px);opacity:0}to{transform:translateY(0);opacity:1}}.portfolio-link[_ngcontent-%COMP%]{display:flex;justify-content:center;flex-wrap:wrap;text-align:center}.card[_ngcontent-%COMP%]{width:fit-content;height:fit-content;background-color:#eee;display:flex;align-items:center;justify-content:center;padding:25px;gap:20px;box-shadow:0 0 20px #0000000e;margin:0 auto}.cta[_ngcontent-%COMP%]{position:relative;margin:auto;padding:12px 18px;transition:all .2s ease;border:none;background:none;cursor:pointer}.cta[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;display:block;border-radius:50px;background:#b1dae7;width:45px;height:45px;transition:all .3s ease}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;font-family:Ubuntu,sans-serif;font-size:18px;font-weight:700;letter-spacing:.05em;color:#234567}.cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{position:relative;top:0;margin-left:10px;fill:none;stroke-linecap:round;stroke-linejoin:round;stroke:#234567;stroke-width:2;transform:translate(-5px);transition:all .3s ease}.cta[_ngcontent-%COMP%]:hover:before{width:100%;background:#b1dae7}.cta[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:translate(0)}.cta[_ngcontent-%COMP%]:active{transform:scale(.95)}.about-me-flex-container[_ngcontent-%COMP%]{display:flex;gap:20px}.about-me-image[_ngcontent-%COMP%]{position:relative}.back-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:300px;height:auto;filter:blur(10px);transition:filter .5s ease-in-out,transform .5s ease-in-out}.front-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:300px;height:auto;position:absolute;top:0;left:0;z-index:10}.about-me-image[_ngcontent-%COMP%]   .show-front-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:blur(0);transform:scale(1.05)}.cta[_ngcontent-%COMP%]{position:relative;display:inline-block;padding:12px 24px;background-color:#f7f7f7;border-radius:30px;cursor:pointer;transition:background-color .3s ease-in-out}.cta[_ngcontent-%COMP%]:hover{background-color:#e2e2e2}.text[_ngcontent-%COMP%]{margin-top:20px;font-size:16px;line-height:1.5;color:#666}']})}return e})();var $m=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=re({type:e,selectors:[["app-first"]],decls:10,vars:0,consts:[[1,"hero-section"],["autoplay","","muted","","loop","","id","bg-video"],["src","video/background.mp4","type","video/mp4"],[1,"profile-pic"],["src","images/roro.jpg","alt","Roaa Ayman"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"video",1),g(2,"source",2),S(3," Your browser does not support the video tag. "),h(),f(4,"div",3),g(5,"img",4),h(),f(6,"h1"),S(7,"Roaa Ayman"),h(),f(8,"p"),S(9,"Recent Computer Science Graduate & Front-End Developer Specializing in Angular"),h()())},styles:[".hero-section[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;color:#fff;overflow:hidden;background-size:cover}.profile-pic[_ngcontent-%COMP%]{border-radius:50%;overflow:hidden;width:150px;height:150px;margin-bottom:20px}.profile-pic[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:10px}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px}.social-icons[_ngcontent-%COMP%]{display:flex;gap:15px}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem;transition:color .3s}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#007bff}.background-animation[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1}#bg-video[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;min-width:100%;min-height:100%;width:auto;height:auto;z-index:-1;transform:translate(-50%,-50%);object-fit:cover}"]})}return e})();var QE=[{path:"",component:$m},{path:"about",component:Bm},{path:"**",redirectTo:""}],Hm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e});static \u0275inj=pe({imports:[zu.forRoot(QE,{scrollPositionRestoration:"enabled",anchorScrolling:"enabled"}),zu]})}return e})();var zm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=re({type:e,selectors:[["app-header"]],decls:25,vars:0,consts:[[1,"h-[70px]","sticky","top-0","z-50","border-b-2","border-black","bg-gradient-to-r","from-[#8e4564]","via-[#81b1d4]","to-[#a06a9d]","px-8","flex","items-center","justify-between"],["type","checkbox","id","check",1,"hidden","peer"],["for","check",1,"menu","block","lg:hidden","cursor-pointer","z-50"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","currentColor",1,"bi","bi-list"],["fill-rule","evenodd","d","M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"],[1,"logo"],[1,"text-black","font-gupter","font-medium","cursor-pointer","text-xl"],[1,"nav-items","peer-checked:right-0","fixed","lg:static","top-0","right-[-250px]","h-screen","lg:h-auto","w-[250px]","lg:w-auto","flex","flex-col","lg:flex-row","justify-evenly","lg:justify-end","items-start","lg:items-center","bg-[#a06a9d]","lg:bg-transparent","transition-all","duration-500","p-8","lg:p-0","gap-y-6","lg:gap-x-6"],[1,"flex","flex-col","lg:flex-row","gap-y-4","lg:gap-x-4","text-black","text-[18px]","font-medium"],["routerLink","/",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["routerLink","/about",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#skills",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#projects",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#contact",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"]],template:function(r,o){r&1&&(f(0,"nav",0),g(1,"input",1),f(2,"label",2),or(),f(3,"svg",3),g(4,"path",4),h()(),ir(),f(5,"div",5)(6,"h2",6),S(7,"RA"),h()(),f(8,"div",7)(9,"ul",8)(10,"li")(11,"a",9),S(12,"Home"),h()(),f(13,"li")(14,"a",10),S(15,"About"),h()(),f(16,"li")(17,"a",11),S(18,"Skills"),h()(),f(19,"li")(20,"a",12),S(21,"Projects"),h()(),f(22,"li")(23,"a",13),S(24,"Contact"),h()()()()())},dependencies:[Pm],styles:['@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFUkU1Z4Y.woff2) format("woff2");unicode-range:U+0600-06FF,U+0750-077F,U+0870-088E,U+0890-0891,U+0897-08E1,U+08E3-08FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE70-FE74,U+FE76-FEFC,U+102E0-102FB,U+10E60-10E7E,U+10EC2-10EC4,U+10EFC-10EFF,U+1EE00-1EE03,U+1EE05-1EE1F,U+1EE21-1EE22,U+1EE24,U+1EE27,U+1EE29-1EE32,U+1EE34-1EE37,U+1EE39,U+1EE3B,U+1EE42,U+1EE47,U+1EE49,U+1EE4B,U+1EE4D-1EE4F,U+1EE51-1EE52,U+1EE54,U+1EE57,U+1EE59,U+1EE5B,U+1EE5D,U+1EE5F,U+1EE61-1EE62,U+1EE64,U+1EE67-1EE6A,U+1EE6C-1EE72,U+1EE74-1EE77,U+1EE79-1EE7C,U+1EE7E,U+1EE80-1EE89,U+1EE8B-1EE9B,U+1EEA1-1EEA3,U+1EEA5-1EEA9,U+1EEAB-1EEBB,U+1EEF0-1EEF1}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFWkU1Z4Y.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFU0U1Z4Y.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFVUU1Z4Y.woff2) format("woff2");unicode-range:U+0307-0308,U+0590-05FF,U+200C-2010,U+20AA,U+25CC,U+FB1D-FB4F}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFWUU1Z4Y.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFV0U1.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPONa9Ff0.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPOJa9Ff0.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPOxa9A.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cm9JNmxJqPO1QkZpy-.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cl9JNmxJqPO1Qslb-rVc74.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cl9JNmxJqPO1Qs3bmrVc74.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Kalnia Glaze;font-style:normal;font-weight:100 700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/kalniaglaze/v4/wlpjgwHCBUNjrGrfu-hwowN1YyC-42Lu26VHf2LtEEIAhqSP.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Kalnia Glaze;font-style:normal;font-weight:100 700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/kalniaglaze/v4/wlpjgwHCBUNjrGrfu-hwowN1YyC-42Lu26VHf2LtEEwAhg.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}body[_ngcontent-%COMP%]{--google-font-color-kalniaglaze:none}*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box;font-family:Pacifico,cursive}nav[_ngcontent-%COMP%]{height:70px;background-image:linear-gradient(to right,#8e4564,#905f8b,#897aac,#8195c3,#80aed1,#81b1d4,#82b5d6,#83b8d9,#83a6d3,#8b93c8,#967fb6,#a06a9d);padding:0 2rem;display:flex;justify-content:space-between;align-items:center;top:0;z-index:1000;position:sticky;border-bottom:2px solid rgb(0,0,0)}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:none}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-family:Gupter,serif;font-weight:700;font-size:2rem;color:#fff;cursor:pointer;margin:0 .5rem;text-shadow:2px 2px 4px rgba(0,0,0,.3);transition:all .3s ease;position:relative;z-index:1}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:before{content:"";position:absolute;width:100%;height:2px;bottom:-5px;left:0;background:linear-gradient(to right,#8e4564,#81b1d4);transform:scaleX(0);transform-origin:left;transition:transform .3s ease}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.nav-items[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{display:flex}.overview[_ngcontent-%COMP%]{margin-right:4rem}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:none}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style:none;margin:0 .5rem}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#000;font-size:18px}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#5b2b6b}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:"";display:block;height:3px;background:#481159;width:0%;transition:all ease-in-out .3s}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after{width:100%}#check[_ngcontent-%COMP%], .menu[_ngcontent-%COMP%]{display:none}@media (max-width: 750px){.nav-items[_ngcontent-%COMP%]{z-index:1000;position:fixed;top:0;height:100vh;width:250px;flex-direction:column;justify-content:space-evenly;background:#a06a9d;padding:2rem;right:-250px;transition:all ease-in-out .5s}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{flex-direction:column;width:auto}.overview[_ngcontent-%COMP%]{margin:0}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:inline-block;font-weight:400;text-transform:uppercase;font-size:13px;margin-bottom:1rem}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:inline-block;cursor:pointer;vertical-align:top}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-block}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{margin-left:2px;transition:all ease-in-out .3s}.menu[_ngcontent-%COMP%]{display:inline-block;position:fixed;right:2.5rem;z-index:1001}#check[_ngcontent-%COMP%]:checked ~ .nav-items[_ngcontent-%COMP%]{right:0}}']})}return e})();var Gm=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/projects";constructor(n){this.http=n}getProjects(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(Nt))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function t4(e,t){if(e&1&&(f(0,"span",15),S(1),h()),e&2){let n=t.$implicit;q(),ke(n)}}function n4(e,t){if(e&1){let n=Sp();f(0,"div",4)(1,"div",5),g(2,"img",6),h(),f(3,"h1",7),S(4),h(),f(5,"p",8),S(6),h(),f(7,"p",9),S(8),h(),f(9,"div",10)(10,"strong",11),S(11,"Skills:"),h(),We(12,t4,2,1,"span",12),h(),f(13,"div",13)(14,"button",14),ht("click",function(){let o=qc(n).$implicit,i=Zt();return Zc(i.openGitHub(o.githubLink))}),S(15,"GitHub"),h(),f(16,"button",14),ht("click",function(){let o=qc(n).$implicit,i=Zt();return Zc(i.openGitHub(o.link))}),S(17,"Live Demo"),h()()()}if(e&2){let n=t.$implicit;q(2),me("src",n.photo,Cn),q(2),ke(n.name),q(2),ke(n.title),q(2),ke(n.description),q(4),me("ngForOf",n.skills)}}var Wm=(()=>{class e{apiService;projects=[];constructor(n){this.apiService=n}ngOnInit(){this.apiService.getProjects().subscribe(n=>{this.projects=n.map(r=>({photo:r.photo,name:r.name,title:r.title,description:r.description,link:r.link,githubLink:r.githubLink,skills:r.skills}))},n=>{console.error("Error fetching projects:",n)})}openGitHub(n){window.open(n,"_blank")}static \u0275fac=function(r){return new(r||e)(j(Gm))};static \u0275cmp=re({type:e,selectors:[["app-projects"]],decls:5,vars:1,consts:[[1,"text-center","mt-6"],[1,"text-4xl","font-righteous","uppercase","text-black","mb-4","relative","inline-block","after:block","after:w-32","after:h-1","after:bg-cyan-500","after:rounded","after:mx-auto","after:mt-2"],[1,"flex","flex-wrap","justify-center","gap-6","p-4"],["class","backdrop-blur-xl bg-[#9b6195]/80 border border-white/20 rounded-xl shadow-xl w-80 p-6 flex flex-col items-center text-center",4,"ngFor","ngForOf"],[1,"backdrop-blur-xl","bg-[#9b6195]/80","border","border-white/20","rounded-xl","shadow-xl","w-80","p-6","flex","flex-col","items-center","text-center"],[1,"w-full","h-72","rounded-xl","overflow-hidden","mb-4"],["alt","Project Image",1,"w-full","h-full","object-cover","object-center",3,"src"],[1,"text-white","text-2xl","font-righteous","uppercase","mb-1"],[1,"text-white","text-sm","font-lato","tracking-wider","uppercase"],[1,"text-white","text-xs","font-lato","mt-1"],[1,"mt-4"],[1,"text-white"],["class","inline-block bg-white text-gray-800 text-xs rounded px-2 py-1 mr-2 mt-2",4,"ngFor","ngForOf"],[1,"mt-5","flex","gap-3"],[1,"px-4","py-2","border","border-purple-900","text-purple-900","rounded-full","text-sm","uppercase","hover:scale-110","hover:border-cyan-600","hover:text-cyan-600","transition",3,"click"],[1,"inline-block","bg-white","text-gray-800","text-xs","rounded","px-2","py-1","mr-2","mt-2"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"h2",1),S(2," Projects "),h()(),f(3,"div",2),We(4,n4,18,5,"div",3),h()),r&2&&(q(4),me("ngForOf",o.projects))},dependencies:[pr]})}return e})();var o4=(e,t)=>({success:e,error:t});function i4(e,t){if(e&1&&(f(0,"div",219),S(1),h()),e&2){let n=Zt();me("ngClass",Fp(2,o4,n.submissionStatus==="success",n.submissionStatus==="error")),q(),lr(" ",n.submissionMessage,`
`)}}var qm=(()=>{class e{submissionStatus=null;submissionMessage="";onSubmit(n){n.preventDefault();let r=n.target;fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json"}}).then(o=>{o.ok?(this.submissionStatus="success",this.submissionMessage="Message sent successfully!",r.reset()):(this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later.")}).catch(()=>{this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later."})}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=re({type:e,selectors:[["app-contact"]],decls:223,vars:1,consts:[[1,"contact-header"],[1,"heading"],[1,"container","d-flex","justify-content-center","align-items-center"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 790 563","fill","none"],["id","Image"],["id","g14"],["id","g16"],["id","g22"],["id","path24","d","M578.06 12.9772C592.384 8.33142 607.668 7.43103 622.682 8.31278C644.252 9.57946 666.668 15.0178 682.527 29.8837C692.521 39.2526 699.149 51.6277 707.182 62.7655C730.486 95.0785 766.513 118.198 782.236 154.912C795.674 186.289 790.623 225.749 767.498 250.666C744.37 275.583 703.649 282.658 675.018 264.535C647.531 247.136 635.383 212.503 610.273 191.742C592.326 176.901 569.144 170.28 549.646 157.607C529.69 144.636 513.457 124.248 509.79 100.515C506.745 80.8173 513.744 59.4156 528.903 46.4558C543.731 33.7796 559.331 19.0536 578.06 12.9772Z","fill","#D0F6FF"],["id","g26"],["id","path28","d","M702.629 254.14C677.841 258.169 653.602 251.674 628.841 247.05C605.059 242.608 581.372 234.267 562.49 218.522C553.842 211.31 546.177 202.344 542.784 191.529C537.944 176.097 542.362 159.436 542.319 143.243C542.267 124.241 537.593 105.929 524.57 91.9138C516.642 83.3826 507.429 75.9038 501.21 66.026C488.249 45.4368 498.285 17.8695 518.578 6.24557C537.067 -4.34208 560.588 -0.151769 579.793 9.03335C598.996 18.2198 615.855 31.9082 635.139 40.9228C656.28 50.8045 679.407 54.6779 702.724 56.9022C720.556 58.6044 738.716 56.5333 753.266 67.1156C763.675 74.6877 771.032 86.0519 775.307 98.2911C783.396 121.448 781.768 148.673 778.037 172.583C775.54 188.601 770.517 204.461 761.348 217.755C750.094 234.074 732.89 245.819 714.058 251.504C710.234 252.66 706.426 253.523 702.629 254.14Z","fill","#ADE0EC"],["id","g30"],["id","path32","d","M663.601 562.578H87.0689C43.5385 528.913 13.2922 480.886 5.1219 426.023C1.72497 403.207 3.65744 376.191 22.008 362.528C50.2285 341.516 92.5784 368.009 124.46 353.325C144.998 343.869 155.119 319.297 155.332 296.439C155.544 273.583 147.922 251.523 142.217 229.409C136.51 207.295 132.749 183.417 140.459 161.935C148.169 140.454 170.87 123.025 192.716 128.727C211.437 133.614 223.318 152.833 241.257 160.133C259.931 167.732 281.608 160.819 298.184 149.256C314.758 137.694 327.949 121.87 343.441 108.858C370.638 86.0156 406.562 72.0169 441.495 77.35C476.426 82.6831 508.747 110.108 514.202 145.471C518.662 174.4 506.652 207.826 524.152 231.129C543.883 257.401 585.152 250.025 613.676 265.983C636.899 278.972 649.286 309.077 642.052 334.934C634.666 361.336 609.565 383.494 613.653 410.622C616.583 430.071 633.6 443.505 645.587 458.982C668.627 488.727 679.049 528.158 663.601 562.578Z","fill","#D0F6FF"],["id","g34"],["id","path36","d","M636.536 562.578H142.588C127.567 548.706 110.711 535.931 102.179 517.242C93.6475 498.553 93.6698 474.269 107.702 459.372C124.638 441.394 152.947 443.847 176.763 437.899C204.228 431.038 229.205 408.689 232.723 380.251C237.265 343.537 206.911 309.992 208.804 273.041C210.296 243.911 234.698 217.737 263.314 214.567C282.66 212.424 302.727 219.607 321.415 214.109C338.741 209.012 351.237 194.119 366.296 184.052C383.968 172.235 406.528 167.099 426.891 172.974C447.257 178.85 464.492 196.695 467.235 217.968C470.152 240.588 458.004 266.283 470.888 284.991C480.485 298.927 499.63 301.618 516.392 301.075C533.155 300.531 551.03 298.252 565.763 306.372C579.463 313.921 587.611 329.548 589.138 345.273C590.664 360.996 586.334 376.788 579.943 391.199C574.357 403.794 567.162 415.706 562.961 428.843C558.759 441.979 557.893 457.066 564.737 469.006C571.941 481.577 585.915 488.105 597.307 496.94C617.442 512.552 635.027 536.936 636.536 562.578Z","fill","#ADE0EC"],["id","g38"],["id","path40","d","M595.195 76.2172L623.725 149.709L684.511 114.948L595.195 76.2172Z","fill","#FAFAFA"],["id","g42"],["id","path44","d","M595.195 76.2172L651.26 133.962L666.528 125.232L595.195 76.2172Z","fill","#DADADA"],["id","g46"],["id","path48","d","M666.528 125.231L655.896 151.885L651.262 133.962L666.528 125.231Z","fill","#DADADA"],["id","g50"],["id","path52","d","M655.896 151.885L642.776 138.814L651.262 133.962L655.896 151.885Z","fill","#B2B2B2"],["id","g54"],["id","path56","d","M222.015 539.778C157.683 522.604 101.579 476.087 72.2367 415.592C60.1279 390.628 52.3612 362.908 54.182 335.155C56.0014 307.4 68.2732 279.663 90.2639 263.011C112.253 246.359 144.303 242.756 167.56 257.538C190.03 271.821 200.733 299.209 220.204 317.461C243.475 339.274 280.404 345.641 308.459 330.683C336.514 315.723 352.288 279.369 342.05 248.968C332.575 220.834 305.793 203.339 282.527 185.228C259.261 167.115 236.126 141.651 239.454 112.116C242.315 86.7319 264.382 67.653 287.628 57.7513C332.132 38.7951 389.516 47.2223 419.844 85.2787C452.476 126.224 446.202 185.954 431.486 236.425C416.769 286.896 395.069 337.985 402.391 390.086C408.475 433.375 434.97 472.304 470.109 497.688C505.247 523.075 548.365 535.649 591.441 538.326C634.426 540.999 680.569 532.908 712.364 503.476C744.158 474.044 754.899 419.157 726.78 386.108C712.226 369.003 690.497 360.328 669.604 352.466C648.708 344.604 626.907 336.377 611.765 319.807C596.621 303.236 590.753 275.553 604.995 258.181C621.492 238.058 665.44 235.858 680.982 214.969C692.069 200.069 679.116 171.364 666.529 157.269","stroke","#00C0E0","stroke-width","2.541","stroke-miterlimit","10","stroke-dasharray","7.62 7.62"],["id","g58"],["id","path60","d","M186.221 462.671C158.423 444.172 133.639 421.035 113.173 394.475C104.595 383.341 96.7115 371.5 91.5083 358.398C86.3038 345.294 83.8862 330.794 86.4431 316.909C88.2757 306.953 93.6209 296.589 103.112 293.404C110.525 290.917 118.902 293.505 125.077 298.35C131.253 303.195 135.584 310.023 139.418 316.916C154.207 343.52 163.287 372.9 174.224 401.352C179.474 415.006 185.205 428.511 192.17 441.366C195.631 447.754 199.387 453.984 203.532 459.939C207.289 465.334 214.117 471.144 216.477 476.969C211.073 481.321 191.263 466.026 186.221 462.671Z","fill","#009D9C"],["id","g62"],["id","path64","d","M107.952 308.508C121.544 366.877 153.477 420.713 197.968 460.267","stroke","#00BBBF","stroke-width","2.02","stroke-miterlimit","10"],["id","g66"],["id","path68","d","M556.282 462.962C580.155 451.221 602.114 435.493 621.004 416.609C628.922 408.693 636.362 400.145 641.81 390.319C647.257 380.493 650.64 369.27 650.028 358.018C649.587 349.946 646.41 341.19 639.223 337.682C633.608 334.942 626.717 336.117 621.339 339.307C615.961 342.497 611.841 347.447 608.109 352.504C593.705 372.014 583.539 394.316 571.997 415.691C566.459 425.947 560.553 436.037 553.736 445.484C550.349 450.177 546.746 454.716 542.861 458.995C539.341 462.875 533.349 466.761 530.891 471.124C534.727 475.129 551.952 465.092 556.282 462.962Z","fill","#009D9C"],["id","g70"],["id","path72","d","M633.861 349.129C617.182 393.899 586.452 433.173 547.233 459.836","stroke","#00BBBF","stroke-width","1.612","stroke-miterlimit","10"],["id","g74"],["id","path76","d","M198.233 424.458C213.177 349.774 197.247 269.251 155.048 206.17","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g78"],["id","path80","d","M159.471 213.554C147.424 209.56 136.887 201.07 130.331 190.079C123.775 179.087 121.256 165.687 123.366 153.024C136.148 156.495 148.154 164.541 154.962 176.037C161.771 187.536 162.465 200.493 159.471 213.554Z","fill","#11ABBA"],["id","g82"],["id","path84","d","M172.923 237.731C170.163 228.217 170.886 217.71 174.922 208.676C178.958 199.643 186.273 192.157 195.149 187.981C198.557 197.74 198.756 208.999 194.512 218.417C190.269 227.834 182.434 233.949 172.923 237.731Z","fill","#11ABBA"],["id","g86"],["id","path88","d","M173.775 236.831C166.404 230.308 156.684 226.574 146.897 226.504C137.11 226.434 127.338 230.03 119.876 236.447C127.196 243.672 137.206 248.568 147.423 248.608C157.641 248.647 166.403 243.999 173.775 236.831Z","fill","#11ABBA"],["id","g90"],["id","path92","d","M188.104 276.094C187.024 266.239 189.542 256.02 195.07 247.837C200.597 239.655 209.088 233.576 218.546 231.029C220.225 241.241 218.483 252.363 212.686 260.887C206.887 269.41 198.122 274.049 188.104 276.094Z","fill","#11ABBA"],["id","g94"],["id","path96","d","M189.099 275.358C182.962 267.634 174.033 262.24 164.408 260.443C154.782 258.647 144.542 260.463 136.091 265.464C142.057 273.87 151.07 280.459 161.124 282.301C171.179 284.145 180.606 281.115 189.099 275.358Z","fill","#11ABBA"],["id","g98"],["id","path100","d","M198.154 314.469C197.924 304.556 201.31 294.598 207.521 286.933C213.729 279.267 222.71 273.961 232.351 272.257C233.146 282.578 230.456 293.504 223.948 301.485C217.439 309.467 208.308 313.315 198.154 314.469Z","fill","#11ABBA"],["id","g102"],["id","path104","d","M199.208 313.823C193.758 305.586 185.324 299.426 175.891 296.789C166.457 294.15 156.099 295.057 147.252 299.294C152.471 308.194 160.885 315.553 170.744 318.274C180.602 320.997 190.253 318.808 199.208 313.823Z","fill","#11ABBA"],["id","g106"],["id","path108","d","M203.971 356.696C205.264 346.866 210.136 337.563 217.445 330.968C224.754 324.372 234.439 320.543 244.225 320.378C243.428 330.699 239.095 341.071 231.443 347.929C223.789 354.789 214.179 357.154 203.971 356.696Z","fill","#11ABBA"],["id","g110"],["id","path112","d","M205.112 356.224C200.99 347.23 193.605 339.817 184.689 335.725C175.775 331.635 165.404 330.9 156.012 333.694C159.806 343.307 166.988 351.901 176.31 356.142C185.632 360.381 195.5 359.74 205.112 356.224Z","fill","#11ABBA"],["id","g114"],["id","path116","d","M546.285 450.207C530.11 375.786 544.71 295.004 585.861 231.219","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g118"],["id","path120","d","M581.562 238.676C593.54 234.478 603.937 225.811 610.312 214.71C616.685 203.608 618.983 190.168 616.663 177.542C603.94 181.23 592.069 189.476 585.452 201.088C578.835 212.7 578.354 225.668 581.562 238.676Z","fill","#11ABBA"],["id","g122"],["id","path124","d","M568.512 263.078C571.114 253.518 570.219 243.024 566.033 234.06C561.85 225.096 554.412 217.737 545.469 213.71C542.22 223.525 542.208 234.787 546.607 244.131C551.006 253.476 558.939 259.457 568.512 263.078Z","fill","#11ABBA"],["id","g126"],["id","path128","d","M567.646 262.192C574.907 255.545 584.566 251.647 594.349 251.411C604.134 251.175 613.963 254.605 621.528 260.895C614.331 268.242 604.403 273.308 594.187 273.52C583.972 273.732 575.135 269.234 567.646 262.192Z","fill","#11ABBA"],["id","g130"],["id","path132","d","M553.965 301.692C554.883 291.82 552.196 281.645 546.534 273.556C540.872 265.469 532.283 259.535 522.783 257.148C521.274 267.388 523.198 278.478 529.136 286.902C535.074 295.328 543.915 299.817 553.965 301.692Z","fill","#11ABBA"],["id","g134"],["id","path136","d","M552.959 300.973C558.968 293.147 567.807 287.6 577.401 285.642C586.995 283.683 597.263 285.324 605.795 290.182C599.97 298.687 591.066 305.428 581.044 307.441C571.021 309.454 561.546 306.585 552.959 300.973Z","fill","#11ABBA"],["id","g138"],["id","path140","d","M544.55 340.232C544.617 330.317 541.066 320.416 534.731 312.857C528.396 305.299 519.329 300.144 509.661 298.606C509.036 308.939 511.905 319.818 518.546 327.687C525.186 335.556 534.379 339.25 544.55 340.232Z","fill","#11ABBA"],["id","g142"],["id","path144","d","M543.486 339.603C548.799 331.276 557.13 324.975 566.519 322.176C575.908 319.378 586.279 320.109 595.196 324.198C590.124 333.185 581.833 340.685 572.021 343.571C562.207 346.46 552.522 344.437 543.486 339.603Z","fill","#11ABBA"],["id","g146"],["id","path148","d","M539.431 382.551C537.978 372.745 532.951 363.525 525.535 357.055C518.117 350.586 508.371 346.92 498.585 346.921C499.551 357.227 504.053 367.523 511.819 374.253C519.583 380.981 529.232 383.182 539.431 382.551Z","fill","#11ABBA"],["id","g150"],["id","path152","d","M538.282 382.098C542.255 373.036 549.518 365.498 558.363 361.259C567.21 357.016 577.568 356.105 587.003 358.74C583.369 368.417 576.328 377.13 567.079 381.527C557.828 385.925 547.95 385.452 538.282 382.098Z","fill","#11ABBA"],["id","g154"],["id","path156","d","M186.615 500.321C190.696 492.791 196.119 485.823 199.682 478.076C190.178 465.849 178.777 454.862 166.819 445.23C159.004 438.931 150.847 433.032 142.419 427.531C134.688 433.762 126.957 439.994 119.225 446.225C120.579 435.351 121.356 425.888 122.482 415.574C105.313 406.143 87.2411 398.331 68.6211 392.377C64.3289 399.386 60.6691 406.825 54.8967 412.829C54.9847 404.798 54.2249 396.412 53.1469 387.893C35.9349 383.405 18.3639 380.482 0.707452 379.308C0.649609 386.531 1.06635 393.746 1.88798 400.912C6.50223 399.507 10.074 395.563 14.9604 394.821C11.7383 402.728 8.82513 411.421 4.99044 419.449C9.19717 438.521 16.3959 456.93 26.2186 473.763C34.3468 468.915 41.9636 462.248 51.7627 458.125C50.0576 473.301 50.0274 489.179 48.7351 504.527C53.8963 510.215 59.4097 515.573 65.2741 520.527C75.5977 529.245 86.9217 536.691 98.9201 542.791C101.353 533.385 103.872 524.016 109.898 516.114C116.996 529.781 124.688 541.96 131.128 555.467C157.986 563.194 186.571 564.779 214.002 559.454C218.542 558.574 222.349 551.211 223.76 546.749C225.172 542.289 224.898 537.468 224.262 532.827C222.26 518.237 216.907 504.646 209.377 492.145C201.36 494.069 193.248 496.332 186.615 500.321Z","fill","#11ABBA"],["id","g158"],["id","path160","d","M194.298 545.299C131.158 507.676 73.43 460.749 23.4922 406.451","stroke","#55CDE2","stroke-width","2.541","stroke-miterlimit","10"],["id","g162"],["id","path164","d","M559.699 515.384C555.868 510.221 551.098 505.622 547.63 500.242C553.415 490.113 560.744 480.704 568.626 472.241C573.781 466.709 579.23 461.436 584.922 456.429C591.334 460.232 597.744 464.032 604.155 467.835C602.002 459.887 600.425 452.929 598.502 445.374C610.285 436.498 622.913 428.73 636.143 422.286C640.078 427.037 643.584 432.176 648.514 436.023C647.601 430.055 647.283 423.73 647.188 417.273C659.526 412.073 672.295 407.997 685.312 405.212C686.116 410.582 686.566 415.998 686.708 421.42C683.127 420.873 680.053 418.324 676.338 418.3C679.571 423.837 682.654 429.991 686.353 435.55C685.232 450.201 681.815 464.681 676.277 478.269C669.715 475.541 663.346 471.403 655.618 469.394C658.486 480.504 660.182 492.319 662.759 503.602C659.518 508.393 655.978 512.977 652.135 517.298C645.372 524.903 637.727 531.67 629.441 537.508C626.639 530.772 623.778 524.07 618.459 518.842C614.616 529.781 610.176 539.676 606.805 550.427C587.63 559.082 566.522 563.353 545.546 562.358C542.075 562.193 538.466 557.123 536.945 553.957C535.425 550.79 535.121 547.171 535.105 543.651C535.058 532.573 537.61 521.88 541.897 511.761C548.065 512.326 554.342 513.133 559.699 515.384Z","fill","#11ABBA"],["id","g166"],["id","path168","d","M558.719 549.691C601.746 514.86 639.767 473.689 671.212 427.878","stroke","#55CDE2","stroke-width","1.91","stroke-miterlimit","10"],["id","g170"],["id","path172","d","M554.113 562.578H187.856C180.008 562.578 173.645 556.132 173.645 548.18V310.114C173.645 302.163 180.008 295.717 187.856 295.717H554.113C561.963 295.717 568.324 302.163 568.324 310.114V548.18C568.324 556.132 561.963 562.578 554.113 562.578Z","fill","#060C37"],["id","g174"],["id","path176","d","M563.719 429.147C563.719 435.866 558.342 441.314 551.71 441.314C545.078 441.314 539.701 435.866 539.701 429.147C539.701 422.427 545.078 416.981 551.71 416.981C558.342 416.981 563.719 422.427 563.719 429.147Z","fill","#111E65"],["id","g178"],["id","path180","d","M182.05 474.266C179.95 474.266 178.247 472.542 178.247 470.413V387.882C178.247 385.753 179.95 384.028 182.05 384.028C184.151 384.028 185.854 385.753 185.854 387.882V470.413C185.854 472.542 184.151 474.266 182.05 474.266Z","fill","#111E65"],["id","path182","d","M535.104 552.722H191.254V305.564H535.104V552.722Z","fill","#D8E9F5"],["id","path184","d","M535.1 322.18H191.256V305.568H535.1V322.18Z","fill","#A4B1BA"],["id","path186","d","M201.252 320.17H196.898V314.56H201.252V320.17Z","fill","#FF6044"],["id","path188","d","M206.906 320.17H202.552V310.653H206.906V320.17Z","fill","#FF6044"],["id","path190","d","M212.886 320.17H208.532V307.952H212.886V320.17Z","fill","#FF6044"],["id","g192"],["id","path194","d","M507.781 308.957V309.767C507.781 310.411 507.264 310.933 506.629 310.933H505.346C504.711 310.933 504.196 311.455 504.196 312.099V315.647C504.196 316.293 504.711 316.814 505.346 316.814H506.629C507.264 316.814 507.781 317.336 507.781 317.979V318.792C507.781 319.435 508.296 319.957 508.931 319.957H526.844C527.479 319.957 527.995 319.435 527.995 318.792V308.957C527.995 308.313 527.479 307.791 526.844 307.791H508.931C508.296 307.791 507.781 308.313 507.781 308.957Z","fill","#D8E9F5"],["id","g196"],["id","path198","d","M526.894 319.341H523.692C523.458 319.341 523.267 319.148 523.267 318.909V308.824C523.267 308.584 523.458 308.391 523.692 308.391H526.894C527.13 308.391 527.32 308.584 527.32 308.824V318.909C527.32 319.148 527.13 319.341 526.894 319.341Z","fill","#92FC28"],["id","g200"],["id","path202","d","M521.94 319.341H518.739C518.505 319.341 518.313 319.148 518.313 318.909V308.824C518.313 308.584 518.505 308.391 518.739 308.391H521.94C522.175 308.391 522.366 308.584 522.366 308.824V318.909C522.366 319.148 522.175 319.341 521.94 319.341Z","fill","#92FC28"],["id","g204"],["id","path206","d","M516.987 319.341H513.785C513.551 319.341 513.36 319.148 513.36 318.909V308.824C513.36 308.584 513.551 308.391 513.785 308.391H516.987C517.223 308.391 517.413 308.584 517.413 308.824V318.909C517.413 319.148 517.223 319.341 516.987 319.341Z","fill","#92FC28"],["id","g208"],["id","path210","d","M498.8 313.874C498.8 316.456 496.733 318.551 494.183 318.551C491.635 318.551 489.569 316.456 489.569 313.874C489.569 311.292 491.635 309.197 494.183 309.197C496.733 309.197 498.8 311.292 498.8 313.874Z","fill","#D8E9F5"],["id","path212","d","M513.36 533.681H212.999V340.836H513.36V533.681Z","fill","#C0CFDA"],["id","path214","d","M513.36 357.464H212.999V340.838H513.36V357.464Z","fill","#A4B3BC"],["id","path216","d","M507.28 373.991H310.642V366.083H507.28V373.991Z","fill","#DCEEFB"],["id","path218","d","M419.169 389.046H310.642V381.138H419.169V389.046Z","fill","#DCEEFB"],["id","path220","d","M369.032 404.104H310.642V396.196H369.032V404.104Z","fill","#DCEEFB"],["id","path222","d","M507.28 430.213H310.642V422.305H507.28V430.213Z","fill","#DCEEFB"],["id","path224","d","M419.169 445.268H310.642V437.36H419.169V445.268Z","fill","#DCEEFB"],["id","path226","d","M369.032 460.325H310.642V452.418H369.032V460.325Z","fill","#DCEEFB"],["id","path228","d","M507.28 485.114H310.642V477.206H507.28V485.114Z","fill","#DCEEFB"],["id","path230","d","M419.169 500.172H310.642V492.264H419.169V500.172Z","fill","#DCEEFB"],["id","path232","d","M369.032 515.228H310.642V507.32H369.032V515.228Z","fill","#DCEEFB"],["id","path234","d","M301.035 409.578H224.781V366.082H301.035V409.578Z","fill","#DCEEFB"],["id","g236"],["id","path238","d","M224.781 409.579L262.908 387.831L301.034 409.579H224.781Z","fill","#CADBE7"],["id","g240"],["id","path242","d","M301.034 366.082L262.908 387.83L224.781 366.082H301.034Z","fill","#CADBE7"],["id","path244","d","M301.035 465.546H224.781V422.05H301.035V465.546Z","fill","#DCEEFB"],["id","g246"],["id","path248","d","M224.781 465.546L262.908 443.798L301.034 465.546H224.781Z","fill","#CADBE7"],["id","g250"],["id","path252","d","M301.034 422.05L262.908 443.798L224.781 422.05H301.034Z","fill","#CADBE7"],["id","path254","d","M301.035 521.515H224.781V478.019H301.035V521.515Z","fill","#DCEEFB"],["id","g256"],["id","path258","d","M224.781 521.514L262.908 499.766L301.034 521.514H224.781Z","fill","#CADBE7"],["id","g260"],["id","path262","d","M301.034 478.018L262.908 499.766L224.781 478.018H301.034Z","fill","#CADBE7"],["id","g264"],["id","g282"],["id","g280","opacity","0.440002"],["id","g274","opacity","0.440002"],["id","path272","opacity","0.440002","d","M314.124 305.565L191.254 430.069V321.271L206.769 305.565H314.124Z","fill","white"],["id","g278","opacity","0.440002"],["id","path276","opacity","0.440002","d","M388.697 305.565L191.254 505.613V449.961L333.77 305.565H388.697Z","fill","white"],["id","g284"],["id","g302"],["id","g300","opacity","0.440002"],["id","g294","opacity","0.440002"],["id","path292","opacity","0.440002","d","M535.104 332.465V441.249L425.071 552.723H317.715L535.104 332.465Z","fill","white"],["id","g298","opacity","0.440002"],["id","path296","opacity","0.440002","d","M535.104 461.142V516.794L499.632 552.723H444.716L535.104 461.142Z","fill","white"],["id","envelope"],["id","g304"],["id","path306","d","M249.266 298.798L351.208 218.764C357.652 213.705 366.657 213.705 373.102 218.764L475.045 298.798V432.924H249.266V298.798Z","fill","#FF9004"],["id","path308","d","M448.926 227.706H275.382V421.076H448.926V227.706Z","fill","#FAFAFA"],["id","path310","d","M438.481 239.346H285.831V245.241H438.481V239.346Z","fill","#DCDCDC"],["id","path312","d","M415.561 251.195H285.831V257.09H415.561V251.195Z","fill","#DCDCDC"],["id","path314","d","M394.51 263.044H285.831V268.939H394.51V263.044Z","fill","#DCDCDC"],["id","path316","d","M394.51 285.792H285.831V291.688H394.51V285.792Z","fill","#DCDCDC"],["id","path318","d","M366.443 297.167H285.831V303.062H366.443V297.167Z","fill","#DCDCDC"],["id","path320","d","M442.769 321H362.156V326.896H442.769V321Z","fill","#DCDCDC"],["id","path322","d","M442.768 332.609H377.201V338.504H442.768V332.609Z","fill","#DCDCDC"],["id","g324"],["id","path326","d","M362.155 365.9L249.265 298.877V432.924L362.155 365.9Z","fill","#FFAE35"],["id","g328"],["id","path330","d","M362.156 365.9L475.045 298.877V432.924L362.156 365.9Z","fill","#FFAE35"],["id","g332"],["id","path334","d","M351.209 352.89L249.267 432.924H475.044L373.102 352.89C366.658 347.831 357.652 347.831 351.209 352.89Z","fill","#FFBF69"],["id","g348"],["id","path350","d","M185.705 159.357C185.994 158.402 185.854 157.315 185.28 156.095C184.719 154.898 183.98 154.112 183.067 153.736C182.152 153.361 181.213 153.405 180.251 153.868C179.287 154.333 178.667 155.04 178.388 155.99C178.109 156.941 178.251 158.015 178.813 159.212C179.375 160.409 180.11 161.203 181.02 161.595C181.927 161.986 182.863 161.951 183.826 161.487C184.789 161.022 185.415 160.312 185.705 159.357ZM184.018 139.899C186.987 140.019 189.648 140.858 192.003 142.415C194.358 143.972 196.169 146.103 197.439 148.81C198.376 150.805 198.868 152.668 198.915 154.398C198.964 156.13 198.62 157.627 197.886 158.892C197.151 160.158 196.083 161.127 194.682 161.803C193.522 162.361 192.412 162.597 191.351 162.51C190.29 162.423 189.34 161.997 188.499 161.234C188.332 163.679 187.01 165.499 184.538 166.691C183.247 167.313 181.88 167.543 180.435 167.382C178.991 167.222 177.639 166.671 176.378 165.728C175.116 164.786 174.101 163.494 173.331 161.853C172.56 160.212 172.207 158.602 172.27 157.021C172.334 155.441 172.761 154.037 173.555 152.812C174.35 151.588 175.402 150.658 176.716 150.026C178.642 149.097 180.458 148.996 182.169 149.723L181.404 148.093L186.755 145.514L191.517 155.66C191.851 156.368 192.222 156.816 192.63 157C193.038 157.183 193.46 157.17 193.898 156.96C195.278 156.295 195.16 154.244 193.547 150.807C192.558 148.7 191.191 147.062 189.448 145.89C187.703 144.718 185.729 144.098 183.524 144.033C181.32 143.967 179.056 144.493 176.737 145.61C174.438 146.718 172.631 148.201 171.317 150.058C170.001 151.916 169.265 153.963 169.108 156.2C168.949 158.438 169.386 160.655 170.416 162.85C171.468 165.09 172.892 166.864 174.687 168.175C176.483 169.485 178.493 170.207 180.719 170.346C182.943 170.483 185.205 169.998 187.503 168.891C189.845 167.763 191.793 166.226 193.349 164.28L196.235 167.254C195.479 168.216 194.473 169.176 193.219 170.134C191.964 171.092 190.636 171.908 189.237 172.583C186.063 174.113 182.955 174.794 179.912 174.629C176.868 174.464 174.138 173.537 171.722 171.846C169.304 170.157 167.414 167.859 166.05 164.954C164.698 162.072 164.144 159.149 164.393 156.189C164.639 153.228 165.671 150.494 167.487 147.988C169.301 145.481 171.807 143.458 175.003 141.918C178.045 140.453 181.05 139.779 184.018 139.899Z","fill","#ADE0EC"],["id","g352"],["id","path354","d","M478.281 145.979L473.499 145.088L471.809 150.637L476.591 151.528L478.281 145.979ZM483.567 146.965L481.877 152.514L486.737 153.418L485.812 158.499L480.333 157.478L478.528 163.209L473.241 162.224L475.046 156.492L470.263 155.601L468.46 161.331L463.174 160.347L464.977 154.616L460.079 153.702L461.001 148.622L466.522 149.65L468.214 144.102L463.314 143.19L464.237 138.109L469.759 139.138L471.562 133.407L476.848 134.393L475.043 140.124L479.826 141.015L481.629 135.284L486.917 136.269L485.112 142.001L490.01 142.913L489.088 147.994L483.567 146.965Z","fill","#ADE0EC"],["id","g356"],["id","path358","d","M230.094 489.727H164.645C144.782 489.727 128.679 473.412 128.679 453.286C128.679 433.159 144.782 416.844 164.645 416.844H194.128C213.99 416.844 230.094 433.159 230.094 453.286V489.727Z","fill","#FFBF69"],["id","g360"],["id","path362","d","M190.288 474.567C192.225 471.057 193.491 467.457 194.24 463.884C197.265 463.216 199.718 462.418 201.535 461.712C199.468 467.269 195.439 471.849 190.288 474.567ZM173.549 476.516C170.414 472.301 168.399 468.049 167.204 463.913C172.228 464.889 176.849 465.295 180.987 465.295C184.501 465.295 187.666 465.013 190.478 464.585C189.44 468.665 187.643 472.75 184.795 476.628C183.054 477.042 181.249 477.283 179.386 477.283C177.368 477.283 175.42 476.999 173.549 476.516ZM157.077 461.27C159.25 461.983 161.355 462.573 163.406 463.075C164.255 466.725 165.672 470.467 167.822 474.207C162.852 471.377 159.006 466.783 157.077 461.27ZM166.919 432.92C165.905 435.193 164.777 438.165 163.89 441.631C161.455 442.199 159.416 442.847 157.807 443.446C159.751 439.087 162.942 435.428 166.919 432.92ZM185.694 430.179C186.289 431.348 188.269 435.45 189.79 441.13C180.926 439.619 173.434 439.938 167.6 440.897C169.168 435.61 171.267 431.824 172.077 430.47C174.382 429.71 176.835 429.288 179.386 429.288C181.572 429.288 183.682 429.614 185.694 430.179ZM201.203 443.946C198.569 443.098 196.02 442.407 193.568 441.864C192.612 437.856 191.394 434.47 190.4 432.058C195.218 434.635 199.063 438.835 201.203 443.946ZM194.354 445.71C196.968 446.339 199.688 447.138 202.507 448.133C202.868 449.796 203.071 451.515 203.071 453.285C203.071 454.669 202.929 456.014 202.707 457.334C201.441 457.942 198.765 459.081 194.862 460.045C195.44 454.989 195.108 450.091 194.354 445.71ZM166.64 444.734C172.634 443.581 180.793 443.047 190.668 444.909C191.612 449.668 192.068 455.159 191.237 460.804C184.963 461.903 176.497 462.275 166.311 460.097C165.321 454.509 165.701 449.252 166.64 444.734ZM155.701 453.285C155.701 451.44 155.927 449.649 156.319 447.921C157.561 447.343 159.839 446.402 163.05 445.549C162.325 449.694 162.056 454.341 162.712 459.24C160.557 458.67 158.328 457.976 156.039 457.161C155.835 455.896 155.701 454.608 155.701 453.285ZM179.386 425.733C164.391 425.733 152.192 438.093 152.192 453.285C152.192 468.479 164.391 480.838 179.386 480.838C194.381 480.838 206.58 468.479 206.58 453.285C206.58 438.093 194.381 425.733 179.386 425.733Z","fill","#FAFAFA"],["id","g364"],["id","path366","d","M487.575 534.716H553.024C572.888 534.716 588.99 518.4 588.99 498.275C588.99 478.149 572.888 461.834 553.024 461.834H523.541C503.679 461.834 487.575 478.149 487.575 498.275V534.716Z","fill","#FFBF69"],["id","g368"],["id","path370","d","M565.214 487.805C565.214 477.497 549.034 468.633 538.283 477.531C527.532 468.633 511.352 477.497 511.352 487.805C511.352 487.805 507.872 508.014 538.283 522.676C568.694 508.014 565.214 487.805 565.214 487.805Z","stroke","#FAFAFA","stroke-width","3.811","stroke-miterlimit","10","stroke-linejoin","round"],["id","g372"],["id","path374","d","M466.093 53.4869C465.677 53.3258 465.259 53.1899 464.843 53.074C464.729 52.6558 464.594 52.2389 464.437 51.8207C463.767 50.1411 462.888 48.4615 461.12 46.7819C459.352 48.4615 458.474 50.1411 457.804 51.8207C457.645 52.2415 457.51 52.6638 457.395 53.0847C456.978 53.2019 456.563 53.3391 456.147 53.4989C454.489 54.1782 452.832 55.0679 451.174 56.8594C452.832 58.6509 454.489 59.5406 456.147 60.2199C456.56 60.3797 456.973 60.5156 457.384 60.6315C457.499 61.0537 457.633 61.4759 457.792 61.8982C458.46 63.5777 459.342 65.2573 461.12 66.9369C462.899 65.2573 463.781 63.5777 464.449 61.8982C464.605 61.4799 464.741 61.0617 464.855 60.6421C465.267 60.5276 465.681 60.3917 466.093 60.2319C467.751 59.5553 469.409 58.6615 471.067 56.8594C469.409 55.0573 467.751 54.1635 466.093 53.4869Z","fill","#ADE0EC"],["id","star1"],["id","path378","d","M18.666 335.315C18.2493 335.154 17.8325 335.016 17.4145 334.901C17.3001 334.484 17.166 334.067 17.0096 333.649C16.3392 331.968 15.461 330.289 13.6929 328.61C11.9247 330.289 11.0466 331.968 10.3761 333.649C10.2171 334.069 10.0816 334.492 9.96728 334.913C9.55186 335.028 9.13514 335.167 8.71972 335.327C7.06201 336.006 5.4043 336.896 3.74658 338.687C5.4043 340.479 7.06201 341.369 8.71972 342.048C9.13251 342.206 9.54398 342.342 9.95676 342.458C10.0698 342.882 10.2052 343.304 10.3643 343.725C11.0321 345.406 11.9142 347.085 13.6929 348.765C15.4715 347.085 16.3536 345.406 17.0214 343.725C17.1779 343.308 17.3133 342.89 17.4263 342.47C17.8391 342.354 18.2532 342.22 18.666 342.058C20.3237 341.383 21.9814 340.489 23.6391 338.687C21.9814 336.885 20.3237 335.991 18.666 335.315Z","fill","#ADE0EC"],["id","g380"],["id","path382","d","M500.378 253.717C499.962 253.558 499.545 253.42 499.128 253.305C499.014 252.886 498.878 252.469 498.722 252.052C498.052 250.372 497.173 248.692 495.405 247.012C493.637 248.692 492.759 250.372 492.089 252.052C491.931 252.472 491.795 252.894 491.681 253.317C491.264 253.432 490.849 253.57 490.433 253.729C488.774 254.409 487.117 255.298 485.459 257.09C487.117 258.881 488.774 259.772 490.433 260.45C490.845 260.61 491.258 260.746 491.669 260.862C491.784 261.284 491.918 261.706 492.078 262.129C492.745 263.808 493.627 265.488 495.405 267.167C497.184 265.488 498.066 263.808 498.734 262.129C498.892 261.71 499.026 261.292 499.14 260.874C499.553 260.758 499.966 260.622 500.378 260.462C502.037 259.786 503.694 258.892 505.352 257.09C503.694 255.289 502.037 254.395 500.378 253.717Z","fill","#ADE0EC"],["id","g384"],["id","path386","d","M673.413 79.5778C673.204 79.4978 672.995 79.4286 672.785 79.3713C672.729 79.1622 672.662 78.9517 672.583 78.7426C672.246 77.9008 671.806 77.059 670.921 76.2172C670.035 77.059 669.595 77.9008 669.258 78.7426C669.178 78.9544 669.112 79.1648 669.054 79.3766C668.844 79.4352 668.636 79.5032 668.429 79.5844C667.596 79.9241 666.766 80.3703 665.936 81.2693C666.766 82.1657 667.596 82.6119 668.429 82.9529C668.635 83.0328 668.84 83.1008 669.048 83.158C669.106 83.3698 669.173 83.5816 669.253 83.7947C669.587 84.6352 670.03 85.4769 670.921 86.3201C671.811 85.4769 672.254 84.6352 672.589 83.7947C672.668 83.5842 672.734 83.3738 672.792 83.1647C672.999 83.1061 673.206 83.0382 673.413 82.9596C674.244 82.6199 675.075 82.1711 675.906 81.2693C675.075 80.3649 674.244 79.9174 673.413 79.5778Z","fill","#D0F6FF"],["id","g388"],["id","path390","d","M724.621 229.528C724.413 229.448 724.204 229.379 723.994 229.321C723.936 229.112 723.87 228.902 723.791 228.694C723.455 227.851 723.014 227.009 722.128 226.167C721.244 227.009 720.803 227.851 720.467 228.694C720.387 228.904 720.32 229.115 720.262 229.327C720.053 229.385 719.845 229.453 719.636 229.534C718.805 229.874 717.974 230.32 717.145 231.219C717.974 232.116 718.805 232.562 719.636 232.903C719.842 232.983 720.049 233.051 720.256 233.108C720.314 233.32 720.38 233.532 720.46 233.745C720.795 234.585 721.238 235.427 722.128 236.27C723.02 235.427 723.461 234.585 723.797 233.745C723.877 233.534 723.943 233.324 723.999 233.115C724.208 233.056 724.415 232.988 724.621 232.91C725.453 232.57 726.284 232.121 727.113 231.219C726.284 230.315 725.453 229.867 724.621 229.528Z","fill","#D0F6FF"],["id","g392"],["id","path394","d","M722.669 226.015C722.46 225.935 722.251 225.866 722.042 225.809C721.984 225.6 721.918 225.389 721.838 225.18C721.503 224.338 721.063 223.497 720.177 222.655C719.291 223.497 718.85 224.338 718.515 225.18C718.435 225.392 718.368 225.602 718.31 225.814C718.101 225.873 717.892 225.941 717.684 226.022C716.853 226.362 716.022 226.808 715.192 227.707C716.022 228.603 716.853 229.049 717.684 229.39C717.891 229.47 718.097 229.538 718.305 229.595C718.361 229.807 718.428 230.019 718.508 230.232C718.844 231.073 719.285 231.914 720.177 232.758C721.068 231.914 721.51 231.073 721.845 230.232C721.924 230.022 721.991 229.811 722.047 229.602C722.255 229.544 722.463 229.476 722.669 229.397C723.5 229.057 724.331 228.609 725.162 227.707C724.331 226.802 723.5 226.355 722.669 226.015Z","fill","#D0F6FF"],["id","g396"],["id","path398","d","M122.37 271.837C122.161 271.756 121.952 271.688 121.742 271.63C121.686 271.421 121.619 271.211 121.54 271.002C121.203 270.16 120.763 269.318 119.877 268.476C118.991 269.318 118.551 270.16 118.215 271.002C118.135 271.213 118.068 271.424 118.01 271.636C117.801 271.694 117.594 271.762 117.385 271.842C116.554 272.183 115.723 272.629 114.892 273.527C115.723 274.425 116.554 274.871 117.385 275.212C117.591 275.291 117.797 275.36 118.005 275.417C118.062 275.629 118.129 275.841 118.209 276.052C118.544 276.894 118.986 277.736 119.877 278.578C120.768 277.736 121.211 276.894 121.545 276.052C121.624 275.843 121.691 275.633 121.748 275.422C121.955 275.365 122.163 275.297 122.37 275.217C123.2 274.878 124.031 274.43 124.862 273.527C124.031 272.624 123.2 272.176 122.37 271.837Z","fill","#ADE0EC"],["id","g400"],["id","path402","d","M30.9696 538.087C30.7606 538.007 30.5516 537.939 30.3426 537.881C30.2847 537.671 30.219 537.461 30.1401 537.252C29.8036 536.41 29.3632 535.568 28.4772 534.728C27.5911 535.568 27.1507 536.41 26.8155 537.252C26.7353 537.464 26.6683 537.674 26.6104 537.887C26.4014 537.945 26.1937 538.012 25.9847 538.094C25.1538 538.435 24.323 538.881 23.4922 539.779C24.323 540.675 25.1538 541.121 25.9847 541.462C26.1911 541.542 26.3975 541.611 26.6052 541.667C26.6617 541.88 26.7301 542.092 26.8089 542.303C27.1442 543.146 27.5859 543.988 28.4772 544.829C29.3685 543.988 29.8115 543.146 30.1454 542.303C30.2243 542.094 30.2913 541.884 30.3478 541.674C30.5555 541.615 30.7633 541.549 30.9696 541.468C31.8005 541.128 32.6313 540.68 33.4621 539.779C32.6313 538.876 31.8005 538.427 30.9696 538.087Z","fill","#ADE0EC"],["id","g404"],["id","path406","d","M384.68 138.195C384.471 138.114 384.262 138.046 384.053 137.989C383.995 137.78 383.928 137.569 383.849 137.36C383.514 136.518 383.073 135.676 382.187 134.835C381.301 135.676 380.861 136.518 380.524 137.36C380.445 137.572 380.377 137.782 380.32 137.994C380.111 138.053 379.904 138.121 379.695 138.202C378.864 138.541 378.033 138.988 377.202 139.885C378.033 140.783 378.864 141.229 379.695 141.57C379.901 141.65 380.107 141.718 380.314 141.775C380.372 141.987 380.439 142.199 380.519 142.411C380.854 143.253 381.296 144.094 382.187 144.936C383.078 144.094 383.52 143.253 383.855 142.411C383.934 142.202 384.001 141.991 384.058 141.781C384.266 141.723 384.472 141.656 384.68 141.576C385.51 141.236 386.341 140.788 387.172 139.885C386.341 138.982 385.51 138.535 384.68 138.195Z","fill","#ADE0EC"],["id","g408"],["id","path410","d","M143.253 52.4684C143.044 52.3885 142.835 52.3192 142.626 52.262C142.568 52.0528 142.501 51.8424 142.423 51.6333C142.087 50.7915 141.646 49.9497 140.76 49.1079C139.874 49.9497 139.434 50.7915 139.097 51.6333C139.019 51.8451 138.951 52.0555 138.894 52.2673C138.685 52.3259 138.477 52.3938 138.268 52.4751C137.437 52.8147 136.606 53.2609 135.775 54.1586C136.606 55.0564 137.437 55.5026 138.268 55.8436C138.474 55.9235 138.681 55.9914 138.888 56.0487C138.945 56.2605 139.012 56.4722 139.092 56.6854C139.427 57.5258 139.869 58.3676 140.76 59.2107C141.652 58.3676 142.093 57.5258 142.429 56.6854C142.507 56.4749 142.575 56.2645 142.631 56.0553C142.839 55.9967 143.045 55.9288 143.253 55.8502C144.084 55.5106 144.915 55.0617 145.745 54.1586C144.915 53.2556 144.084 52.8081 143.253 52.4684Z","fill","#ADE0EC"],["id","star4"],["id","path414","d","M659.175 279.551C658.966 279.47 658.757 279.402 658.546 279.344C658.49 279.135 658.423 278.925 658.344 278.716C658.009 277.874 657.567 277.032 656.682 276.19C655.796 277.032 655.356 277.874 655.019 278.716C654.939 278.926 654.873 279.138 654.816 279.35C654.605 279.408 654.397 279.476 654.19 279.556C653.359 279.897 652.527 280.343 651.697 281.241C652.527 282.139 653.359 282.585 654.19 282.926C654.396 283.005 654.603 283.074 654.81 283.131C654.867 283.343 654.934 283.555 655.014 283.766C655.349 284.608 655.791 285.45 656.682 286.292C657.574 285.45 658.015 284.608 658.35 283.766C658.429 283.557 658.495 283.347 658.553 283.136C658.761 283.079 658.968 283.011 659.175 282.931C660.006 282.592 660.836 282.144 661.667 281.241C660.836 280.338 660.006 279.89 659.175 279.551Z","fill","#ADE0EC"],["id","star5"],["id","path418","d","M412.477 191.341C412.268 191.26 412.059 191.192 411.85 191.133C411.793 190.924 411.727 190.715 411.647 190.506C411.311 189.664 410.871 188.822 409.985 187.98C409.099 188.822 408.659 189.664 408.323 190.506C408.243 190.718 408.176 190.928 408.118 191.14C407.909 191.197 407.7 191.266 407.492 191.346C406.662 191.687 405.831 192.133 405 193.031C405.831 193.929 406.662 194.375 407.492 194.715C407.699 194.795 407.905 194.864 408.113 194.921C408.17 195.133 408.237 195.345 408.317 195.556C408.652 196.398 409.094 197.24 409.985 198.082C410.876 197.24 411.318 196.398 411.653 195.556C411.732 195.346 411.799 195.137 411.856 194.926C412.063 194.869 412.271 194.801 412.477 194.721C413.308 194.382 414.139 193.934 414.97 193.031C414.139 192.128 413.308 191.681 412.477 191.341Z","fill","#D0F6FF"],["id","star2"],["id","path422","d","M318.495 91.4014C318.129 91.2602 317.762 91.1403 317.396 91.0391C317.295 90.6715 317.178 90.3039 317.04 89.9363C316.45 88.4605 315.678 86.9847 314.124 85.5075C312.57 86.9847 311.797 88.4605 311.208 89.9363C311.069 90.3079 310.95 90.6782 310.85 91.0484C310.483 91.151 310.117 91.2709 309.752 91.4121C308.295 92.0088 306.837 92.792 305.381 94.3663C306.837 95.9407 308.295 96.7239 309.752 97.3206C310.115 97.4604 310.476 97.5803 310.839 97.6815C310.939 98.0531 311.059 98.4234 311.198 98.795C311.786 100.272 312.56 101.749 314.124 103.225C315.687 101.749 316.463 100.272 317.049 98.795C317.187 98.4274 317.306 98.0598 317.406 97.6922C317.77 97.5896 318.132 97.4711 318.495 97.3312C319.953 96.7358 321.41 95.95 322.868 94.3663C321.41 92.7826 319.953 91.9968 318.495 91.4014Z","fill","#ADE0EC"],["id","g424"],["id","path426","d","M95.3161 198.94C94.9494 198.801 94.5826 198.679 94.2171 198.578C94.1159 198.21 93.9989 197.843 93.8609 197.475C93.2706 195.999 92.4989 194.524 90.9451 193.047C89.3912 194.524 88.6182 195.999 88.0293 197.475C87.8899 197.847 87.7703 198.217 87.6704 198.587C87.3036 198.69 86.9382 198.81 86.5727 198.951C85.1161 199.548 83.6582 200.331 82.2017 201.905C83.6582 203.48 85.1161 204.263 86.5727 204.86C86.9355 204.999 87.2971 205.119 87.6599 205.221C87.7598 205.592 87.8794 205.962 88.0188 206.334C88.6064 207.811 89.3807 209.288 90.9451 210.764C92.5081 209.288 93.2838 207.811 93.8701 206.334C94.0081 205.966 94.1264 205.599 94.2263 205.231C94.5892 205.129 94.9533 205.01 95.3161 204.87C96.774 204.275 98.2306 203.49 99.6885 201.905C98.2306 200.322 96.774 199.536 95.3161 198.94Z","fill","#ADE0EC"],["id","star3"],["id","path430","d","M567.016 163.164C566.649 163.023 566.282 162.903 565.915 162.8C565.815 162.434 565.697 162.066 565.559 161.699C564.97 160.223 564.197 158.746 562.643 157.27C561.089 158.746 560.316 160.223 559.728 161.699C559.59 162.069 559.47 162.441 559.369 162.81C559.003 162.912 558.638 163.033 558.272 163.175C556.814 163.771 555.358 164.553 553.9 166.129C555.358 167.703 556.814 168.486 558.272 169.082C558.634 169.222 558.997 169.343 559.359 169.444C559.459 169.816 559.579 170.186 559.717 170.558C560.306 172.035 561.08 173.51 562.643 174.986C564.206 173.51 564.982 172.035 565.57 170.558C565.708 170.19 565.826 169.822 565.926 169.453C566.289 169.352 566.653 169.234 567.016 169.094C568.472 168.498 569.93 167.713 571.387 166.129C569.93 164.545 568.472 163.759 567.016 163.164Z","fill","#D0F6FF"],["id","star6"],["id","path434","d","M785.486 113.408C785.119 113.267 784.752 113.147 784.385 113.045C784.285 112.678 784.167 112.31 784.03 111.943C783.44 110.467 782.667 108.99 781.113 107.514C779.559 108.99 778.786 110.467 778.198 111.943C778.059 112.314 777.94 112.685 777.839 113.055C777.473 113.157 777.108 113.277 776.742 113.418C775.284 114.015 773.828 114.798 772.37 116.373C773.828 117.947 775.284 118.73 776.742 119.327C777.104 119.467 777.467 119.587 777.829 119.688C777.929 120.06 778.049 120.43 778.187 120.801C778.776 122.279 779.55 123.756 781.113 125.231C782.676 123.756 783.452 122.279 784.04 120.801C784.178 120.434 784.296 120.066 784.396 119.697C784.759 119.596 785.123 119.477 785.486 119.338C786.942 118.742 788.4 117.956 789.857 116.373C788.4 114.789 786.942 114.003 785.486 113.408Z","fill","#D0F6FF"],["id","g436"],["id","path438","d","M556.27 45.0362C555.903 44.895 555.536 44.7752 555.169 44.6739C555.069 44.3063 554.951 43.9387 554.813 43.5711C554.224 42.0953 553.451 40.6182 551.897 39.1424C550.343 40.6182 549.57 42.0953 548.983 43.5711C548.843 43.9427 548.724 44.313 548.624 44.6833C548.257 44.7858 547.892 44.9057 547.526 45.0469C546.068 45.6436 544.612 46.4268 543.154 48.0011C544.612 49.5755 546.068 50.3587 547.526 50.9554C547.888 51.0953 548.251 51.2151 548.613 51.3164C548.713 51.688 548.833 52.0583 548.971 52.4299C549.56 53.907 550.334 55.3841 551.897 56.8599C553.46 55.3841 554.236 53.907 554.824 52.4299C554.962 52.0622 555.08 51.6946 555.18 51.327C555.543 51.2245 555.907 51.1059 556.27 50.9661C557.726 50.3707 559.184 49.5848 560.641 48.0011C559.184 46.4175 557.726 45.6316 556.27 45.0362Z","fill","#D0F6FF"],["id","contact-form","action","https://formspree.io/f/mldrlygg","method","POST",3,"submit"],[1,"title","text-center","mb-4"],[1,"form-group","position-relative"],["for","name",1,"d-block"],["data-feather","user",1,"icon"],["type","text","id","name","name","name","placeholder","Name","required","",1,"form-control","form-control-lg","thick"],["for","email",1,"d-block"],["data-feather","mail",1,"icon"],["type","email","id","email","name","email","placeholder","E-mail","required","",1,"form-control","form-control-lg","thick"],[1,"form-group","message"],["id","message","name","message","rows","7","placeholder","Message","required","",1,"form-control","form-control-lg"],[1,"text-center"],["type","submit",1,"btn","btn-primary"],[3,"ngClass",4,"ngIf"],[3,"ngClass"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"h2",1),S(2,"Contact me "),h()(),f(3,"div",2),or(),f(4,"svg",3)(5,"g",4)(6,"g",5)(7,"g",6)(8,"g",7),g(9,"path",8),h(),f(10,"g",9),g(11,"path",10),h(),f(12,"g",11),g(13,"path",12),h(),f(14,"g",13),g(15,"path",14),h(),f(16,"g",15),g(17,"path",16),h(),f(18,"g",17),g(19,"path",18),h(),f(20,"g",19),g(21,"path",20),h(),f(22,"g",21),g(23,"path",22),h(),f(24,"g",23),g(25,"path",24),h(),f(26,"g",25),g(27,"path",26),h(),f(28,"g",27),g(29,"path",28),h(),f(30,"g",29),g(31,"path",30),h(),f(32,"g",31),g(33,"path",32),h(),f(34,"g",33),g(35,"path",34),h(),f(36,"g",35),g(37,"path",36),h(),f(38,"g",37),g(39,"path",38),h(),f(40,"g",39),g(41,"path",40),h(),f(42,"g",41),g(43,"path",42),h(),f(44,"g",43),g(45,"path",44),h(),f(46,"g",45),g(47,"path",46),h(),f(48,"g",47),g(49,"path",48),h(),f(50,"g",49),g(51,"path",50),h(),f(52,"g",51),g(53,"path",52),h(),f(54,"g",53),g(55,"path",54),h(),f(56,"g",55),g(57,"path",56),h(),f(58,"g",57),g(59,"path",58),h(),f(60,"g",59),g(61,"path",60),h(),f(62,"g",61),g(63,"path",62),h(),f(64,"g",63),g(65,"path",64),h(),f(66,"g",65),g(67,"path",66),h(),f(68,"g",67),g(69,"path",68),h(),f(70,"g",69),g(71,"path",70),h(),f(72,"g",71),g(73,"path",72),h(),f(74,"g",73),g(75,"path",74),h(),f(76,"g",75),g(77,"path",76),h(),f(78,"g",77),g(79,"path",78),h(),f(80,"g",79),g(81,"path",80),h(),f(82,"g",81),g(83,"path",82),h(),f(84,"g",83),g(85,"path",84),h(),f(86,"g",85),g(87,"path",86),h(),g(88,"path",87)(89,"path",88)(90,"path",89)(91,"path",90)(92,"path",91),f(93,"g",92),g(94,"path",93),h(),f(95,"g",94),g(96,"path",95),h(),f(97,"g",96),g(98,"path",97),h(),f(99,"g",98),g(100,"path",99),h(),f(101,"g",100),g(102,"path",101),h(),g(103,"path",102)(104,"path",103)(105,"path",104)(106,"path",105)(107,"path",106)(108,"path",107)(109,"path",108)(110,"path",109)(111,"path",110)(112,"path",111)(113,"path",112)(114,"path",113),f(115,"g",114),g(116,"path",115),h(),f(117,"g",116),g(118,"path",117),h(),g(119,"path",118),f(120,"g",119),g(121,"path",120),h(),f(122,"g",121),g(123,"path",122),h(),g(124,"path",123),f(125,"g",124),g(126,"path",125),h(),f(127,"g",126),g(128,"path",127),h(),f(129,"g",128)(130,"g",129)(131,"g",130)(132,"g",131),g(133,"path",132),h(),f(134,"g",133),g(135,"path",134),h()()()(),f(136,"g",135)(137,"g",136)(138,"g",137)(139,"g",138),g(140,"path",139),h(),f(141,"g",140),g(142,"path",141),h()()()(),f(143,"g",142)(144,"g",143),g(145,"path",144),h(),g(146,"path",145)(147,"path",146)(148,"path",147)(149,"path",148)(150,"path",149)(151,"path",150)(152,"path",151)(153,"path",152),f(154,"g",153),g(155,"path",154),h(),f(156,"g",155),g(157,"path",156),h(),f(158,"g",157),g(159,"path",158),h()(),f(160,"g",159),g(161,"path",160),h(),f(162,"g",161),g(163,"path",162),h(),f(164,"g",163),g(165,"path",164),h(),f(166,"g",165),g(167,"path",166),h(),f(168,"g",167),g(169,"path",168),h(),f(170,"g",169),g(171,"path",170),h(),f(172,"g",171),g(173,"path",172),h(),f(174,"g",173),g(175,"path",174),h(),f(176,"g",175),g(177,"path",176),h(),f(178,"g",177),g(179,"path",178),h(),f(180,"g",179),g(181,"path",180),h(),f(182,"g",181),g(183,"path",182),h(),f(184,"g",183),g(185,"path",184),h(),f(186,"g",185),g(187,"path",186),h(),f(188,"g",187),g(189,"path",188),h(),f(190,"g",189),g(191,"path",190),h(),f(192,"g",191),g(193,"path",192),h(),f(194,"g",193),g(195,"path",194),h(),f(196,"g",195),g(197,"path",196),h(),f(198,"g",197),g(199,"path",198),h(),f(200,"g",199),g(201,"path",200),h(),f(202,"g",201),g(203,"path",202),h(),f(204,"g",203),g(205,"path",204),h()()()()(),ir(),f(206,"form",205),ht("submit",function(s){return o.onSubmit(s)}),f(207,"h1",206),S(208,"Talk to me"),h(),f(209,"div",207)(210,"label",208),g(211,"i",209),h(),g(212,"input",210),h(),f(213,"div",207)(214,"label",211),g(215,"i",212),h(),g(216,"input",213),h(),f(217,"div",214),g(218,"textarea",215),h(),f(219,"div",216)(220,"button",217),S(221,"Send message"),h()()(),We(222,i4,2,5,"div",218),h()),r&2&&(q(222),me("ngIf",o.submissionStatus))},dependencies:[ig,Us,Wg,Hg,iu],styles:['*[_ngcontent-%COMP%]{margin-bottom:1rem}.contact-header[_ngcontent-%COMP%]{text-align:center;color:#000;padding:1rem;position:relative;font-family:Pacifico,cursive;font-size:2rem}.contact-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:150px;height:4px;background-color:#11abba;border-radius:2px}.container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:9rem}svg[_ngcontent-%COMP%]{width:30%;height:auto;animation:_ngcontent-%COMP%_float 2s ease-in-out infinite;margin-left:10%}form[_ngcontent-%COMP%]{width:35%;background-color:#fff;padding:2rem;border-radius:3rem;box-shadow:0 10px 20px #0000001a;margin-right:10%}.title[_ngcontent-%COMP%]{font-family:Pacifico,cursive;color:#212529;font-size:2.5rem;text-align:center;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;font-size:1.1rem;color:#212529;background-color:#f2f6f8;border:none;border-radius:2rem;box-shadow:0 7px 5px #6f1a8e41}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:7rem}.form-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#57565c}[_ngcontent-%COMP%]::placeholder{color:#838788;font-weight:600}.btn.btn-primary[_ngcontent-%COMP%]{display:inline-block;width:100%;padding:.8rem;font-size:1.1rem;font-weight:700;border:none;border-radius:3rem;background-image:linear-gradient(131deg,#905f8b,#82b5d6,#83b8d9,#a06a9d);background-size:300% 100%;transition:all .3s ease-in-out;color:#fff;cursor:pointer}.btn.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 7px 5px #6f1a8e41;background-size:100% 100%;transform:translateY(-.15em)}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}@keyframes _ngcontent-%COMP%_blink{0%{opacity:0}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.container[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:2rem}svg[_ngcontent-%COMP%]{width:60%;margin-left:0}form[_ngcontent-%COMP%]{width:90%;margin-right:0}.title[_ngcontent-%COMP%]{font-size:2rem}.btn.btn-primary[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.title[_ngcontent-%COMP%]{font-size:1.8rem}.contact-header[_ngcontent-%COMP%]{font-size:1.5rem}form[_ngcontent-%COMP%]{padding:1.5rem}.btn.btn-primary[_ngcontent-%COMP%]{padding:.6rem}}']})}return e})();var Zm=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/skills";constructor(n){this.http=n}getSkills(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(Nt))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function c4(e,t){if(e&1&&(f(0,"div",6)(1,"div",7),g(2,"img",8),h(),f(3,"h3",9),S(4),h()()),e&2){let n=t.$implicit;q(2),me("src",n.icon,Cn)("alt",n.name),q(2),ke(n.name)}}var Xm=(()=>{class e{skillsService;skills=[];constructor(n){this.skillsService=n}ngOnInit(){this.skillsService.getSkills().subscribe(n=>{this.skills=n.map(r=>({icon:r.photo,name:r.name}))},n=>{console.error("Error fetching skills:",n)})}static \u0275fac=function(r){return new(r||e)(j(Zm))};static \u0275cmp=re({type:e,selectors:[["app-skills"]],decls:7,vars:1,consts:[["id","skills",1,"pt-16","pb-12","bg-gradient-to-br","from-pink-50","via-white","to-rose-100"],[1,"text-center","pb-10"],[1,"text-4xl","font-extrabold","uppercase","text-pink-600","tracking-wider","relative","inline-block"],[1,"block","w-16","h-1","bg-pink-400","mx-auto","mt-2","rounded-full"],[1,"grid","grid-cols-2","sm:grid-cols-3","md:grid-cols-4","lg:grid-cols-5","gap-6","px-6","sm:px-12"],["class","bg-white border border-pink-200 rounded-3xl p-6 shadow-md hover:shadow-pink-200 transition-all duration-300 hover:scale-105 flex flex-col items-center text-center",4,"ngFor","ngForOf"],[1,"bg-white","border","border-pink-200","rounded-3xl","p-6","shadow-md","hover:shadow-pink-200","transition-all","duration-300","hover:scale-105","flex","flex-col","items-center","text-center"],[1,"w-20","h-20","flex","items-center","justify-center","rounded-full","bg-pink-100","border","border-pink-300","shadow-inner","mb-3"],[1,"w-10","h-10","object-contain",3,"src","alt"],[1,"text-pink-700","font-semibold","text-sm","tracking-wide"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"div",1)(2,"h2",2),S(3," Skills "),g(4,"span",3),h()(),f(5,"div",4),We(6,c4,5,3,"div",5),h()()),r&2&&(q(6),me("ngForOf",o.skills))},dependencies:[pr]})}return e})();var Km=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/experiences";constructor(n){this.http=n}getExperiences(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(Nt))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function d4(e,t){if(e&1&&(f(0,"div",12)(1,"h3",13),S(2),h(),f(3,"p",14),S(4),h(),f(5,"p",15),S(6),Kr(7,"date"),Kr(8,"date"),h()()),e&2){let n=Zt().$implicit;q(2),ke(n.title),q(2),ke(n.company),q(2),gs("",Yr(7,4,n.from,"MMM yyyy")," - ",n.to?Yr(8,7,n.to,"MMM yyyy"):"Present","")}}function f4(e,t){if(e&1&&(f(0,"div",16)(1,"h3",13),S(2),h(),f(3,"p",14),S(4),h(),f(5,"p",15),S(6),Kr(7,"date"),Kr(8,"date"),h()()),e&2){let n=Zt().$implicit;q(2),ke(n.title),q(2),ke(n.company),q(2),gs("",Yr(7,4,n.from,"MMM yyyy")," - ",n.to?Yr(8,7,n.to,"MMM yyyy"):"Present","")}}function h4(e,t){if(e&1&&(f(0,"div",6)(1,"div",7),We(2,d4,9,10,"div",8),h(),g(3,"div",9),f(4,"div",10),We(5,f4,9,10,"div",11),h()()),e&2){let n=t.index;q(2),me("ngIf",n%2===0),q(3),me("ngIf",n%2!==0)}}var Ym=(()=>{class e{experiencesService;experiences=[];skills;constructor(n){this.experiencesService=n}ngOnInit(){this.experiencesService.getExperiences().subscribe(n=>{this.experiences=n.map(r=>({title:r.title,company:r.company,from:r.from,to:r.to}))},n=>{console.error("Error fetching experiences:",n)})}static \u0275fac=function(r){return new(r||e)(j(Km))};static \u0275cmp=re({type:e,selectors:[["app-experiences"]],decls:7,vars:1,consts:[["id","experience",1,"pt-10"],[1,"text-center","pb-8"],[1,"text-4xl","font-righteous","uppercase","text-black","mb-2","relative","inline-block","after:block","after:w-24","after:h-1","after:bg-cyan-500","after:rounded","after:mx-auto","after:mt-2"],[1,"relative","max-w-5xl","mx-auto","px-4"],[1,"absolute","left-1/2","transform","-translate-x-1/2","h-full","border-l-2","border-cyan-500"],["class","mb-16 flex flex-col sm:flex-row items-center w-full relative",4,"ngFor","ngForOf"],[1,"mb-16","flex","flex-col","sm:flex-row","items-center","w-full","relative"],[1,"w-full","sm:w-1/2","pr-4","sm:pr-8","flex","justify-end","z-10"],["class","bg-white border border-gray-200 p-6 rounded-lg shadow w-full max-w-md text-right",4,"ngIf"],[1,"absolute","left-1/2","transform","-translate-x-1/2","w-5","h-5","bg-cyan-500","rounded-full","border-4","border-white","shadow","z-20"],[1,"w-full","sm:w-1/2","pl-4","sm:pl-8","flex","justify-start","z-10"],["class","bg-white border border-gray-200 p-6 rounded-lg shadow w-full max-w-md text-left",4,"ngIf"],[1,"bg-white","border","border-gray-200","p-6","rounded-lg","shadow","w-full","max-w-md","text-right"],[1,"text-xl","font-semibold","text-gray-800"],[1,"text-gray-600"],[1,"text-gray-500","text-sm"],[1,"bg-white","border","border-gray-200","p-6","rounded-lg","shadow","w-full","max-w-md","text-left"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"div",1)(2,"h2",2),S(3," Experience "),h()(),f(4,"div",3),g(5,"div",4),We(6,h4,6,2,"div",5),h()()),r&2&&(q(6),me("ngForOf",o.experiences))},dependencies:[pr,Us,sg]})}return e})();var Qm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=re({type:e,selectors:[["app-footer"]],decls:17,vars:0,consts:[[1,"bg-gray-900","text-white","py-1"],[1,"flex","justify-center","gap-4"],[1,"text-sm","hover:text-blue-400","transition-colors"],["href","mailto:<EMAIL>"],["href","https://www.instagram.com/roaaayman_10/"],["href","https://github.com/roaaayman21"],["href","https://wa.me/+2001151310078"],[1,"text-xl"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"ul",1)(2,"li",2)(3,"a",3),S(4,"Email"),h()(),f(5,"li",2)(6,"a",4),S(7,"Instagram"),h()(),f(8,"li",2)(9,"a",5),S(10,"Github"),h()(),f(11,"li",2)(12,"a",6),S(13,"WhatsApp"),h()(),f(14,"li",7)(15,"p"),S(16,"\u{1F44B}"),h()()()())},styles:["div[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;width:100%;background-image:linear-gradient(to right,#8e4564,#905f8b,#897aac,#8195c3,#80aed1,#81b1d4,#82b5d6,#83b8d9,#83a6d3,#8b93c8,#967fb6,#a06a9d);background-size:cover;font-family:Menlo,monospace;flex-direction:column}ul[_ngcontent-%COMP%]{display:inline-grid;grid-auto-flow:row;grid-gap:12px;justify-items:center;margin:auto;padding:10px;list-style:none;text-align:center}@media (min-width: 500px){ul[_ngcontent-%COMP%]{grid-auto-flow:column}}li[_ngcontent-%COMP%]{padding:5px}a[_ngcontent-%COMP%]{color:#000;text-decoration:none;font-size:1.2rem;box-shadow:inset 0 -1px #fff6}a[_ngcontent-%COMP%]:hover{box-shadow:inset 0 -1.2em #fff6}li[_ngcontent-%COMP%]:last-child{grid-column:1 / 2;grid-row:1 / 2}li[_ngcontent-%COMP%]:hover ~ li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_wave-animation .3s infinite}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-15deg)}}@media (max-width: 500px){div[_ngcontent-%COMP%]{background-size:200% 100%;background-position:center}}"]})}return e})();var Jm=(()=>{class e{title="portflio";static \u0275fac=function(r){return new(r||e)};static \u0275cmp=re({type:e,selectors:[["app-root"]],decls:7,vars:0,consts:[["id","projects"],["id","skills"],["id","experiences"],["id","contact"]],template:function(r,o){r&1&&g(0,"app-header")(1,"router-outlet")(2,"app-projects",0)(3,"app-skills",1)(4,"app-experiences",2)(5,"app-contact",3)(6,"app-footer")},dependencies:[ku,zm,Wm,qm,Xm,Ym,Qm]})}return e})();var e0=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ge({type:e,bootstrap:[Jm]});static \u0275inj=pe({imports:[Fg,Hm,Eg,Zg,Xg]})}return e})();Tg().bootstrapModule(e0,{ngZoneEventCoalescing:!0}).catch(e=>console.error(e));
