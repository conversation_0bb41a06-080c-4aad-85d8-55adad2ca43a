

.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: var(--text-primary);
  overflow: hidden;
  background-size: cover;
  /* Add pink theme overlay */
  background: linear-gradient(rgba(251, 207, 232, 0.3), rgba(254, 242, 248, 0.3));
}

.profile-pic {
  border-radius: 50%;
  overflow: hidden;
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
  border: 4px solid var(--border-pink);
  box-shadow: 0 0 20px var(--shadow-pink);
}

.profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px var(--shadow-pink);
}

.hero-section p {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--text-primary);
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: var(--accent-pink);
}

.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;

}

#bg-video {
  position: absolute;
  top: 50%;
  left: 50%;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  z-index: -1;
  transform: translate(-50%, -50%);
  object-fit: cover;
}


