

.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: var(--text-primary);
  overflow: hidden;
}

.profile-pic {
  border-radius: 50%;
  overflow: hidden;
  width: 150px;
  height: 150px;
  margin-bottom: 20px;
  border: 4px solid var(--border-pink);
  box-shadow: 0 0 20px var(--shadow-pink);
}

.profile-pic img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-section h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  color: var(--text-primary);
  text-shadow: 2px 2px 4px var(--shadow-pink);
}

.hero-section p {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--text-primary);
  font-size: 1.5rem;
  transition: color 0.3s;
}

.social-icons a:hover {
  color: var(--accent-pink);
}

/* Vanta.js background will be automatically applied to #vanta-birds */


