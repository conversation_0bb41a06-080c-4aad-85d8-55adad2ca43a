var dm=Object.defineProperty,fm=Object.defineProperties;var hm=Object.getOwnPropertyDescriptors;var du=Object.getOwnPropertySymbols;var pm=Object.prototype.hasOwnProperty,gm=Object.prototype.propertyIsEnumerable;var fu=(e,t,n)=>t in e?dm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,C=(e,t)=>{for(var n in t||={})pm.call(t,n)&&fu(e,n,t[n]);if(du)for(var n of du(t))gm.call(t,n)&&fu(e,n,t[n]);return e},j=(e,t)=>fm(e,hm(t));var mo=(e,t,n)=>new Promise((r,o)=>{var i=c=>{try{a(n.next(c))}catch(l){o(l)}},s=c=>{try{a(n.throw(c))}catch(l){o(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(i,s);a((n=n.apply(e,t)).next())});function hu(e,t){return Object.is(e,t)}var Y=null,yo=!1,vo=1,yt=Symbol("SIGNAL");function B(e){let t=Y;return Y=e,t}function pu(){return Y}var fr={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function js(e){if(yo)throw new Error("");if(Y===null)return;Y.consumerOnSignalRead(e);let t=Y.nextProducerIndex++;if(Eo(Y),t<Y.producerNode.length&&Y.producerNode[t]!==e&&dr(Y)){let n=Y.producerNode[t];wo(n,Y.producerIndexOfThis[t])}Y.producerNode[t]!==e&&(Y.producerNode[t]=e,Y.producerIndexOfThis[t]=dr(Y)?vu(e,Y,t):0),Y.producerLastReadVersion[t]=e.version}function mm(){vo++}function gu(e){if(!(dr(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===vo)){if(!e.producerMustRecompute(e)&&!Hs(e)){e.dirty=!1,e.lastCleanEpoch=vo;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=vo}}function mu(e){if(e.liveConsumerNode===void 0)return;let t=yo;yo=!0;try{for(let n of e.liveConsumerNode)n.dirty||ym(n)}finally{yo=t}}function yu(){return Y?.consumerAllowSignalWrites!==!1}function ym(e){e.dirty=!0,mu(e),e.consumerMarkedDirty?.(e)}function Do(e){return e&&(e.nextProducerIndex=0),B(e)}function Bs(e,t){if(B(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(dr(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)wo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Hs(e){Eo(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(gu(n),r!==n.version))return!0}return!1}function $s(e){if(Eo(e),dr(e))for(let t=0;t<e.producerNode.length;t++)wo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function vu(e,t,n){if(Cu(e),e.liveConsumerNode.length===0&&Du(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=vu(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(n),e.liveConsumerNode.push(t)-1}function wo(e,t){if(Cu(e),e.liveConsumerNode.length===1&&Du(e))for(let r=0;r<e.producerNode.length;r++)wo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Eo(o),o.producerIndexOfThis[r]=t}}function dr(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Eo(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Cu(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Du(e){return e.producerNode!==void 0}function wu(e){let t=Object.create(vm);t.computation=e;let n=()=>{if(gu(t),js(t),t.value===Co)throw t.error;return t.value};return n[yt]=t,n}var Ls=Symbol("UNSET"),Vs=Symbol("COMPUTING"),Co=Symbol("ERRORED"),vm=j(C({},fr),{value:Ls,dirty:!0,error:null,equal:hu,producerMustRecompute(e){return e.value===Ls||e.value===Vs},producerRecomputeValue(e){if(e.value===Vs)throw new Error("Detected cycle in computations.");let t=e.value;e.value=Vs;let n=Do(e),r;try{r=e.computation()}catch(o){r=Co,e.error=o}finally{Bs(e,n)}if(t!==Ls&&t!==Co&&r!==Co&&e.equal(t,r)){e.value=t;return}e.value=r,e.version++}});function Cm(){throw new Error}var Eu=Cm;function bu(){Eu()}function Iu(e){Eu=e}var Dm=null;function _u(e){let t=Object.create(Su);t.value=e;let n=()=>(js(t),t.value);return n[yt]=t,n}function zs(e,t){yu()||bu(),e.equal(e.value,t)||(e.value=t,wm(e))}function Mu(e,t){yu()||bu(),zs(e,t(e.value))}var Su=j(C({},fr),{equal:hu,value:void 0});function wm(e){e.version++,mm(),mu(e),Dm?.()}function A(e){return typeof e=="function"}function cn(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var bo=cn(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function hr(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var Z=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(A(r))try{r()}catch(i){t=i instanceof bo?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Au(i)}catch(s){t=t??[],s instanceof bo?t=[...t,...s.errors]:t.push(s)}}if(t)throw new bo(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Au(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&hr(n,t)}remove(t){let{_finalizers:n}=this;n&&hr(n,t),t instanceof e&&t._removeParent(this)}};Z.EMPTY=(()=>{let e=new Z;return e.closed=!0,e})();var Gs=Z.EMPTY;function Io(e){return e instanceof Z||e&&"closed"in e&&A(e.remove)&&A(e.add)&&A(e.unsubscribe)}function Au(e){A(e)?e():e.unsubscribe()}var Pe={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var ln={setTimeout(e,t,...n){let{delegate:r}=ln;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=ln;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function _o(e){ln.setTimeout(()=>{let{onUnhandledError:t}=Pe;if(t)t(e);else throw e})}function pr(){}var xu=Ws("C",void 0,void 0);function Tu(e){return Ws("E",void 0,e)}function Fu(e){return Ws("N",e,void 0)}function Ws(e,t,n){return{kind:e,value:t,error:n}}var jt=null;function un(e){if(Pe.useDeprecatedSynchronousErrorHandling){let t=!jt;if(t&&(jt={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=jt;if(jt=null,n)throw r}}else e()}function Nu(e){Pe.useDeprecatedSynchronousErrorHandling&&jt&&(jt.errorThrown=!0,jt.error=e)}var Bt=class extends Z{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Io(t)&&t.add(this)):this.destination=Im}static create(t,n,r){return new dn(t,n,r)}next(t){this.isStopped?Zs(Fu(t),this):this._next(t)}error(t){this.isStopped?Zs(Tu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Zs(xu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},Em=Function.prototype.bind;function qs(e,t){return Em.call(e,t)}var Xs=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Mo(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Mo(r)}else Mo(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Mo(n)}}},dn=class extends Bt{constructor(t,n,r){super();let o;if(A(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Pe.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&qs(t.next,i),error:t.error&&qs(t.error,i),complete:t.complete&&qs(t.complete,i)}):o=t}this.destination=new Xs(o)}};function Mo(e){Pe.useDeprecatedSynchronousErrorHandling?Nu(e):_o(e)}function bm(e){throw e}function Zs(e,t){let{onStoppedNotification:n}=Pe;n&&ln.setTimeout(()=>n(e,t))}var Im={closed:!0,next:pr,error:bm,complete:pr};var fn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function be(e){return e}function Ks(...e){return Ys(e)}function Ys(e){return e.length===0?be:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var L=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Mm(n)?n:new dn(n,r,o);return un(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=Ou(r),new r((o,i)=>{let s=new dn({next:a=>{try{n(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[fn](){return this}pipe(...n){return Ys(n)(this)}toPromise(n){return n=Ou(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function Ou(e){var t;return(t=e??Pe.Promise)!==null&&t!==void 0?t:Promise}function _m(e){return e&&A(e.next)&&A(e.error)&&A(e.complete)}function Mm(e){return e&&e instanceof Bt||_m(e)&&Io(e)}function Qs(e){return A(e?.lift)}function V(e){return t=>{if(Qs(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function P(e,t,n,r,o){return new Js(e,t,n,r,o)}var Js=class extends Bt{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(c){t.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){t.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function hn(){return V((e,t)=>{let n=null;e._refCount++;let r=P(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var pn=class extends L{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Qs(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new Z;let n=this.getSubject();t.add(this.source.subscribe(P(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=Z.EMPTY)}return t}refCount(){return hn()(this)}};var Ru=cn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var J=(()=>{class e extends L{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new So(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new Ru}next(n){un(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){un(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){un(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Gs:(this.currentObservers=null,i.push(n),new Z(()=>{this.currentObservers=null,hr(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new L;return n.source=this,n}}return e.create=(t,n)=>new So(t,n),e})(),So=class extends J{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Gs}};var ie=class extends J{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var Ie=new L(e=>e.complete());function Uu(e){return e&&A(e.schedule)}function Pu(e){return e[e.length-1]}function Ao(e){return A(Pu(e))?e.pop():void 0}function vt(e){return Uu(Pu(e))?e.pop():void 0}function Lu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(u){try{l(r.next(u))}catch(d){s(d)}}function c(u){try{l(r.throw(u))}catch(d){s(d)}}function l(u){u.done?i(u.value):o(u.value).then(a,c)}l((r=r.apply(e,t||[])).next())})}function ku(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Ht(e){return this instanceof Ht?(this.v=e,this):new Ht(e)}function Vu(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o={},a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(p){return function(v){return Promise.resolve(v).then(p,d)}}function a(p,v){r[p]&&(o[p]=function(D){return new Promise(function(_,T){i.push([p,D,_,T])>1||c(p,D)})},v&&(o[p]=v(o[p])))}function c(p,v){try{l(r[p](v))}catch(D){m(i[0][3],D)}}function l(p){p.value instanceof Ht?Promise.resolve(p.value.v).then(u,d):m(i[0][2],p)}function u(p){c("next",p)}function d(p){c("throw",p)}function m(p,v){p(v),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ju(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof ku=="function"?ku(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(l){i({value:l,done:a})},s)}}var xo=e=>e&&typeof e.length=="number"&&typeof e!="function";function To(e){return A(e?.then)}function Fo(e){return A(e[fn])}function No(e){return Symbol.asyncIterator&&A(e?.[Symbol.asyncIterator])}function Oo(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Sm(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ro=Sm();function Uo(e){return A(e?.[Ro])}function Po(e){return Vu(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield Ht(n.read());if(o)return yield Ht(void 0);yield yield Ht(r)}}finally{n.releaseLock()}})}function ko(e){return A(e?.getReader)}function X(e){if(e instanceof L)return e;if(e!=null){if(Fo(e))return Am(e);if(xo(e))return xm(e);if(To(e))return Tm(e);if(No(e))return Bu(e);if(Uo(e))return Fm(e);if(ko(e))return Nm(e)}throw Oo(e)}function Am(e){return new L(t=>{let n=e[fn]();if(A(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function xm(e){return new L(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Tm(e){return new L(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,_o)})}function Fm(e){return new L(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function Bu(e){return new L(t=>{Om(e,t).catch(n=>t.error(n))})}function Nm(e){return Bu(Po(e))}function Om(e,t){var n,r,o,i;return Lu(this,void 0,void 0,function*(){try{for(n=ju(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function ve(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Lo(e,t=0){return V((n,r)=>{n.subscribe(P(r,o=>ve(r,e,()=>r.next(o),t),()=>ve(r,e,()=>r.complete(),t),o=>ve(r,e,()=>r.error(o),t)))})}function Vo(e,t=0){return V((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function Hu(e,t){return X(e).pipe(Vo(t),Lo(t))}function $u(e,t){return X(e).pipe(Vo(t),Lo(t))}function zu(e,t){return new L(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function Gu(e,t){return new L(n=>{let r;return ve(n,t,()=>{r=e[Ro](),ve(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>A(r?.return)&&r.return()})}function jo(e,t){if(!e)throw new Error("Iterable cannot be null");return new L(n=>{ve(n,t,()=>{let r=e[Symbol.asyncIterator]();ve(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Wu(e,t){return jo(Po(e),t)}function qu(e,t){if(e!=null){if(Fo(e))return Hu(e,t);if(xo(e))return zu(e,t);if(To(e))return $u(e,t);if(No(e))return jo(e,t);if(Uo(e))return Gu(e,t);if(ko(e))return Wu(e,t)}throw Oo(e)}function W(e,t){return t?qu(e,t):X(e)}function M(...e){let t=vt(e);return W(e,t)}function gn(e,t){let n=A(e)?e:()=>e,r=o=>o.error(n());return new L(t?o=>t.schedule(r,0,o):r)}function ea(e){return!!e&&(e instanceof L||A(e.lift)&&A(e.subscribe))}var rt=cn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function x(e,t){return V((n,r)=>{let o=0;n.subscribe(P(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Rm}=Array;function Um(e,t){return Rm(t)?e(...t):e(t)}function Bo(e){return x(t=>Um(e,t))}var{isArray:Pm}=Array,{getPrototypeOf:km,prototype:Lm,keys:Vm}=Object;function Ho(e){if(e.length===1){let t=e[0];if(Pm(t))return{args:t,keys:null};if(jm(t)){let n=Vm(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function jm(e){return e&&typeof e=="object"&&km(e)===Lm}function $o(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function gr(...e){let t=vt(e),n=Ao(e),{args:r,keys:o}=Ho(e);if(r.length===0)return W([],t);let i=new L(Bm(r,t,o?s=>$o(o,s):be));return n?i.pipe(Bo(n)):i}function Bm(e,t,n=be){return r=>{Zu(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Zu(t,()=>{let l=W(e[c],t),u=!1;l.subscribe(P(r,d=>{i[c]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Zu(e,t,n){e?ve(n,e,t):t()}function Xu(e,t,n,r,o,i,s,a){let c=[],l=0,u=0,d=!1,m=()=>{d&&!c.length&&!l&&t.complete()},p=D=>l<r?v(D):c.push(D),v=D=>{i&&t.next(D),l++;let _=!1;X(n(D,u++)).subscribe(P(t,T=>{o?.(T),i?p(T):t.next(T)},()=>{_=!0},void 0,()=>{if(_)try{for(l--;c.length&&l<r;){let T=c.shift();s?ve(t,s,()=>v(T)):v(T)}m()}catch(T){t.error(T)}}))};return e.subscribe(P(t,p,()=>{d=!0,m()})),()=>{a?.()}}function K(e,t,n=1/0){return A(t)?K((r,o)=>x((i,s)=>t(r,i,o,s))(X(e(r,o))),n):(typeof t=="number"&&(n=t),V((r,o)=>Xu(r,o,e,n)))}function mn(e=1/0){return K(be,e)}function Ku(){return mn(1)}function yn(...e){return Ku()(W(e,vt(e)))}function zo(e){return new L(t=>{X(e()).subscribe(t)})}function ta(...e){let t=Ao(e),{args:n,keys:r}=Ho(e),o=new L(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),c=s,l=s;for(let u=0;u<s;u++){let d=!1;X(n[u]).subscribe(P(i,m=>{d||(d=!0,l--),a[u]=m},()=>c--,void 0,()=>{(!c||!d)&&(l||i.next(r?$o(r,a):a),i.complete())}))}});return t?o.pipe(Bo(t)):o}function Ce(e,t){return V((n,r)=>{let o=0;n.subscribe(P(r,i=>e.call(t,i,o++)&&r.next(i)))})}function Ct(e){return V((t,n)=>{let r=null,o=!1,i;r=t.subscribe(P(n,void 0,void 0,s=>{i=X(e(s,Ct(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Yu(e,t,n,r,o){return(i,s)=>{let a=n,c=t,l=0;i.subscribe(P(s,u=>{let d=l++;c=a?e(c,u,d):(a=!0,u),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ot(e,t){return A(t)?K(e,t,1):K(e,1)}function Dt(e){return V((t,n)=>{let r=!1;t.subscribe(P(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function it(e){return e<=0?()=>Ie:V((t,n)=>{let r=0;t.subscribe(P(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function na(e){return x(()=>e)}function Go(e=Hm){return V((t,n)=>{let r=!1;t.subscribe(P(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function Hm(){return new rt}function wt(e){return V((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Ge(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ce((o,i)=>e(o,i,r)):be,it(1),n?Dt(t):Go(()=>new rt))}function vn(e){return e<=0?()=>Ie:V((t,n)=>{let r=[];t.subscribe(P(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function ra(e,t){let n=arguments.length>=2;return r=>r.pipe(e?Ce((o,i)=>e(o,i,r)):be,vn(1),n?Dt(t):Go(()=>new rt))}function oa(e,t){return V(Yu(e,t,arguments.length>=2,!0))}function ia(...e){let t=vt(e);return V((n,r)=>{(t?yn(e,n,t):yn(e,n)).subscribe(r)})}function De(e,t){return V((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(P(r,c=>{o?.unsubscribe();let l=0,u=i++;X(e(c,u)).subscribe(o=P(r,d=>r.next(t?t(c,d,u,l++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function sa(e){return V((t,n)=>{X(e).subscribe(P(n,()=>n.complete(),pr)),!n.closed&&t.subscribe(n)})}function ee(e,t,n){let r=A(e)||t||n?{next:e,error:t,complete:n}:e;return r?V((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(P(i,c=>{var l;(l=r.next)===null||l===void 0||l.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var l;a=!1,(l=r.error)===null||l===void 0||l.call(r,c),i.error(c)},()=>{var c,l;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(l=r.finalize)===null||l===void 0||l.call(r)}))}):be}var Ld="https://g.co/ng/security#xss",I=class extends Error{constructor(t,n){super(Si(t,n)),this.code=t}};function Si(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Mr(e){return{toString:e}.toString()}var Wo="__parameters__";function $m(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function Vd(e,t,n){return Mr(()=>{let r=$m(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,l,u){let d=c.hasOwnProperty(Wo)?c[Wo]:Object.defineProperty(c,Wo,{value:[]})[Wo];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),c}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var Ve=globalThis;function G(e){for(let t in e)if(e[t]===G)return t;throw Error("Could not find renamed property on target object.")}function zm(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function pe(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(pe).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function Qu(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var Gm=G({__forward_ref__:G});function Ai(e){return e.__forward_ref__=Ai,e.toString=function(){return pe(this())},e}function he(e){return jd(e)?e():e}function jd(e){return typeof e=="function"&&e.hasOwnProperty(Gm)&&e.__forward_ref__===Ai}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ae(e){return{providers:e.providers||[],imports:e.imports||[]}}function xi(e){return Ju(e,Hd)||Ju(e,$d)}function Bd(e){return xi(e)!==null}function Ju(e,t){return e.hasOwnProperty(t)?e[t]:null}function Wm(e){let t=e&&(e[Hd]||e[$d]);return t||null}function ed(e){return e&&(e.hasOwnProperty(td)||e.hasOwnProperty(qm))?e[td]:null}var Hd=G({\u0275prov:G}),td=G({\u0275inj:G}),$d=G({ngInjectableDef:G}),qm=G({ngInjectorDef:G}),b=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=w({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function zd(e){return e&&!!e.\u0275providers}var Zm=G({\u0275cmp:G}),Xm=G({\u0275dir:G}),Km=G({\u0275pipe:G}),Ym=G({\u0275mod:G}),ti=G({\u0275fac:G}),yr=G({__NG_ELEMENT_ID__:G}),nd=G({__NG_ENV_ID__:G});function Ti(e){return typeof e=="string"?e:e==null?"":String(e)}function Qm(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():Ti(e)}function Jm(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new I(-200,e)}function sc(e,t){throw new I(-201,!1)}var N=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(N||{}),Da;function Gd(){return Da}function Te(e){let t=Da;return Da=e,t}function Wd(e,t,n){let r=xi(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&N.Optional)return null;if(t!==void 0)return t;sc(e,"Injector")}var e0={},Cr=e0,wa="__NG_DI_FLAG__",ni="ngTempTokenPath",t0="ngTokenPath",n0=/\n/gm,r0="\u0275",rd="__source",En;function o0(){return En}function Et(e){let t=En;return En=e,t}function i0(e,t=N.Default){if(En===void 0)throw new I(-203,!1);return En===null?Wd(e,void 0,t):En.get(e,t&N.Optional?null:void 0,t)}function E(e,t=N.Default){return(Gd()||i0)(he(e),t)}function y(e,t=N.Default){return E(e,Fi(t))}function Fi(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ea(e){let t=[];for(let n=0;n<e.length;n++){let r=he(e[n]);if(Array.isArray(r)){if(r.length===0)throw new I(900,!1);let o,i=N.Default;for(let s=0;s<r.length;s++){let a=r[s],c=s0(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}t.push(E(o,i))}else t.push(E(r))}return t}function qd(e,t){return e[wa]=t,e.prototype[wa]=t,e}function s0(e){return e[wa]}function a0(e,t,n,r){let o=e[ni];throw t[rd]&&o.unshift(t[rd]),e.message=c0(`
`+e.message,o,n,r),e[t0]=o,e[ni]=null,e}function c0(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==r0?e.slice(2):e;let o=pe(t);if(Array.isArray(t))o=t.map(pe).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):pe(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(n0,`
  `)}`}var Ni=qd(Vd("Optional"),8);var ac=qd(Vd("SkipSelf"),4);function In(e,t){let n=e.hasOwnProperty(ti);return n?e[ti]:null}function cc(e,t){e.forEach(n=>Array.isArray(n)?cc(n,t):t(n))}function Zd(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function ri(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function l0(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function u0(e,t,n){let r=Sr(e,t);return r>=0?e[r|1]=n:(r=~r,l0(e,r,t,n)),r}function aa(e,t){let n=Sr(e,t);if(n>=0)return e[n|1]}function Sr(e,t){return d0(e,t,1)}function d0(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var _n={},Fe=[],Mn=new b(""),Xd=new b("",-1),Kd=new b(""),oi=class{get(t,n=Cr){if(n===Cr){let r=new Error(`NullInjectorError: No provider for ${pe(t)}!`);throw r.name="NullInjectorError",r}return n}},Yd=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Yd||{}),Ze=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Ze||{}),_t=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(_t||{});function f0(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function ba(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];h0(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Qd(e){return e===3||e===4||e===6}function h0(e){return e.charCodeAt(0)===64}function Dr(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?od(e,n,o,null,t[++r]):od(e,n,o,null,null))}}return e}function od(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var Jd="ng-template";function p0(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&f0(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(lc(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function lc(e){return e.type===4&&e.value!==Jd}function g0(e,t,n){let r=e.type===4&&!n?Jd:e.value;return t===r}function m0(e,t,n){let r=4,o=e.attrs,i=o!==null?C0(o):0,s=!1;for(let a=0;a<t.length;a++){let c=t[a];if(typeof c=="number"){if(!s&&!ke(r)&&!ke(c))return!1;if(s&&ke(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!g0(e,c,n)||c===""&&t.length===1){if(ke(r))return!1;s=!0}}else if(r&8){if(o===null||!p0(e,o,c,n)){if(ke(r))return!1;s=!0}}else{let l=t[++a],u=y0(c,o,lc(e),n);if(u===-1){if(ke(r))return!1;s=!0;continue}if(l!==""){let d;if(u>i?d="":d=o[u+1].toLowerCase(),r&2&&l!==d){if(ke(r))return!1;s=!0}}}}return ke(r)||s}function ke(e){return(e&1)===0}function y0(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return D0(t,e)}function v0(e,t,n=!1){for(let r=0;r<t.length;r++)if(m0(e,t[r],n))return!0;return!1}function C0(e){for(let t=0;t<e.length;t++){let n=e[t];if(Qd(n))return t}return e.length}function D0(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function id(e,t){return e?":not("+t.trim()+")":t}function w0(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ke(s)&&(t+=id(i,o),o=""),r=s,i=i||!ke(r);n++}return o!==""&&(t+=id(i,o)),t}function E0(e){return e.map(w0).join(",")}function b0(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!ke(o))break;o=i}r++}return{attrs:t,classes:n}}function te(e){return Mr(()=>{let t=of(e),n=j(C({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Yd.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Ze.Emulated,styles:e.styles||Fe,_:null,schemas:e.schemas||null,tView:null,id:""});sf(n);let r=e.dependencies;return n.directiveDefs=ad(r,!1),n.pipeDefs=ad(r,!0),n.id=M0(n),n})}function I0(e){return Mt(e)||ef(e)}function _0(e){return e!==null}function ce(e){return Mr(()=>({type:e.type,bootstrap:e.bootstrap||Fe,declarations:e.declarations||Fe,imports:e.imports||Fe,exports:e.exports||Fe,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function sd(e,t){if(e==null)return _n;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=_t.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==_t.None?[r,a]:r,t[i]=s):n[i]=r}return n}function Be(e){return Mr(()=>{let t=of(e);return sf(t),t})}function Mt(e){return e[Zm]||null}function ef(e){return e[Xm]||null}function tf(e){return e[Km]||null}function nf(e){let t=Mt(e)||ef(e)||tf(e);return t!==null?t.standalone:!1}function rf(e,t){let n=e[Ym]||null;if(!n&&t===!0)throw new Error(`Type ${pe(e)} does not have '\u0275mod' property.`);return n}function of(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||_n,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||Fe,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:sd(e.inputs,t),outputs:sd(e.outputs),debugInfo:null}}function sf(e){e.features?.forEach(t=>t(e))}function ad(e,t){if(!e)return null;let n=t?tf:I0;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(_0)}function M0(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Oi(e){return{\u0275providers:e}}function S0(...e){return{\u0275providers:af(!0,e),\u0275fromNgModule:!0}}function af(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return cc(t,s=>{let a=s;Ia(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&cf(o,i),n}function cf(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];uc(o,i=>{t(i,r)})}}function Ia(e,t,n,r){if(e=he(e),!e)return!1;let o=null,i=ed(e),s=!i&&Mt(e);if(!i&&!s){let c=e.ngModule;if(i=ed(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let l of c)Ia(l,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let l;try{cc(i.imports,u=>{Ia(u,t,n,r)&&(l||=[],l.push(u))})}finally{}l!==void 0&&cf(l,t)}if(!a){let l=In(o)||(()=>new o);t({provide:o,useFactory:l,deps:Fe},o),t({provide:Kd,useValue:o,multi:!0},o),t({provide:Mn,useValue:()=>E(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let l=e;uc(c,u=>{t(u,l)})}}else return!1;return o!==e&&e.providers!==void 0}function uc(e,t){for(let n of e)zd(n)&&(n=n.\u0275providers),Array.isArray(n)?uc(n,t):t(n)}var A0=G({provide:String,useValue:G});function lf(e){return e!==null&&typeof e=="object"&&A0 in e}function x0(e){return!!(e&&e.useExisting)}function T0(e){return!!(e&&e.useFactory)}function Sn(e){return typeof e=="function"}function F0(e){return!!e.useClass}var Ri=new b(""),Yo={},N0={},ca;function dc(){return ca===void 0&&(ca=new oi),ca}var ge=class{},wr=class extends ge{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Ma(t,s=>this.processProvider(s)),this.records.set(Xd,Cn(void 0,this)),o.has("environment")&&this.records.set(ge,Cn(void 0,this));let i=this.records.get(Ri);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Kd,Fe,N.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=B(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),B(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=Et(this),r=Te(void 0),o;try{return t()}finally{Et(n),Te(r)}}get(t,n=Cr,r=N.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(nd))return t[nd](this);r=Fi(r);let o,i=Et(this),s=Te(void 0);try{if(!(r&N.SkipSelf)){let c=this.records.get(t);if(c===void 0){let l=k0(t)&&xi(t);l&&this.injectableDefInScope(l)?c=Cn(_a(t),Yo):c=null,this.records.set(t,c)}if(c!=null)return this.hydrate(t,c)}let a=r&N.Self?dc():this.parent;return n=r&N.Optional&&n===Cr?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[ni]=a[ni]||[]).unshift(pe(t)),i)throw a;return a0(a,t,"R3InjectorError",this.source)}else throw a}finally{Te(s),Et(i)}}resolveInjectorInitializers(){let t=B(null),n=Et(this),r=Te(void 0),o;try{let i=this.get(Mn,Fe,N.Self);for(let s of i)s()}finally{Et(n),Te(r),B(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(pe(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new I(205,!1)}processProvider(t){t=he(t);let n=Sn(t)?t:he(t&&t.provide),r=R0(t);if(!Sn(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Cn(void 0,Yo,!0),o.factory=()=>Ea(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=B(null);try{return n.value===Yo&&(n.value=N0,n.value=n.factory()),typeof n.value=="object"&&n.value&&P0(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{B(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=he(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function _a(e){let t=xi(e),n=t!==null?t.factory:In(e);if(n!==null)return n;if(e instanceof b)throw new I(204,!1);if(e instanceof Function)return O0(e);throw new I(204,!1)}function O0(e){if(e.length>0)throw new I(204,!1);let n=Wm(e);return n!==null?()=>n.factory(e):()=>new e}function R0(e){if(lf(e))return Cn(void 0,e.useValue);{let t=uf(e);return Cn(t,Yo)}}function uf(e,t,n){let r;if(Sn(e)){let o=he(e);return In(o)||_a(o)}else if(lf(e))r=()=>he(e.useValue);else if(T0(e))r=()=>e.useFactory(...Ea(e.deps||[]));else if(x0(e))r=()=>E(he(e.useExisting));else{let o=he(e&&(e.useClass||e.provide));if(U0(e))r=()=>new o(...Ea(e.deps));else return In(o)||_a(o)}return r}function Cn(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function U0(e){return!!e.deps}function P0(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function k0(e){return typeof e=="function"||typeof e=="object"&&e instanceof b}function Ma(e,t){for(let n of e)Array.isArray(n)?Ma(n,t):n&&zd(n)?Ma(n.\u0275providers,t):t(n)}function Me(e,t){e instanceof wr&&e.assertNotDestroyed();let n,r=Et(e),o=Te(void 0);try{return t()}finally{Et(r),Te(o)}}function df(){return Gd()!==void 0||o0()!=null}function L0(e){if(!df())throw new I(-203,!1)}function V0(e){let t=Ve.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function j0(e){return typeof e=="function"}var ct=0,k=1,S=2,me=3,Le=4,He=5,ii=6,si=7,je=8,An=9,Xe=10,se=11,Er=12,cd=13,Ln=14,Ke=15,xn=16,Dn=17,Tn=18,Ui=19,ff=20,bt=21,la=22,Ne=23,St=25,hf=1;var zt=7,ai=8,ci=9,Oe=10,li=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(li||{});function It(e){return Array.isArray(e)&&typeof e[hf]=="object"}function lt(e){return Array.isArray(e)&&e[hf]===!0}function pf(e){return(e.flags&4)!==0}function Pi(e){return e.componentOffset>-1}function fc(e){return(e.flags&1)===1}function At(e){return!!e.template}function Sa(e){return(e[S]&512)!==0}var Aa=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function gf(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function Vn(){return mf}function mf(e){return e.type.prototype.ngOnChanges&&(e.setInput=H0),B0}Vn.ngInherit=!0;function B0(){let e=vf(this),t=e?.current;if(t){let n=e.previous;if(n===_n)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function H0(e,t,n,r,o){let i=this.declaredInputs[r],s=vf(e)||$0(e,{previous:_n,current:null}),a=s.current||(s.current={}),c=s.previous,l=c[i];a[i]=new Aa(l&&l.currentValue,n,c===_n),gf(e,t,o,n)}var yf="__ngSimpleChanges__";function vf(e){return e[yf]||null}function $0(e,t){return e[yf]=t}var ld=null;var We=function(e,t,n){ld?.(e,t,n)},Cf="svg",z0="math";function Ye(e){for(;Array.isArray(e);)e=e[ct];return e}function Df(e,t){return Ye(t[e])}function Re(e,t){return Ye(t[e.index])}function wf(e,t){return e.data[t]}function Tt(e,t){let n=t[e];return It(n)?n:n[ct]}function hc(e){return(e[S]&128)===128}function G0(e){return lt(e[me])}function ui(e,t){return t==null?null:e[t]}function Ef(e){e[Dn]=0}function bf(e){e[S]&1024||(e[S]|=1024,hc(e)&&ki(e))}function W0(e,t){for(;e>0;)t=t[Ln],e--;return t}function br(e){return!!(e[S]&9216||e[Ne]?.dirty)}function xa(e){e[Xe].changeDetectionScheduler?.notify(7),e[S]&64&&(e[S]|=1024),br(e)&&ki(e)}function ki(e){e[Xe].changeDetectionScheduler?.notify(0);let t=Gt(e);for(;t!==null&&!(t[S]&8192||(t[S]|=8192,!hc(t)));)t=Gt(t)}function If(e,t){if((e[S]&256)===256)throw new I(911,!1);e[bt]===null&&(e[bt]=[]),e[bt].push(t)}function q0(e,t){if(e[bt]===null)return;let n=e[bt].indexOf(t);n!==-1&&e[bt].splice(n,1)}function Gt(e){let t=e[me];return lt(t)?t[me]:t}var O={lFrame:Of(null),bindingsEnabled:!0,skipHydrationRootTNode:null};var _f=!1;function Z0(){return O.lFrame.elementDepthCount}function X0(){O.lFrame.elementDepthCount++}function K0(){O.lFrame.elementDepthCount--}function Mf(){return O.bindingsEnabled}function Y0(){return O.skipHydrationRootTNode!==null}function Q0(e){return O.skipHydrationRootTNode===e}function J0(){O.skipHydrationRootTNode=null}function q(){return O.lFrame.lView}function $e(){return O.lFrame.tView}function pc(e){return O.lFrame.contextLView=e,e[je]}function gc(e){return O.lFrame.contextLView=null,e}function Ee(){let e=Sf();for(;e!==null&&e.type===64;)e=e.parent;return e}function Sf(){return O.lFrame.currentTNode}function e1(){let e=O.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function Ar(e,t){let n=O.lFrame;n.currentTNode=e,n.isParent=t}function Af(){return O.lFrame.isParent}function t1(){O.lFrame.isParent=!1}function xf(){return _f}function ud(e){_f=e}function n1(){let e=O.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function r1(e){return O.lFrame.bindingIndex=e}function mc(){return O.lFrame.bindingIndex++}function o1(e){let t=O.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function i1(){return O.lFrame.inI18n}function s1(e,t){let n=O.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ta(t)}function a1(){return O.lFrame.currentDirectiveIndex}function Ta(e){O.lFrame.currentDirectiveIndex=e}function c1(e){let t=O.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function Tf(e){O.lFrame.currentQueryIndex=e}function l1(e){let t=e[k];return t.type===2?t.declTNode:t.type===1?e[He]:null}function Ff(e,t,n){if(n&N.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&N.Host);)if(o=l1(i),o===null||(i=i[Ln],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=O.lFrame=Nf();return r.currentTNode=t,r.lView=e,!0}function yc(e){let t=Nf(),n=e[k];O.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Nf(){let e=O.lFrame,t=e===null?null:e.child;return t===null?Of(e):t}function Of(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function Rf(){let e=O.lFrame;return O.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Uf=Rf;function vc(){let e=Rf();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function u1(e){return(O.lFrame.contextLView=W0(e,O.lFrame.contextLView))[je]}function jn(){return O.lFrame.selectedIndex}function Wt(e){O.lFrame.selectedIndex=e}function Pf(){let e=O.lFrame;return wf(e.tView,e.selectedIndex)}function Bn(){O.lFrame.currentNamespace=Cf}function Hn(){d1()}function d1(){O.lFrame.currentNamespace=null}function f1(){return O.lFrame.currentNamespace}var kf=!0;function Cc(){return kf}function Dc(e){kf=e}function h1(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=mf(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function wc(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:l,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),c&&(e.viewHooks??=[]).push(-n,c),l&&((e.viewHooks??=[]).push(n,l),(e.viewCheckHooks??=[]).push(n,l)),u!=null&&(e.destroyHooks??=[]).push(n,u)}}function Qo(e,t,n){Lf(e,t,3,n)}function Jo(e,t,n,r){(e[S]&3)===n&&Lf(e,t,n,r)}function ua(e,t){let n=e[S];(n&3)===t&&(n&=16383,n+=1,e[S]=n)}function Lf(e,t,n,r){let o=r!==void 0?e[Dn]&65535:0,i=r??-1,s=t.length-1,a=0;for(let c=o;c<s;c++)if(typeof t[c+1]=="number"){if(a=t[c],r!=null&&a>=r)break}else t[c]<0&&(e[Dn]+=65536),(a<i||i==-1)&&(p1(e,n,t,c),e[Dn]=(e[Dn]&**********)+c+2),c++}function dd(e,t){We(4,e,t);let n=B(null);try{t.call(e)}finally{B(n),We(5,e,t)}}function p1(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[S]>>14<e[Dn]>>16&&(e[S]&3)===t&&(e[S]+=16384,dd(a,i)):dd(a,i)}var bn=-1,qt=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function g1(e){return e instanceof qt}function m1(e){return(e.flags&8)!==0}function y1(e){return(e.flags&16)!==0}var da={},Fa=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Fi(r);let o=this.injector.get(t,da,r);return o!==da||n===da?o:this.parentInjector.get(t,n,r)}};function Vf(e){return e!==bn}function di(e){return e&32767}function v1(e){return e>>16}function fi(e,t){let n=v1(e),r=t;for(;n>0;)r=r[Ln],n--;return r}var Na=!0;function fd(e){let t=Na;return Na=e,t}var C1=256,jf=C1-1,Bf=5,D1=0,qe={};function w1(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(yr)&&(r=n[yr]),r==null&&(r=n[yr]=D1++);let o=r&jf,i=1<<o;t.data[e+(o>>Bf)]|=i}function hi(e,t){let n=Hf(e,t);if(n!==-1)return n;let r=t[k];r.firstCreatePass&&(e.injectorIndex=t.length,fa(r.data,e),fa(t,null),fa(r.blueprint,null));let o=Ec(e,t),i=e.injectorIndex;if(Vf(o)){let s=di(o),a=fi(o,t),c=a[k].data;for(let l=0;l<8;l++)t[i+l]=a[s+l]|c[s+l]}return t[i+8]=o,i}function fa(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Hf(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function Ec(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=qf(o),r===null)return bn;if(n++,o=o[Ln],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return bn}function Oa(e,t,n){w1(e,t,n)}function E1(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Qd(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function $f(e,t,n){if(n&N.Optional||e!==void 0)return e;sc(t,"NodeInjector")}function zf(e,t,n,r){if(n&N.Optional&&r===void 0&&(r=null),!(n&(N.Self|N.Host))){let o=e[An],i=Te(void 0);try{return o?o.get(t,r,n&N.Optional):Wd(t,r,n&N.Optional)}finally{Te(i)}}return $f(r,t,n)}function Gf(e,t,n,r=N.Default,o){if(e!==null){if(t[S]&2048&&!(r&N.Self)){let s=S1(e,t,n,r,qe);if(s!==qe)return s}let i=Wf(e,t,n,r,qe);if(i!==qe)return i}return zf(t,n,r,o)}function Wf(e,t,n,r,o){let i=_1(n);if(typeof i=="function"){if(!Ff(t,e,r))return r&N.Host?$f(o,n,r):zf(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&N.Optional))sc(n);else return s}finally{Uf()}}else if(typeof i=="number"){let s=null,a=Hf(e,t),c=bn,l=r&N.Host?t[Ke][He]:null;for((a===-1||r&N.SkipSelf)&&(c=a===-1?Ec(e,t):t[a+8],c===bn||!pd(r,!1)?a=-1:(s=t[k],a=di(c),t=fi(c,t)));a!==-1;){let u=t[k];if(hd(i,a,u.data)){let d=b1(a,t,n,s,r,l);if(d!==qe)return d}c=t[a+8],c!==bn&&pd(r,t[k].data[a+8]===l)&&hd(i,a,t)?(s=u,a=di(c),t=fi(c,t)):a=-1}}return o}function b1(e,t,n,r,o,i){let s=t[k],a=s.data[e+8],c=r==null?Pi(a)&&Na:r!=s&&(a.type&3)!==0,l=o&N.Host&&i===a,u=I1(a,s,n,c,l);return u!==null?Fn(t,s,u,a):qe}function I1(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,c=e.directiveStart,l=e.directiveEnd,u=i>>20,d=r?a:a+u,m=o?a+u:l;for(let p=d;p<m;p++){let v=s[p];if(p<c&&n===v||p>=c&&v.type===n)return p}if(o){let p=s[c];if(p&&At(p)&&p.type===n)return c}return null}function Fn(e,t,n,r){let o=e[n],i=t.data;if(g1(o)){let s=o;s.resolving&&Jm(Qm(i[n]));let a=fd(s.canSeeViewProviders);s.resolving=!0;let c,l=s.injectImpl?Te(s.injectImpl):null,u=Ff(e,r,N.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&h1(n,i[n],t)}finally{l!==null&&Te(l),fd(a),s.resolving=!1,Uf()}}return o}function _1(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(yr)?e[yr]:void 0;return typeof t=="number"?t>=0?t&jf:M1:t}function hd(e,t,n){let r=1<<e;return!!(n[t+(e>>Bf)]&r)}function pd(e,t){return!(e&N.Self)&&!(e&N.Host&&t)}var $t=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Gf(this._tNode,this._lView,t,Fi(r),n)}};function M1(){return new $t(Ee(),q())}function Li(e){return Mr(()=>{let t=e.prototype.constructor,n=t[ti]||Ra(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[ti]||Ra(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Ra(e){return jd(e)?()=>{let t=Ra(he(e));return t&&t()}:In(e)}function S1(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[S]&2048&&!(s[S]&512);){let a=Wf(i,s,n,r|N.Self,qe);if(a!==qe)return a;let c=i.parent;if(!c){let l=s[ff];if(l){let u=l.get(n,qe,r);if(u!==qe)return u}c=qf(s),s=s[Ln]}i=c}return o}function qf(e){let t=e[k],n=t.type;return n===2?t.declTNode:n===1?e[He]:null}function bc(e){return E1(Ee(),e)}function gd(e,t=null,n=null,r){let o=Zf(e,t,n,r);return o.resolveInjectorInitializers(),o}function Zf(e,t=null,n=null,r,o=new Set){let i=[n||Fe,S0(e)];return r=r||(typeof e=="object"?void 0:pe(e)),new wr(i,t||dc(),r||null,o)}var _e=class e{static{this.THROW_IF_NOT_FOUND=Cr}static{this.NULL=new oi}static create(t,n){if(Array.isArray(t))return gd({name:""},n,t,"");{let r=t.name??"";return gd({name:r},t.parent,t.providers,r)}}static{this.\u0275prov=w({token:e,providedIn:"any",factory:()=>E(Xd)})}static{this.__NG_ELEMENT_ID__=-1}};var A1=new b("");A1.__NG_ELEMENT_ID__=e=>{let t=Ee();if(t===null)throw new I(204,!1);if(t.type&2)return t.value;if(e&N.Optional)return null;throw new I(204,!1)};var x1="ngOriginalError";function ha(e){return e[x1]}var Ic=(()=>{class e{static{this.__NG_ELEMENT_ID__=T1}static{this.__NG_ENV_ID__=n=>n}}return e})(),Ua=class extends Ic{constructor(t){super(),this._lView=t}onDestroy(t){return If(this._lView,t),()=>q0(this._lView,t)}};function T1(){return new Ua(q())}var ut=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new ie(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}}return e})();var Pa=class extends J{constructor(t=!1){super(),this.destroyRef=void 0,this.pendingTasks=void 0,this.__isAsync=t,df()&&(this.destroyRef=y(Ic,{optional:!0})??void 0,this.pendingTasks=y(ut,{optional:!0})??void 0)}emit(t){let n=B(null);try{super.next(t)}finally{B(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let c=t;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof Z&&t.add(a),a}wrapInTimeout(t){return n=>{let r=this.pendingTasks?.add();setTimeout(()=>{t(n),r!==void 0&&this.pendingTasks?.remove(r)})}}},Q=Pa;function pi(...e){}function Xf(e){let t,n;function r(){e=pi;try{n!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(n),t!==void 0&&clearTimeout(t)}catch{}}return t=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(n=requestAnimationFrame(()=>{e(),r()})),()=>r()}function md(e){return queueMicrotask(()=>e()),()=>{e=pi}}var $=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Q(!1),this.onMicrotaskEmpty=new Q(!1),this.onStable=new Q(!1),this.onError=new Q(!1),typeof Zone>"u")throw new I(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.callbackScheduled=!1,O1(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new I(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new I(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,F1,pi,pi);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},F1={};function _c(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function N1(e){e.isCheckStableRunning||e.callbackScheduled||(e.callbackScheduled=!0,Zone.root.run(()=>{Xf(()=>{e.callbackScheduled=!1,ka(e),e.isCheckStableRunning=!0,_c(e),e.isCheckStableRunning=!1})}),ka(e))}function O1(e){let t=()=>{N1(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(R1(a))return n.invokeTask(o,i,s,a);try{return yd(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),vd(e)}},onInvoke:(n,r,o,i,s,a,c)=>{try{return yd(e),n.invoke(o,i,s,a,c)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!U1(a)&&t(),vd(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,ka(e),_c(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function ka(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function yd(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function vd(e){e._nesting--,_c(e)}var gi=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Q,this.onMicrotaskEmpty=new Q,this.onStable=new Q,this.onError=new Q}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function R1(e){return Kf(e,"__ignore_ng_zone__")}function U1(e){return Kf(e,"__scheduler_tick__")}function Kf(e,t){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[t]===!0}function P1(e="zone.js",t){return e==="noop"?new gi:e==="zone.js"?new $(t):e}var st=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&ha(t);for(;n&&ha(n);)n=ha(n);return n||null}},k1=new b("",{providedIn:"root",factory:()=>{let e=y($),t=y(st);return n=>e.runOutsideAngular(()=>t.handleError(n))}});function L1(){return Vi(Ee(),q())}function Vi(e,t){return new Yt(Re(e,t))}var Yt=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=L1}}return e})();function Yf(e){return(e.flags&128)===128}var Qf=new Map,V1=0;function j1(){return V1++}function B1(e){Qf.set(e[Ui],e)}function H1(e){Qf.delete(e[Ui])}var Cd="__ngContext__";function Zt(e,t){It(t)?(e[Cd]=t[Ui],B1(t)):e[Cd]=t}function Jf(e){return th(e[Er])}function eh(e){return th(e[Le])}function th(e){for(;e!==null&&!lt(e);)e=e[Le];return e}var La;function nh(e){La=e}function $1(){if(La!==void 0)return La;if(typeof document<"u")return document;throw new I(210,!1)}var ji=new b("",{providedIn:"root",factory:()=>z1}),z1="ng",Mc=new b(""),Qe=new b("",{providedIn:"platform",factory:()=>"unknown"});var Sc=new b("",{providedIn:"root",factory:()=>$1().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var G1="h",W1="b";var q1=()=>null;function Ac(e,t,n=!1){return q1(e,t,n)}var rh=!1,Z1=new b("",{providedIn:"root",factory:()=>rh});var qo;function X1(){if(qo===void 0&&(qo=null,Ve.trustedTypes))try{qo=Ve.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return qo}function Dd(e){return X1()?.createScriptURL(e)||e}var mi=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ld})`}};function xr(e){return e instanceof mi?e.changingThisBreaksApplicationSecurity:e}function xc(e,t){let n=K1(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Ld})`)}return n===t}function K1(e){return e instanceof mi&&e.getTypeName()||null}var Y1=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function oh(e){return e=String(e),e.match(Y1)?e:"unsafe:"+e}var Bi=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Bi||{});function Qt(e){let t=sh();return t?t.sanitize(Bi.URL,e)||"":xc(e,"URL")?xr(e):oh(Ti(e))}function Q1(e){let t=sh();if(t)return Dd(t.sanitize(Bi.RESOURCE_URL,e)||"");if(xc(e,"ResourceURL"))return Dd(xr(e));throw new I(904,!1)}function J1(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Q1:Qt}function ih(e,t,n){return J1(t,n)(e)}function sh(){let e=q();return e&&e[Xe].sanitizer}function ah(e){return e instanceof Function?e():e}function e2(e){return(e??y(_e)).get(Qe)==="browser"}var at=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(at||{}),t2;function Tc(e,t){return t2(e,t)}function wn(e,t,n,r,o){if(r!=null){let i,s=!1;lt(r)?i=r:It(r)&&(s=!0,r=r[ct]);let a=Ye(r);e===0&&n!==null?o==null?fh(t,n,a):yi(t,n,a,o||null,!0):e===1&&n!==null?yi(t,n,a,o||null,!0):e===2?m2(t,a,s):e===3&&t.destroyNode(a),i!=null&&v2(t,e,i,n,o)}}function n2(e,t){return e.createText(t)}function r2(e,t,n){e.setValue(t,n)}function ch(e,t,n){return e.createElement(t,n)}function o2(e,t){lh(e,t),t[ct]=null,t[He]=null}function i2(e,t,n,r,o,i){r[ct]=o,r[He]=t,Hi(e,r,n,1,o,i)}function lh(e,t){t[Xe].changeDetectionScheduler?.notify(8),Hi(e,t,t[se],2,null,null)}function s2(e){let t=e[Er];if(!t)return pa(e[k],e);for(;t;){let n=null;if(It(t))n=t[Er];else{let r=t[Oe];r&&(n=r)}if(!n){for(;t&&!t[Le]&&t!==e;)It(t)&&pa(t[k],t),t=t[me];t===null&&(t=e),It(t)&&pa(t[k],t),n=t&&t[Le]}t=n}}function a2(e,t,n,r){let o=Oe+r,i=n.length;r>0&&(n[o-1][Le]=t),r<i-Oe?(t[Le]=n[o],Zd(n,Oe+r,t)):(n.push(t),t[Le]=null),t[me]=n;let s=t[xn];s!==null&&n!==s&&uh(s,t);let a=t[Tn];a!==null&&a.insertView(e),xa(t),t[S]|=128}function uh(e,t){let n=e[ci],r=t[me];if(It(r))e[S]|=li.HasTransplantedViews;else{let o=r[me][Ke];t[Ke]!==o&&(e[S]|=li.HasTransplantedViews)}n===null?e[ci]=[t]:n.push(t)}function Fc(e,t){let n=e[ci],r=n.indexOf(t);n.splice(r,1)}function Va(e,t){if(e.length<=Oe)return;let n=Oe+t,r=e[n];if(r){let o=r[xn];o!==null&&o!==e&&Fc(o,r),t>0&&(e[n-1][Le]=r[Le]);let i=ri(e,Oe+t);o2(r[k],r);let s=i[Tn];s!==null&&s.detachView(i[k]),r[me]=null,r[Le]=null,r[S]&=-129}return r}function dh(e,t){if(!(t[S]&256)){let n=t[se];n.destroyNode&&Hi(e,t,n,3,null,null),s2(t)}}function pa(e,t){if(t[S]&256)return;let n=B(null);try{t[S]&=-129,t[S]|=256,t[Ne]&&$s(t[Ne]),l2(e,t),c2(e,t),t[k].type===1&&t[se].destroy();let r=t[xn];if(r!==null&&lt(t[me])){r!==t[me]&&Fc(r,t);let o=t[Tn];o!==null&&o.detachView(e)}H1(t)}finally{B(n)}}function c2(e,t){let n=e.cleanup,r=t[si];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[si]=null);let o=t[bt];if(o!==null){t[bt]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function l2(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof qt)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];We(4,a,c);try{c.call(a)}finally{We(5,a,c)}}else{We(4,o,i);try{i.call(o)}finally{We(5,o,i)}}}}}function u2(e,t,n){return d2(e,t.parent,n)}function d2(e,t,n){let r=t;for(;r!==null&&r.type&168;)t=r,r=t.parent;if(r===null)return n[ct];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===Ze.None||i===Ze.Emulated)return null}return Re(r,n)}}function yi(e,t,n,r,o){e.insertBefore(t,n,r,o)}function fh(e,t,n){e.appendChild(t,n)}function wd(e,t,n,r,o){r!==null?yi(e,t,n,r,o):fh(e,t,n)}function hh(e,t){return e.parentNode(t)}function f2(e,t){return e.nextSibling(t)}function h2(e,t,n){return g2(e,t,n)}function p2(e,t,n){return e.type&40?Re(e,n):null}var g2=p2,Ed;function Nc(e,t,n,r){let o=u2(e,r,t),i=t[se],s=r.parent||t[He],a=h2(s,r,t);if(o!=null)if(Array.isArray(n))for(let c=0;c<n.length;c++)wd(i,o,n[c],a,!1);else wd(i,o,n,a,!1);Ed!==void 0&&Ed(i,r,t,n,o)}function mr(e,t){if(t!==null){let n=t.type;if(n&3)return Re(t,e);if(n&4)return ja(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return mr(e,r);{let o=e[t.index];return lt(o)?ja(-1,o):Ye(o)}}else{if(n&128)return mr(e,t.next);if(n&32)return Tc(t,e)()||Ye(e[t.index]);{let r=ph(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=Gt(e[Ke]);return mr(o,r)}else return mr(e,t.next)}}}return null}function ph(e,t){if(t!==null){let r=e[Ke][He],o=t.projection;return r.projection[o]}return null}function ja(e,t){let n=Oe+e+1;if(n<t.length){let r=t[n],o=r[k].firstChild;if(o!==null)return mr(r,o)}return t[zt]}function m2(e,t,n){e.removeChild(null,t,n)}function Oc(e,t,n,r,o,i,s){for(;n!=null;){if(n.type===128){n=n.next;continue}let a=r[n.index],c=n.type;if(s&&t===0&&(a&&Zt(Ye(a),r),n.flags|=2),(n.flags&32)!==32)if(c&8)Oc(e,t,n.child,r,o,i,!1),wn(t,e,o,a,i);else if(c&32){let l=Tc(n,r),u;for(;u=l();)wn(t,e,o,u,i);wn(t,e,o,a,i)}else c&16?y2(e,t,r,n,o,i):wn(t,e,o,a,i);n=s?n.projectionNext:n.next}}function Hi(e,t,n,r,o,i){Oc(n,r,e.firstChild,t,o,i,!1)}function y2(e,t,n,r,o,i){let s=n[Ke],c=s[He].projection[r.projection];if(Array.isArray(c))for(let l=0;l<c.length;l++){let u=c[l];wn(t,e,o,u,i)}else{let l=c,u=s[me];Yf(r)&&(l.flags|=128),Oc(e,t,l,u,o,i,!0)}}function v2(e,t,n,r,o){let i=n[zt],s=Ye(n);i!==s&&wn(t,e,r,i,o);for(let a=Oe;a<n.length;a++){let c=n[a];Hi(c[k],c,e,t,r,i)}}function C2(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:at.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=at.Important),e.setStyle(n,r,o,i))}}function D2(e,t,n){e.setAttribute(t,"style",n)}function gh(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function mh(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&ba(e,t,r),o!==null&&gh(e,t,o),i!==null&&D2(e,t,i)}var Jt={};function ne(e=1){yh($e(),q(),jn()+e,!1)}function yh(e,t,n,r){if(!r)if((t[S]&3)===3){let i=e.preOrderCheckHooks;i!==null&&Qo(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Jo(t,i,0,n)}Wt(n)}function z(e,t=N.Default){let n=q();if(n===null)return E(e,t);let r=Ee();return Gf(r,n,he(e),t)}function vh(){let e="invalid";throw new Error(e)}function Ch(e,t,n,r,o,i){let s=B(null);try{let a=null;o&_t.SignalBased&&(a=t[r][yt]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&_t.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):gf(t,a,r,i)}finally{B(s)}}function w2(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)Wt(~o);else{let i=o,s=n[++r],a=n[++r];s1(s,i);let c=t[i];a(2,c)}}}finally{Wt(-1)}}function $i(e,t,n,r,o,i,s,a,c,l,u){let d=t.blueprint.slice();return d[ct]=o,d[S]=r|4|128|8|64,(l!==null||e&&e[S]&2048)&&(d[S]|=2048),Ef(d),d[me]=d[Ln]=e,d[je]=n,d[Xe]=s||e&&e[Xe],d[se]=a||e&&e[se],d[An]=c||e&&e[An]||null,d[He]=i,d[Ui]=j1(),d[ii]=u,d[ff]=l,d[Ke]=t.type==2?e[Ke]:d,d}function zi(e,t,n,r,o){let i=e.data[t];if(i===null)i=E2(e,t,n,r,o),i1()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=e1();i.injectorIndex=s===null?-1:s.injectorIndex}return Ar(i,!0),i}function E2(e,t,n,r,o){let i=Sf(),s=Af(),a=s?i:i&&i.parent,c=e.data[t]=S2(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=c),i!==null&&(s?i.child==null&&c.parent!==null&&(i.child=c):i.next===null&&(i.next=c,c.prev=i)),c}function Dh(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function wh(e,t,n,r,o){let i=jn(),s=r&2;try{Wt(-1),s&&t.length>St&&yh(e,t,St,!1),We(s?2:0,o),n(r,o)}finally{Wt(i),We(s?3:1,o)}}function Eh(e,t,n){if(pf(t)){let r=B(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=n[s];a.contentQueries(1,c,s)}}}finally{B(r)}}}function bh(e,t,n){Mf()&&(R2(e,t,n,Re(n,t)),(n.flags&64)===64&&Ah(e,t,n))}function Ih(e,t,n=Re){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function _h(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=Rc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Rc(e,t,n,r,o,i,s,a,c,l,u){let d=St+r,m=d+o,p=b2(d,m),v=typeof l=="function"?l():l;return p[k]={type:e,blueprint:p,template:n,queries:null,viewQuery:a,declTNode:t,data:p.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:m,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:v,incompleteFirstPass:!1,ssrId:u}}function b2(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Jt);return n}function I2(e,t,n,r){let i=r.get(Z1,rh)||n===Ze.ShadowDom,s=e.selectRootElement(t,i);return _2(s),s}function _2(e){M2(e)}var M2=()=>null;function S2(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return Y0()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function bd(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,c=_t.None;Array.isArray(s)?(a=s[0],c=s[1]):a=s;let l=i;if(o!==null){if(!o.hasOwnProperty(i))continue;l=o[i]}e===0?Id(r,n,l,a,c):Id(r,n,l,a)}return r}function Id(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function A2(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],c=null,l=null;for(let u=r;u<o;u++){let d=i[u],m=n?n.get(d):null,p=m?m.inputs:null,v=m?m.outputs:null;c=bd(0,d.inputs,u,c,p),l=bd(1,d.outputs,u,l,v);let D=c!==null&&s!==null&&!lc(t)?G2(c,u,s):null;a.push(D)}c!==null&&(c.hasOwnProperty("class")&&(t.flags|=8),c.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=c,t.outputs=l}function x2(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function T2(e,t,n,r,o,i,s,a){let c=Re(t,n),l=t.inputs,u;!a&&l!=null&&(u=l[r])?(Uc(e,n,u,r,o),Pi(t)&&F2(n,t.index)):t.type&3?(r=x2(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(c,r,o)):t.type&12}function F2(e,t){let n=Tt(t,e);n[S]&16||(n[S]|=64)}function Mh(e,t,n,r){if(Mf()){let o=r===null?null:{"":-1},i=P2(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&Sh(e,t,n,s,o,a),o&&k2(n,r,o)}n.mergedAttrs=Dr(n.mergedAttrs,n.attrs)}function Sh(e,t,n,r,o,i){for(let l=0;l<r.length;l++)Oa(hi(n,t),e,r[l].type);V2(n,e.data.length,r.length);for(let l=0;l<r.length;l++){let u=r[l];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,c=Dh(e,t,r.length,null);for(let l=0;l<r.length;l++){let u=r[l];n.mergedAttrs=Dr(n.mergedAttrs,u.hostAttrs),j2(e,n,t,c,u),L2(c,u,o),u.contentQueries!==null&&(n.flags|=4),(u.hostBindings!==null||u.hostAttrs!==null||u.hostVars!==0)&&(n.flags|=64);let d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),c++}A2(e,n,i)}function N2(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;O2(s)!=a&&s.push(a),s.push(n,r,i)}}function O2(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function R2(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;Pi(n)&&B2(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||hi(n,t),Zt(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let c=e.data[a],l=Fn(t,e,a,n);if(Zt(l,t),s!==null&&z2(t,a-o,l,c,n,s),At(c)){let u=Tt(n.index,t);u[je]=Fn(t,e,a,n)}}}function Ah(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=a1();try{Wt(i);for(let a=r;a<o;a++){let c=e.data[a],l=t[a];Ta(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&U2(c,l)}}finally{Wt(-1),Ta(s)}}function U2(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function P2(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(v0(t,s.selectors,!1))if(r||(r=[]),At(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let c=a.length;Ba(e,t,c)}else r.unshift(s),Ba(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function Ba(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function k2(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new I(-301,!1);r.push(t[o],i)}}}function L2(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;At(t)&&(n[""]=e)}}function V2(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function j2(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=In(o.type,!0)),s=new qt(i,At(o),z);e.blueprint[r]=s,n[r]=s,N2(e,t,r,Dh(e,n,o.hostVars,Jt),o)}function B2(e,t,n){let r=Re(t,e),o=_h(n),i=e[Xe].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=Gi(e,$i(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function H2(e,t,n,r,o,i){let s=Re(e,t);$2(t[se],s,i,e.value,n,r,o)}function $2(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?Ti(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function z2(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let c=s[a++],l=s[a++],u=s[a++],d=s[a++];Ch(r,n,c,l,u,d)}}function G2(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function xh(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function Th(e,t){let n=e.contentQueries;if(n!==null){let r=B(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Tf(i),a.contentQueries(2,t[s],s)}}}finally{B(r)}}}function Gi(e,t){return e[Er]?e[cd][Le]=t:e[Er]=t,e[cd]=t,t}function Ha(e,t,n){Tf(0);let r=B(null);try{t(e,n)}finally{B(r)}}function W2(e){return e[si]??=[]}function q2(e){return e.cleanup??=[]}function Fh(e,t){let n=e[An],r=n?n.get(st,null):null;r&&r.handleError(t)}function Uc(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],c=n[i++],l=t[s],u=e.data[s];Ch(u,l,r,a,c,o)}}function Z2(e,t,n){let r=Df(t,e);r2(e[se],r,n)}function X2(e,t){let n=Tt(t,e),r=n[k];K2(r,n);let o=n[ct];o!==null&&n[ii]===null&&(n[ii]=Ac(o,n[An])),Pc(r,n,n[je])}function K2(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function Pc(e,t,n){yc(t);try{let r=e.viewQuery;r!==null&&Ha(1,r,n);let o=e.template;o!==null&&wh(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Tn]?.finishViewCreation(e),e.staticContentQueries&&Th(e,t),e.staticViewQueries&&Ha(2,e.viewQuery,n);let i=e.components;i!==null&&Y2(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[S]&=-5,vc()}}function Y2(e,t){for(let n=0;n<t.length;n++)X2(e,t[n])}function Q2(e,t,n,r){let o=B(null);try{let i=t.tView,a=e[S]&4096?4096:16,c=$i(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),l=e[t.index];c[xn]=l;let u=e[Tn];return u!==null&&(c[Tn]=u.createEmbeddedView(i)),Pc(i,c,n),c}finally{B(o)}}function _d(e,t){return!t||t.firstChild===null||Yf(e)}function J2(e,t,n,r=!0){let o=t[k];if(a2(o,t,e,n),r){let s=ja(n,e),a=t[se],c=hh(a,e[zt]);c!==null&&i2(o,e[He],a,t,c,s)}let i=t[ii];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function vi(e,t,n,r,o=!1){for(;n!==null;){if(n.type===128){n=o?n.projectionNext:n.next;continue}let i=t[n.index];i!==null&&r.push(Ye(i)),lt(i)&&ey(i,r);let s=n.type;if(s&8)vi(e,t,n.child,r);else if(s&32){let a=Tc(n,t),c;for(;c=a();)r.push(c)}else if(s&16){let a=ph(t,n);if(Array.isArray(a))r.push(...a);else{let c=Gt(t[Ke]);vi(c[k],c,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function ey(e,t){for(let n=Oe;n<e.length;n++){let r=e[n],o=r[k].firstChild;o!==null&&vi(r[k],r,o,t)}e[zt]!==e[ct]&&t.push(e[zt])}var Nh=[];function ty(e){return e[Ne]??ny(e)}function ny(e){let t=Nh.pop()??Object.create(oy);return t.lView=e,t}function ry(e){e.lView[Ne]!==e&&(e.lView=null,Nh.push(e))}var oy=j(C({},fr),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{ki(e.lView)},consumerOnSignalRead(){this.lView[Ne]=this}});function iy(e){let t=e[Ne]??Object.create(sy);return t.lView=e,t}var sy=j(C({},fr),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{let t=Gt(e.lView);for(;t&&!Oh(t[k]);)t=Gt(t);t&&bf(t)},consumerOnSignalRead(){this.lView[Ne]=this}});function Oh(e){return e.type!==2}var ay=100;function Rh(e,t=!0,n=0){let r=e[Xe],o=r.rendererFactory,i=!1;i||o.begin?.();try{cy(e,n)}catch(s){throw t&&Fh(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function cy(e,t){let n=xf();try{ud(!0),$a(e,t);let r=0;for(;br(e);){if(r===ay)throw new I(103,!1);r++,$a(e,1)}}finally{ud(n)}}function ly(e,t,n,r){let o=t[S];if((o&256)===256)return;let i=!1,s=!1;!i&&t[Xe].inlineEffectRunner?.flush(),yc(t);let a=!0,c=null,l=null;i||(Oh(e)?(l=ty(t),c=Do(l)):pu()===null?(a=!1,l=iy(t),c=Do(l)):t[Ne]&&($s(t[Ne]),t[Ne]=null));try{Ef(t),r1(e.bindingStartIndex),n!==null&&wh(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let p=e.preOrderCheckHooks;p!==null&&Qo(t,p,null)}else{let p=e.preOrderHooks;p!==null&&Jo(t,p,0,null),ua(t,0)}if(s||uy(t),Uh(t,0),e.contentQueries!==null&&Th(e,t),!i)if(u){let p=e.contentCheckHooks;p!==null&&Qo(t,p)}else{let p=e.contentHooks;p!==null&&Jo(t,p,1),ua(t,1)}w2(e,t);let d=e.components;d!==null&&kh(t,d,0);let m=e.viewQuery;if(m!==null&&Ha(2,m,r),!i)if(u){let p=e.viewCheckHooks;p!==null&&Qo(t,p)}else{let p=e.viewHooks;p!==null&&Jo(t,p,2),ua(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[la]){for(let p of t[la])p();t[la]=null}i||(t[S]&=-73)}catch(u){throw i||ki(t),u}finally{l!==null&&(Bs(l,c),a&&ry(l)),vc()}}function Uh(e,t){for(let n=Jf(e);n!==null;n=eh(n))for(let r=Oe;r<n.length;r++){let o=n[r];Ph(o,t)}}function uy(e){for(let t=Jf(e);t!==null;t=eh(t)){if(!(t[S]&li.HasTransplantedViews))continue;let n=t[ci];for(let r=0;r<n.length;r++){let o=n[r];bf(o)}}}function dy(e,t,n){let r=Tt(t,e);Ph(r,n)}function Ph(e,t){hc(e)&&$a(e,t)}function $a(e,t){let r=e[k],o=e[S],i=e[Ne],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Hs(i)),s||=!1,i&&(i.dirty=!1),e[S]&=-9217,s)ly(r,e,r.template,e[je]);else if(o&8192){Uh(e,1);let a=r.components;a!==null&&kh(e,a,1)}}function kh(e,t,n){for(let r=0;r<t.length;r++)dy(e,t[r],n)}function kc(e,t){let n=xf()?64:1088;for(e[Xe].changeDetectionScheduler?.notify(t);e;){e[S]|=n;let r=Gt(e);if(Sa(e)&&!r)return e;e=r}return null}var Xt=class{get rootNodes(){let t=this._lView,n=t[k];return vi(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[je]}set context(t){this._lView[je]=t}get destroyed(){return(this._lView[S]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[me];if(lt(t)){let n=t[ai],r=n?n.indexOf(this):-1;r>-1&&(Va(t,r),ri(n,r))}this._attachedToViewContainer=!1}dh(this._lView[k],this._lView)}onDestroy(t){If(this._lView,t)}markForCheck(){kc(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[S]&=-129}reattach(){xa(this._lView),this._lView[S]|=128}detectChanges(){this._lView[S]|=1024,Rh(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new I(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let t=Sa(this._lView),n=this._lView[xn];n!==null&&!t&&Fc(n,this._lView),lh(this._lView[k],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new I(902,!1);this._appRef=t;let n=Sa(this._lView),r=this._lView[xn];r!==null&&!n&&uh(r,this._lView),xa(this._lView)}},Wi=(()=>{class e{static{this.__NG_ELEMENT_ID__=py}}return e})(),fy=Wi,hy=class extends fy{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=Q2(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new Xt(o)}};function py(){return gy(Ee(),q())}function gy(e,t){return e.type&4?new hy(t,e,Vi(e,t)):null}var U_=new RegExp(`^(\\d+)*(${W1}|${G1})*(.*)`);var my=()=>null;function Md(e,t){return my(e,t)}var Nn=class{},Lh=new b("",{providedIn:"root",factory:()=>!1});var Vh=new b(""),za=class{},Ci=class{};function yy(e){let t=Error(`No component factory found for ${pe(e)}.`);return t[vy]=e,t}var vy="ngComponent";var Ga=class{resolveComponentFactory(t){throw yy(t)}},On=class{static{this.NULL=new Ga}},Rn=class{},$n=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>Cy()}}return e})();function Cy(){let e=q(),t=Ee(),n=Tt(t.index,e);return(It(n)?n:e)[se]}var Dy=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>null})}}return e})();var Sd=new Set;function zn(e){Sd.has(e)||(Sd.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var we=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(we||{}),wy={destroy(){}};function Lc(e,t){!t&&L0(Lc);let n=t?.injector??y(_e);return e2(n)?(zn("NgAfterNextRender"),by(e,n,!0,t?.phase??we.MixedReadWrite)):wy}function Ey(e,t){if(e instanceof Function)switch(t){case we.EarlyRead:return{earlyRead:e};case we.Write:return{write:e};case we.MixedReadWrite:return{mixedReadWrite:e};case we.Read:return{read:e}}return e}function by(e,t,n,r){let o=Ey(e,r),i=t.get(Vc),s=i.handler??=new qa,a=[],c=[],l=()=>{for(let p of c)s.unregister(p);u()},u=t.get(Ic).onDestroy(l),d=0,m=(p,v)=>{if(!v)return;let D=n?(...T)=>(d--,d<1&&l(),v(...T)):v,_=Me(t,()=>new Wa(p,a,D));s.register(_),c.push(_),d++};return m(we.EarlyRead,o.earlyRead),m(we.Write,o.write),m(we.MixedReadWrite,o.mixedReadWrite),m(we.Read,o.read),{destroy:l}}var Wa=class{constructor(t,n,r){this.phase=t,this.pipelinedArgs=n,this.callbackFn=r,this.zone=y($),this.errorHandler=y(st,{optional:!0}),y(Nn,{optional:!0})?.notify(6)}invoke(){try{let t=this.zone.runOutsideAngular(()=>this.callbackFn.apply(null,this.pipelinedArgs));this.pipelinedArgs.splice(0,this.pipelinedArgs.length,t)}catch(t){this.errorHandler?.handleError(t)}}},qa=class{constructor(){this.executingCallbacks=!1,this.buckets={[we.EarlyRead]:new Set,[we.Write]:new Set,[we.MixedReadWrite]:new Set,[we.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},Vc=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Za(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=Qu(o,a);else if(i==2){let c=a,l=t[++s];r=Qu(r,c+": "+l+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var Di=class extends On{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Mt(t);return new Un(n,this.ngModule)}};function Ad(e,t){let n=[];for(let r in e){if(!e.hasOwnProperty(r))continue;let o=e[r];if(o===void 0)continue;let i=Array.isArray(o),s=i?o[0]:o,a=i?o[1]:_t.None;t?n.push({propName:s,templateName:r,isSignal:(a&_t.SignalBased)!==0}):n.push({propName:s,templateName:r})}return n}function Iy(e){let t=e.toLowerCase();return t==="svg"?Cf:t==="math"?z0:null}var Un=class extends Ci{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=Ad(t.inputs,!0);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Ad(this.componentDef.outputs,!1)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=E0(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=B(null);try{o=o||this.ngModule;let s=o instanceof ge?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new Fa(t,s):t,c=a.get(Rn,null);if(c===null)throw new I(407,!1);let l=a.get(Dy,null),u=a.get(Vc,null),d=a.get(Nn,null),m={rendererFactory:c,sanitizer:l,inlineEffectRunner:null,afterRenderEventManager:u,changeDetectionScheduler:d},p=c.createRenderer(null,this.componentDef),v=this.componentDef.selectors[0][0]||"div",D=r?I2(p,r,this.componentDef.encapsulation,a):ch(p,v,Iy(v)),_=512;this.componentDef.signals?_|=4096:this.componentDef.onPush||(_|=16);let T=null;D!==null&&(T=Ac(D,a,!0));let de=Rc(0,null,null,1,0,null,null,null,null,null,null),U=$i(null,de,null,_,null,null,m,p,a,null,T);yc(U);let fe,ye;try{let oe=this.componentDef,nt,ks=null;oe.findHostDirectiveDefs?(nt=[],ks=new Map,oe.findHostDirectiveDefs(oe,nt,ks),nt.push(oe)):nt=[oe];let lm=_y(U,D),um=My(lm,D,oe,nt,U,m,p);ye=wf(de,St),D&&xy(p,oe,D,r),n!==void 0&&Ty(ye,this.ngContentSelectors,n),fe=Ay(um,oe,nt,ks,U,[Fy]),Pc(de,U,null)}finally{vc()}return new Xa(this.componentType,fe,Vi(ye,U),U,ye)}finally{B(i)}}},Xa=class extends za{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new Xt(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;Uc(i[k],i,o,t,n),this.previousInputValues.set(t,n);let s=Tt(this._tNode.index,i);kc(s,1)}}get injector(){return new $t(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function _y(e,t){let n=e[k],r=St;return e[r]=t,zi(n,r,2,"#host",null)}function My(e,t,n,r,o,i,s){let a=o[k];Sy(r,e,t,s);let c=null;t!==null&&(c=Ac(t,o[An]));let l=i.rendererFactory.createRenderer(t,n),u=16;n.signals?u=4096:n.onPush&&(u=64);let d=$i(o,_h(n),null,u,o[e.index],e,i,l,null,null,c);return a.firstCreatePass&&Ba(a,e,r.length-1),Gi(o,d),o[e.index]=d}function Sy(e,t,n,r){for(let o of e)t.mergedAttrs=Dr(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(Za(t,t.mergedAttrs,!0),n!==null&&mh(r,n,t))}function Ay(e,t,n,r,o,i){let s=Ee(),a=o[k],c=Re(s,o);Sh(a,o,s,n,null,r);for(let u=0;u<n.length;u++){let d=s.directiveStart+u,m=Fn(o,a,d,s);Zt(m,o)}Ah(a,o,s),c&&Zt(c,o);let l=Fn(o,a,s.directiveStart+s.componentOffset,s);if(e[je]=o[je]=l,i!==null)for(let u of i)u(l,t);return Eh(a,s,o),l}function xy(e,t,n,r){if(r)ba(e,n,["ng-version","18.1.4"]);else{let{attrs:o,classes:i}=b0(t.selectors[0]);o&&ba(e,n,o),i&&i.length>0&&gh(e,n,i.join(" "))}}function Ty(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Fy(){let e=Ee();wc(q()[k],e)}var Gn=(()=>{class e{static{this.__NG_ELEMENT_ID__=Ny}}return e})();function Ny(){let e=Ee();return Ry(e,q())}var Oy=Gn,jh=class extends Oy{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Vi(this._hostTNode,this._hostLView)}get injector(){return new $t(this._hostTNode,this._hostLView)}get parentInjector(){let t=Ec(this._hostTNode,this._hostLView);if(Vf(t)){let n=fi(t,this._hostLView),r=di(t),o=n[k].data[r+8];return new $t(o,n)}else return new $t(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=xd(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-Oe}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Md(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,_d(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!j0(t),a;if(s)a=n;else{let v=n||{};a=v.index,r=v.injector,o=v.projectableNodes,i=v.environmentInjector||v.ngModuleRef}let c=s?t:new Un(Mt(t)),l=r||this.parentInjector;if(!i&&c.ngModule==null){let D=(s?l:this.parentInjector).get(ge,null);D&&(i=D)}let u=Mt(c.componentType??{}),d=Md(this._lContainer,u?.id??null),m=d?.firstChild??null,p=c.create(l,o,m,i);return this.insertImpl(p.hostView,a,_d(this._hostTNode,d)),p}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(G0(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let c=o[me],l=new jh(c,c[He],c[me]);l.detach(l.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return J2(s,o,i,r),t.attachToViewContainerRef(),Zd(ga(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=xd(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Va(this._lContainer,n);r&&(ri(ga(this._lContainer),n),dh(r[k],r))}detach(t){let n=this._adjustIndex(t,-1),r=Va(this._lContainer,n);return r&&ri(ga(this._lContainer),n)!=null?new Xt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function xd(e){return e[ai]}function ga(e){return e[ai]||(e[ai]=[])}function Ry(e,t){let n,r=t[e.index];return lt(r)?n=r:(n=xh(r,t,null,e),t[e.index]=n,Gi(t,n)),Py(n,t,e,r),new jh(n,e,t)}function Uy(e,t){let n=e[se],r=n.createComment(""),o=Re(t,e),i=hh(n,o);return yi(n,i,r,f2(n,o),!1),r}var Py=Vy,ky=()=>!1;function Ly(e,t,n){return ky(e,t,n)}function Vy(e,t,n,r){if(e[zt])return;let o;n.type&8?o=Ye(r):o=Uy(t,n),e[zt]=o}function Tr(e,t){zn("NgSignals");let n=_u(e),r=n[yt];return t?.equal&&(r.equal=t.equal),n.set=o=>zs(r,o),n.update=o=>Mu(r,o),n.asReadonly=jy.bind(n),n}function jy(){let e=this[yt];if(e.readonlyFn===void 0){let t=()=>this();t[yt]=e,e.readonlyFn=t}return e.readonlyFn}function By(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(Gy))}return i}return wi.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(l=>{o.template=l}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let l=o.styles.length,u=o.styleUrls;o.styleUrls.forEach((d,m)=>{a.push(""),s.push(r(d).then(p=>{a[l+m]=p,u.splice(u.indexOf(d),1),u.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(l=>{a.push(l),o.styleUrl=void 0}));let c=Promise.all(s).then(()=>Wy(i));t.push(c)}),$y(),Promise.all(t).then(()=>{})}var wi=new Map,Hy=new Set;function $y(){let e=wi;return wi=new Map,e}function zy(){return wi.size===0}function Gy(e){return typeof e=="string"?e:e.text()}function Wy(e){Hy.delete(e)}function qy(e){return Object.getPrototypeOf(e.prototype).constructor}function qi(e){let t=qy(e.type),n=!0,r=[e];for(;t;){let o;if(At(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new I(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Zo(e.inputs),s.inputTransforms=Zo(e.inputTransforms),s.declaredInputs=Zo(e.declaredInputs),s.outputs=Zo(e.outputs);let a=o.hostBindings;a&&Qy(e,a);let c=o.viewQuery,l=o.contentQueries;if(c&&Ky(e,c),l&&Yy(e,l),Zy(e,o),zm(e.outputs,o.outputs),At(o)&&o.data.animation){let u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===qi&&(n=!1)}}t=Object.getPrototypeOf(t)}Xy(r)}function Zy(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function Xy(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Dr(o.hostAttrs,n=Dr(n,o.hostAttrs))}}function Zo(e){return e===_n?{}:e===Fe?[]:e}function Ky(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function Yy(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function Qy(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function jc(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var xt=class{},Ir=class{};var Ei=class extends xt{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Di(this);let o=rf(t);this._bootstrapComponents=ah(o.bootstrap),this._r3Injector=Zf(t,n,[{provide:xt,useValue:this},{provide:On,useValue:this.componentFactoryResolver},...r],pe(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},bi=class extends Ir{constructor(t){super(),this.moduleType=t}create(t){return new Ei(this.moduleType,t,[])}};function Jy(e,t,n){return new Ei(e,t,n)}var Ka=class extends xt{constructor(t){super(),this.componentFactoryResolver=new Di(this),this.instance=null;let n=new wr([...t.providers,{provide:xt,useValue:this},{provide:On,useValue:this.componentFactoryResolver}],t.parent||dc(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function Zi(e,t,n=null){return new Ka({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}function Bh(e){return tv(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function ev(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function tv(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function nv(e,t,n){return e[t]=n}function Pn(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function rv(e,t,n,r){let o=Pn(e,t,n);return Pn(e,t+1,r)||o}function ov(e){return(e.flags&32)===32}function iv(e,t,n,r,o,i,s,a,c){let l=t.consts,u=zi(t,e,4,s||null,a||null);Mh(t,n,u,ui(l,c)),wc(t,u);let d=u.tView=Rc(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,l,null);return t.queries!==null&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}function sv(e,t,n,r,o,i,s,a,c,l){let u=n+St,d=t.firstCreatePass?iv(u,t,e,r,o,i,s,a,c):t.data[u];Ar(d,!1);let m=av(t,e,d,n);Cc()&&Nc(t,e,m,d),Zt(m,e);let p=xh(m,e,m,d);return e[u]=p,Gi(e,p),Ly(p,d,e),fc(d)&&bh(t,e,d),c!=null&&Ih(e,d,l),d}function Ft(e,t,n,r,o,i,s,a){let c=q(),l=$e(),u=ui(l.consts,i);return sv(c,l,e,t,n,r,o,u,s,a),Ft}var av=cv;function cv(e,t,n,r){return Dc(!0),t[se].createComment("")}function Xi(e,t,n,r){let o=q(),i=mc();if(Pn(o,i,t)){let s=$e(),a=Pf();H2(a,o,e,t,n,r)}return Xi}function lv(e,t,n,r){return Pn(e,mc(),n)?t+Ti(n)+r:Jt}function Xo(e,t){return e<<17|t<<2}function Kt(e){return e>>17&32767}function uv(e){return(e&2)==2}function dv(e,t){return e&131071|t<<17}function Ya(e){return e|2}function kn(e){return(e&131068)>>2}function ma(e,t){return e&-131069|t<<2}function fv(e){return(e&1)===1}function Qa(e){return e|1}function hv(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Kt(s),c=kn(s);e[r]=n;let l=!1,u;if(Array.isArray(n)){let d=n;u=d[1],(u===null||Sr(d,u)>0)&&(l=!0)}else u=n;if(o)if(c!==0){let m=Kt(e[a+1]);e[r+1]=Xo(m,a),m!==0&&(e[m+1]=ma(e[m+1],r)),e[a+1]=dv(e[a+1],r)}else e[r+1]=Xo(a,0),a!==0&&(e[a+1]=ma(e[a+1],r)),a=r;else e[r+1]=Xo(c,0),a===0?a=r:e[c+1]=ma(e[c+1],r),c=r;l&&(e[r+1]=Ya(e[r+1])),Td(e,u,r,!0),Td(e,u,r,!1),pv(t,u,e,r,i),s=Xo(a,c),i?t.classBindings=s:t.styleBindings=s}function pv(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Sr(i,t)>=0&&(n[r+1]=Qa(n[r+1]))}function Td(e,t,n,r){let o=e[n+1],i=t===null,s=r?Kt(o):kn(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],l=e[s+1];gv(c,t)&&(a=!0,e[s+1]=r?Qa(l):Ya(l)),s=r?Kt(l):kn(l)}a&&(e[n+1]=r?Ya(o):Qa(o))}function gv(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Sr(e,t)>=0:!1}function Se(e,t,n){let r=q(),o=mc();if(Pn(r,o,t)){let i=$e(),s=Pf();T2(i,s,r,e,t,r[se],n,!1)}return Se}function Fd(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";Uc(e,n,i[s],s,r)}function Bc(e,t){return mv(e,t,null,!0),Bc}function mv(e,t,n,r){let o=q(),i=$e(),s=o1(2);if(i.firstUpdatePass&&vv(i,e,s,r),t!==Jt&&Pn(o,s,t)){let a=i.data[jn()];bv(i,a,o,o[se],e,o[s+1]=Iv(t,n),r,s)}}function yv(e,t){return t>=e.expandoStartIndex}function vv(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[jn()],s=yv(e,n);_v(i,r)&&t===null&&!s&&(t=!1),t=Cv(o,i,t,r),hv(o,i,t,n,s,r)}}function Cv(e,t,n,r){let o=c1(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=ya(null,e,t,n,r),n=_r(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=ya(o,e,t,n,r),i===null){let c=Dv(e,t,r);c!==void 0&&Array.isArray(c)&&(c=ya(null,e,t,c[1],r),c=_r(c,t.attrs,r),wv(e,t,r,c))}else i=Ev(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function Dv(e,t,n){let r=n?t.classBindings:t.styleBindings;if(kn(r)!==0)return e[Kt(r)]}function wv(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Kt(o)]=r}function Ev(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=_r(r,s,n)}return _r(r,t.attrs,n)}function ya(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=_r(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function _r(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),u0(e,s,n?!0:t[++i]))}return e===void 0?null:e}function bv(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let c=e.data,l=c[a+1],u=fv(l)?Nd(c,t,n,o,kn(l),s):void 0;if(!Ii(u)){Ii(i)||uv(l)&&(i=Nd(c,null,n,o,a,s));let d=Df(jn(),n);C2(r,s,d,o,i)}}function Nd(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let c=e[o],l=Array.isArray(c),u=l?c[1]:c,d=u===null,m=n[o+1];m===Jt&&(m=d?Fe:void 0);let p=d?aa(m,r):u===r?m:void 0;if(l&&!Ii(p)&&(p=aa(c,r)),Ii(p)&&(a=p,s))return a;let v=e[o+1];o=s?Kt(v):kn(v)}if(t!==null){let c=i?t.residualClasses:t.residualStyles;c!=null&&(a=aa(c,r))}return a}function Ii(e){return e!==void 0}function Iv(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=pe(xr(e)))),e}function _v(e,t){return(e.flags&(t?8:16))!==0}function Mv(e,t,n,r,o,i){let s=t.consts,a=ui(s,o),c=zi(t,e,2,r,a);return Mh(t,n,c,ui(s,i)),c.attrs!==null&&Za(c,c.attrs,!1),c.mergedAttrs!==null&&Za(c,c.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,c),c}function f(e,t,n,r){let o=q(),i=$e(),s=St+e,a=o[se],c=i.firstCreatePass?Mv(s,i,o,t,n,r):i.data[s],l=Sv(i,o,c,a,t,e);o[s]=l;let u=fc(c);return Ar(c,!0),mh(a,l,c),!ov(c)&&Cc()&&Nc(i,o,l,c),Z0()===0&&Zt(l,o),X0(),u&&(bh(i,o,c),Eh(i,c,o)),r!==null&&Ih(o,c),f}function h(){let e=Ee();Af()?t1():(e=e.parent,Ar(e,!1));let t=e;Q0(t)&&J0(),K0();let n=$e();return n.firstCreatePass&&(wc(n,e),pf(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&m1(t)&&Fd(n,t,q(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&y1(t)&&Fd(n,t,q(),t.stylesWithoutHost,!1),h}function g(e,t,n,r){return f(e,t,n,r),h(),g}var Sv=(e,t,n,r,o,i)=>(Dc(!0),ch(r,o,f1()));function Hh(){return q()}var _i="en-US";var Av=_i;function xv(e){typeof e=="string"&&(Av=e.toLowerCase().replace(/_/g,"-"))}var Tv=(e,t,n)=>{};function Je(e,t,n,r){let o=q(),i=$e(),s=Ee();return Nv(i,o,o[se],s,e,t,r),Je}function Fv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[si],c=o[i+2];return a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Nv(e,t,n,r,o,i,s){let a=fc(r),l=e.firstCreatePass&&q2(e),u=t[je],d=W2(t),m=!0;if(r.type&3||s){let D=Re(r,t),_=s?s(D):D,T=d.length,de=s?fe=>s(Ye(fe[r.index])):r.index,U=null;if(!s&&a&&(U=Fv(e,t,o,r.index)),U!==null){let fe=U.__ngLastListenerFn__||U;fe.__ngNextListenerFn__=i,U.__ngLastListenerFn__=i,m=!1}else{i=Rd(r,t,u,i),Tv(D,o,i);let fe=n.listen(_,o,i);d.push(i,fe),l&&l.push(o,de,T,T+1)}}else i=Rd(r,t,u,i);let p=r.outputs,v;if(m&&p!==null&&(v=p[o])){let D=v.length;if(D)for(let _=0;_<D;_+=2){let T=v[_],de=v[_+1],ye=t[T][de].subscribe(i),oe=d.length;d.push(i,ye),l&&l.push(o,r.index,oe,-(oe+1))}}}function Od(e,t,n,r){let o=B(null);try{return We(6,t,n),n(r)!==!1}catch(i){return Fh(e,i),!1}finally{We(7,t,n),B(o)}}function Rd(e,t,n,r){return function o(i){if(i===Function)return r;let s=e.componentOffset>-1?Tt(e.index,t):t;kc(s,5);let a=Od(t,n,r,i),c=o.__ngNextListenerFn__;for(;c;)a=Od(t,n,c,i)&&a,c=c.__ngNextListenerFn__;return a}}function Fr(e=1){return u1(e)}function R(e,t=""){let n=q(),r=$e(),o=e+St,i=r.firstCreatePass?zi(r,o,1,t,null):r.data[o],s=Ov(r,n,i,t,e);n[o]=s,Cc()&&Nc(r,n,s,i),Ar(i,!1)}var Ov=(e,t,n,r,o)=>(Dc(!0),n2(t[se],r));function Nt(e){return Wn("",e,""),Nt}function Wn(e,t,n){let r=q(),o=lv(r,e,t,n);return o!==Jt&&Z2(r,jn(),o),Wn}function Rv(e,t,n){let r=$e();if(r.firstCreatePass){let o=At(e);Ja(n,r.data,r.blueprint,o,!0),Ja(t,r.data,r.blueprint,o,!1)}}function Ja(e,t,n,r,o){if(e=he(e),Array.isArray(e))for(let i=0;i<e.length;i++)Ja(e[i],t,n,r,o);else{let i=$e(),s=q(),a=Ee(),c=Sn(e)?e:he(e.provide),l=uf(e),u=a.providerIndexes&1048575,d=a.directiveStart,m=a.providerIndexes>>20;if(Sn(e)||!e.multi){let p=new qt(l,o,z),v=Ca(c,t,o?u:u+m,d);v===-1?(Oa(hi(a,s),i,c),va(i,e,t.length),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(p),s.push(p)):(n[v]=p,s[v]=p)}else{let p=Ca(c,t,u+m,d),v=Ca(c,t,u,u+m),D=p>=0&&n[p],_=v>=0&&n[v];if(o&&!_||!o&&!D){Oa(hi(a,s),i,c);let T=kv(o?Pv:Uv,n.length,o,r,l);!o&&_&&(n[v].providerFactory=T),va(i,e,t.length,0),t.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(T),s.push(T)}else{let T=$h(n[o?v:p],l,!o&&r);va(i,e,p>-1?p:v,T)}!o&&r&&_&&n[v].componentProviders++}}}function va(e,t,n,r){let o=Sn(t),i=F0(t);if(o||i){let c=(i?he(t.useClass):t).prototype.ngOnDestroy;if(c){let l=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let u=l.indexOf(n);u===-1?l.push(n,[r,c]):l[u+1].push(r,c)}else l.push(n,c)}}}function $h(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Ca(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function Uv(e,t,n,r){return ec(this.multi,[])}function Pv(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Fn(n,n[k],this.providerFactory.index,r);i=a.slice(0,s),ec(o,i);for(let c=s;c<a.length;c++)i.push(a[c])}else i=[],ec(o,i);return i}function ec(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function kv(e,t,n,r,o){let i=new qt(e,n,z);return i.multi=[],i.index=t,i.componentProviders=0,$h(i,o,r&&!n),i}function zh(e,t=[]){return n=>{n.providersResolver=(r,o)=>Rv(r,o?o(e):e,t)}}var Lv=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=af(!1,n.type),o=r.length>0?Zi([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(E(ge))})}}return e})();function Gh(e){zn("NgStandalone"),e.getStandaloneInjector=t=>t.get(Lv).getOrCreateStandaloneInjector(e)}function Wh(e,t,n,r,o){return jv(q(),n1(),e,t,n,r,o)}function Vv(e,t){let n=e[t];return n===Jt?void 0:n}function jv(e,t,n,r,o,i,s){let a=t+n;return rv(e,a,o,i)?nv(e,a+2,s?r.call(s,o,i):r(o,i)):Vv(e,a+2)}var Ko=null;function Bv(e){Ko!==null&&(e.defaultEncapsulation!==Ko.defaultEncapsulation||e.preserveWhitespaces!==Ko.preserveWhitespaces)||(Ko=e)}var Ki=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Hc=new b(""),Nr=new b(""),Yi=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,$c||(Hv(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}isStable(){return this._isZoneStable&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static{this.\u0275fac=function(r){return new(r||e)(E($),E(Qi),E(Nr))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Qi=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return $c?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function Hv(e){$c=e}var $c;function en(e){return!!e&&typeof e.then=="function"}function qh(e){return!!e&&typeof e.subscribe=="function"}var Ji=new b(""),Zh=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=y(Ji,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(en(i))n.push(i);else if(qh(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),es=new b("");function $v(){Iu(()=>{throw new I(600,!1)})}function zv(e){return e.isBoundToModule}var Gv=10;function Wv(e,t,n){try{let r=n();return en(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Xh(e,t){return Array.isArray(t)?t.reduce(Xh,e):C(C({},e),t)}var Ot=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=y(k1),this.afterRenderEffectManager=y(Vc),this.zonelessEnabled=y(Lh),this.externalTestViews=new Set,this.beforeRender=new J,this.afterTick=new J,this.componentTypes=[],this.components=[],this.isStable=y(ut).hasPendingTasks.pipe(x(n=>!n)),this._injector=y(ge)}get allViews(){return[...this.externalTestViews.keys(),...this._views]}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Ci;if(!this._injector.get(Zh).done){let m=!o&&nf(n),p=!1;throw new I(405,p)}let s;o?s=n:s=this._injector.get(On).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=zv(s)?void 0:this._injector.get(xt),c=r||s.selector,l=s.create(_e.NULL,[],c,a),u=l.location.nativeElement,d=l.injector.get(Hc,null);return d?.registerApplication(u),l.onDestroy(()=>{this.detachView(l.hostView),ei(this.components,l),d?.unregisterApplication(u)}),this._loadComponent(l),l}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new I(101,!1);let r=B(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this._runningTick=!1,B(r),this.afterTick.next()}}detectChangesInAttachedViews(n){let r=null;this._injector.destroyed||(r=this._injector.get(Rn,null,{optional:!0}));let o=0,i=this.afterRenderEffectManager;for(;o<Gv;){let s=o===0;if(n||!s){this.beforeRender.next(s);for(let{_lView:a,notifyErrorHandler:c}of this._views)qv(a,c,s,this.zonelessEnabled)}else r?.begin?.(),r?.end?.();if(o++,i.executeInternalCallbacks(),!this.allViews.some(({_lView:a})=>br(a))&&(i.execute(),!this.allViews.some(({_lView:a})=>br(a))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;ei(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(es,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>ei(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new I(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function ei(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function qv(e,t,n,r){if(!n&&!br(e))return;Rh(e,t,n&&!r?0:1)}var tc=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ts=(()=>{class e{compileModuleSync(n){return new bi(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=rf(n),i=ah(o.declarations).reduce((s,a)=>{let c=Mt(a);return c&&s.push(new Un(c)),s},[]);return new tc(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Zv=new b("");function Xv(e,t,n){let r=new bi(n);return Promise.resolve(r)}function Ud(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var Kv=(()=>{class e{constructor(){this.zone=y($),this.changeDetectionScheduler=y(Nn),this.applicationRef=y(Ot)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Yv({ngZoneFactory:e,ignoreChangesOutsideZone:t}){return e??=()=>new $(Kh()),[{provide:$,useFactory:e},{provide:Mn,multi:!0,useFactory:()=>{let n=y(Kv,{optional:!0});return()=>n.initialize()}},{provide:Mn,multi:!0,useFactory:()=>{let n=y(Qv);return()=>{n.initialize()}}},t===!0?{provide:Vh,useValue:!0}:[]]}function Kh(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Qv=(()=>{class e{constructor(){this.subscription=new Z,this.initialized=!1,this.zone=y($),this.pendingTasks=y(ut)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{$.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{$.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var Jv=(()=>{class e{constructor(){this.appRef=y(Ot),this.taskService=y(ut),this.ngZone=y($),this.zonelessEnabled=y(Lh),this.disableScheduling=y(Vh,{optional:!0})??!1,this.zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run,this.schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}],this.subscriptions=new Z,this.cancelScheduledCallback=null,this.shouldRefreshViews=!1,this.useMicrotaskScheduler=!1,this.runningTick=!1,this.pendingRenderTaskId=null,this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof gi||!this.zoneIsDefined)}notify(n){if(!this.zonelessEnabled&&n===5)return;switch(n){case 3:case 2:case 0:case 4:case 5:case 1:{this.shouldRefreshViews=!0;break}case 8:case 7:case 6:case 9:default:}if(!this.shouldScheduleTick())return;let r=this.useMicrotaskScheduler?md:Xf;this.pendingRenderTaskId=this.taskService.add(),this.zoneIsDefined?Zone.root.run(()=>{this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}):this.cancelScheduledCallback=r(()=>{this.tick(this.shouldRefreshViews)})}shouldScheduleTick(){return!(this.disableScheduling||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&$.isInAngularZone())}tick(n){if(this.runningTick||this.appRef.destroyed)return;let r=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick(n)},void 0,this.schedulerTickApplyArgs)}catch(o){throw this.taskService.remove(r),o}finally{this.cleanup()}this.useMicrotaskScheduler=!0,md(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(r)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.shouldRefreshViews=!1,this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let n=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(n)}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function eC(){return typeof $localize<"u"&&$localize.locale||_i}var zc=new b("",{providedIn:"root",factory:()=>y(zc,N.Optional|N.SkipSelf)||eC()});var Yh=new b(""),Qh=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let o=P1(r?.ngZone,Kh({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{let i=r?.ignoreChangesOutsideZone,s=Jy(n.moduleType,this.injector,[...Yv({ngZoneFactory:()=>o,ignoreChangesOutsideZone:i}),{provide:Nn,useExisting:Jv}]),a=s.injector.get(st,null);return o.runOutsideAngular(()=>{let c=o.onError.subscribe({next:l=>{a.handleError(l)}});s.onDestroy(()=>{ei(this._modules,s),c.unsubscribe()})}),Wv(a,o,()=>{let c=s.injector.get(Zh);return c.runInitializers(),c.donePromise.then(()=>{let l=s.injector.get(zc,_i);return xv(l||_i),this._moduleDoBootstrap(s),s})})})}bootstrapModule(n,r=[]){let o=Xh({},r);return Xv(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){let r=n.injector.get(Ot);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(r);else throw new I(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new I(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Yh,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(E(_e))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),vr=null,Jh=new b("");function tC(e){if(vr&&!vr.get(Jh,!1))throw new I(400,!1);$v(),vr=e;let t=e.get(Qh);return oC(e),t}function Gc(e,t,n=[]){let r=`Platform: ${t}`,o=new b(r);return(i=[])=>{let s=ep();if(!s||s.injector.get(Jh,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):tC(nC(a,r))}return rC(o)}}function nC(e=[],t){return _e.create({name:t,providers:[{provide:Ri,useValue:"platform"},{provide:Yh,useValue:new Set([()=>vr=null])},...e]})}function rC(e){let t=ep();if(!t)throw new I(401,!1);return t}function ep(){return vr?.get(Qh)??null}function oC(e){e.get(Mc,null)?.forEach(n=>n())}var qn=(()=>{class e{static{this.__NG_ELEMENT_ID__=iC}}return e})();function iC(e){return sC(Ee(),q(),(e&16)===16)}function sC(e,t,n){if(Pi(e)&&!n){let r=Tt(e.index,t);return new Xt(r,r)}else if(e.type&175){let r=t[Ke];return new Xt(r,t)}return null}var nc=class{constructor(){}supports(t){return Bh(t)}create(t){return new rc(t)}},aC=(e,t)=>t,rc=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||aC}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<Pd(r,o,i)?n:r,a=Pd(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let l=a-o,u=c-o;if(l!=u){for(let m=0;m<l;m++){let p=m<i.length?i[m]:i[m]=0,v=p+m;u<=v&&v<l&&(i[m]=p+1)}let d=s.previousIndex;i[d]=u-l}}a!==c&&t(s,a,c)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Bh(t))throw new I(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,ev(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new oc(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Mi),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Mi),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},oc=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},ic=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Mi=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new ic,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function Pd(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}function kd(){return new Wc([new nc])}var Wc=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:kd})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||kd()),deps:[[e,new ac,new Ni]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new I(901,!1)}}return e})();var tp=Gc(null,"core",[]),np=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(E(Ot))}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({})}}return e})();var rp=new b("");function Zn(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Or(e,t){zn("NgSignals");let n=wu(e);return t?.equal&&(n[yt].equal=t.equal),n}function dt(e){let t=B(null);try{return e()}finally{B(t)}}function op(e){let t=Mt(e);if(!t)return null;let n=new Un(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var up=null;function tn(){return up}function dp(e){up??=e}var ns=class{};var ue=new b(""),Jc=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(lC),providedIn:"platform"})}}return e})(),fp=new b(""),lC=(()=>{class e extends Jc{constructor(){super(),this._doc=y(ue),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return tn().getBaseHref(this._doc)}onPopState(n){let r=tn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=tn().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function el(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function ip(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function ft(e){return e&&e[0]!=="?"?"?"+e:e}var ht=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(tl),providedIn:"root"})}}return e})(),hp=new b(""),tl=(()=>{class e extends ht{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??y(ue).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return el(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+ft(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+ft(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+ft(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(Jc),E(hp,8))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),pp=(()=>{class e extends ht{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=el(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+ft(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+ft(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(E(Jc),E(hp,8))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Kn=(()=>{class e{constructor(n){this._subject=new Q,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=fC(ip(sp(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+ft(r))}normalize(n){return e.stripTrailingSlash(dC(this._basePath,sp(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+ft(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=ft}static{this.joinWithSlash=el}static{this.stripTrailingSlash=ip}static{this.\u0275fac=function(r){return new(r||e)(E(ht))}}static{this.\u0275prov=w({token:e,factory:()=>uC(),providedIn:"root"})}}return e})();function uC(){return new Kn(E(ht))}function dC(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function sp(e){return e.replace(/\/index.html$/,"")}function fC(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}function rs(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var qc=/\s+/,ap=[],gp=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=ap,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(qc):ap}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(qc):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(qc).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static{this.\u0275fac=function(r){return new(r||e)(z(Yt),z($n))}}static{this.\u0275dir=Be({type:e,selectors:[["","ngClass",""]],inputs:{klass:[0,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var Zc=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},os=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Zc(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),cp(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);cp(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(z(Gn),z(Wi),z(Wc))}}static{this.\u0275dir=Be({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function cp(e,t){e.context.$implicit=t.item}var mp=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Xc,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){lp("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){lp("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(z(Gn),z(Wi))}}static{this.\u0275dir=Be({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Xc=class{constructor(){this.$implicit=null,this.ngIf=null}};function lp(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${pe(t)}'.`)}var yp=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({})}}return e})(),nl="browser",hC="server";function pC(e){return e===nl}function is(e){return e===hC}var vp=(()=>{class e{static{this.\u0275prov=w({token:e,providedIn:"root",factory:()=>pC(y(Qe))?new Kc(y(ue),window):new Yc})}}return e})(),Kc=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=gC(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function gC(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Yc=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},Xn=class{};var Pr=class{},as=class{},pt=class e{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?typeof t=="string"?this.lazyInit=()=>{this.headers=new Map,t.split(`
`).forEach(n=>{let r=n.indexOf(":");if(r>0){let o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();let n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){let n=new e;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){let n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(t.name,n);let o=(t.op==="a"?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":let i=t.value;if(!i)this.headers.delete(n),this.normalizedNames.delete(n);else{let s=this.headers.get(n);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}break}}setHeaderEntries(t,n){let r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}};var ol=class{encodeKey(t){return Cp(t)}encodeValue(t){return Cp(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}};function mC(e,t){let n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],c=n.get(s)||[];c.push(a),n.set(s,c)}),n}var yC=/%(\d[a-f0-9])/gi,vC={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Cp(e){return encodeURIComponent(e).replace(yC,(t,n)=>vC[n]??t)}function ss(e){return`${e}`}var Ut=class e{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new ol,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=mC(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{let r=t.fromObject[n],o=Array.isArray(r)?r.map(ss):[ss(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();let n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){let n=[];return Object.keys(t).forEach(r=>{let o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{let n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>t!=="").join("&")}clone(t){let n=new e({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":let n=(t.op==="a"?this.map.get(t.param):void 0)||[];n.push(ss(t.value)),this.map.set(t.param,n);break;case"d":if(t.value!==void 0){let r=this.map.get(t.param)||[],o=r.indexOf(ss(t.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}else{this.map.delete(t.param);break}}}),this.cloneFrom=this.updates=null)}};var il=class{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}};function CC(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Dp(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function wp(e){return typeof Blob<"u"&&e instanceof Blob}function Ep(e){return typeof FormData<"u"&&e instanceof FormData}function DC(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var Ur=class e{constructor(t,n,r,o){this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase();let i;if(CC(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),this.transferCache=i.transferCache),this.headers??=new pt,this.context??=new il,!this.params)this.params=new Ut,this.urlWithParams=n;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=n;else{let a=n.indexOf("?"),c=a===-1?"?":a<n.length-1?"&":"";this.urlWithParams=n+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Dp(this.body)||wp(this.body)||Ep(this.body)||DC(this.body)?this.body:this.body instanceof Ut?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Ep(this.body)?null:wp(this.body)?this.body.type||null:Dp(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof Ut?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(t={}){let n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=t.transferCache??this.transferCache,s=t.body!==void 0?t.body:this.body,a=t.withCredentials??this.withCredentials,c=t.reportProgress??this.reportProgress,l=t.headers||this.headers,u=t.params||this.params,d=t.context??this.context;return t.setHeaders!==void 0&&(l=Object.keys(t.setHeaders).reduce((m,p)=>m.set(p,t.setHeaders[p]),l)),t.setParams&&(u=Object.keys(t.setParams).reduce((m,p)=>m.set(p,t.setParams[p]),u)),new e(n,r,s,{params:u,headers:l,context:d,reportProgress:c,responseType:o,withCredentials:a,transferCache:i})}},Pt=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Pt||{}),kr=class{constructor(t,n=200,r="OK"){this.headers=t.headers||new pt,this.status=t.status!==void 0?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}},cs=class e extends kr{constructor(t={}){super(t),this.type=Pt.ResponseHeader}clone(t={}){return new e({headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Lr=class e extends kr{constructor(t={}){super(t),this.type=Pt.Response,this.body=t.body!==void 0?t.body:null}clone(t={}){return new e({body:t.body!==void 0?t.body:this.body,headers:t.headers||this.headers,status:t.status!==void 0?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}},Rt=class extends kr{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${t.url||"(unknown url)"}`:this.message=`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}},Sp=200,wC=204;function rl(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache}}var nn=(()=>{class e{constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof Ur)i=n;else{let c;o.headers instanceof pt?c=o.headers:c=new pt(o.headers);let l;o.params&&(o.params instanceof Ut?l=o.params:l=new Ut({fromObject:o.params})),i=new Ur(n,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:l,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache})}let s=M(i).pipe(ot(c=>this.handler.handle(c)));if(n instanceof Ur||o.observe==="events")return s;let a=s.pipe(Ce(c=>c instanceof Lr));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(x(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return c.body}));case"blob":return a.pipe(x(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new Error("Response is not a Blob.");return c.body}));case"text":return a.pipe(x(c=>{if(c.body!==null&&typeof c.body!="string")throw new Error("Response is not a string.");return c.body}));case"json":default:return a.pipe(x(c=>c.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:new Ut().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,rl(o,r))}post(n,r,o={}){return this.request("POST",n,rl(o,r))}put(n,r,o={}){return this.request("PUT",n,rl(o,r))}static{this.\u0275fac=function(r){return new(r||e)(E(Pr))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),EC=/^\)\]\}',?\n/,bC="X-Request-URL";function bp(e){if(e.url)return e.url;let t=bC.toLocaleLowerCase();return e.headers.get(t)}var IC=(()=>{class e{constructor(){this.fetchImpl=y(sl,{optional:!0})?.fetch??fetch.bind(globalThis),this.ngZone=y($)}handle(n){return new L(r=>{let o=new AbortController;return this.doRequest(n,o.signal,r).then(al,i=>r.error(new Rt({error:i}))),()=>o.abort()})}doRequest(n,r,o){return mo(this,null,function*(){let i=this.createRequestInit(n),s;try{let p=this.ngZone.runOutsideAngular(()=>this.fetchImpl(n.urlWithParams,C({signal:r},i)));_C(p),o.next({type:Pt.Sent}),s=yield p}catch(p){o.error(new Rt({error:p,status:p.status??0,statusText:p.statusText,url:n.urlWithParams,headers:p.headers}));return}let a=new pt(s.headers),c=s.statusText,l=bp(s)??n.urlWithParams,u=s.status,d=null;if(n.reportProgress&&o.next(new cs({headers:a,status:u,statusText:c,url:l})),s.body){let p=s.headers.get("content-length"),v=[],D=s.body.getReader(),_=0,T,de,U=typeof Zone<"u"&&Zone.current;yield this.ngZone.runOutsideAngular(()=>mo(this,null,function*(){for(;;){let{done:ye,value:oe}=yield D.read();if(ye)break;if(v.push(oe),_+=oe.length,n.reportProgress){de=n.responseType==="text"?(de??"")+(T??=new TextDecoder).decode(oe,{stream:!0}):void 0;let nt=()=>o.next({type:Pt.DownloadProgress,total:p?+p:void 0,loaded:_,partialText:de});U?U.run(nt):nt()}}}));let fe=this.concatChunks(v,_);try{let ye=s.headers.get("Content-Type")??"";d=this.parseBody(n,fe,ye)}catch(ye){o.error(new Rt({error:ye,headers:new pt(s.headers),status:s.status,statusText:s.statusText,url:bp(s)??n.urlWithParams}));return}}u===0&&(u=d?Sp:0),u>=200&&u<300?(o.next(new Lr({body:d,headers:a,status:u,statusText:c,url:l})),o.complete()):o.error(new Rt({error:d,headers:a,status:u,statusText:c,url:l}))})}parseBody(n,r,o){switch(n.responseType){case"json":let i=new TextDecoder().decode(r).replace(EC,"");return i===""?null:JSON.parse(i);case"text":return new TextDecoder().decode(r);case"blob":return new Blob([r],{type:o});case"arraybuffer":return r.buffer}}createRequestInit(n){let r={},o=n.withCredentials?"include":void 0;if(n.headers.forEach((i,s)=>r[i]=s.join(",")),n.headers.has("Accept")||(r.Accept="application/json, text/plain, */*"),!n.headers.has("Content-Type")){let i=n.detectContentTypeHeader();i!==null&&(r["Content-Type"]=i)}return{body:n.serializeBody(),method:n.method,headers:r,credentials:o}}concatChunks(n,r){let o=new Uint8Array(r),i=0;for(let s of n)o.set(s,i),i+=s.length;return o}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),sl=class{};function al(){}function _C(e){e.then(al,al)}function Ap(e,t){return t(e)}function MC(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}function SC(e,t,n){return(r,o)=>Me(n,()=>t(r,i=>e(i,o)))}var AC=new b(""),cl=new b(""),xC=new b(""),xp=new b("",{providedIn:"root",factory:()=>!0});function TC(){let e=null;return(t,n)=>{e===null&&(e=(y(AC,{optional:!0})??[]).reduceRight(MC,Ap));let r=y(ut);if(y(xp)){let i=r.add();return e(t,n).pipe(wt(()=>r.remove(i)))}else return e(t,n)}}var Ip=(()=>{class e extends Pr{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=y(ut),this.contributeToStability=y(xp)}handle(n){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(cl),...this.injector.get(xC,[])]));this.chain=r.reduceRight((o,i)=>SC(o,i,this.injector),Ap)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(wt(()=>this.pendingTasks.remove(r)))}else return this.chain(n,r=>this.backend.handle(r))}static{this.\u0275fac=function(r){return new(r||e)(E(as),E(ge))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();var FC=/^\)\]\}',?\n/;function NC(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}var _p=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if(n.method==="JSONP")throw new I(-2800,!1);let r=this.xhrFactory;return(r.\u0275loadImpl?W(r.\u0275loadImpl()):M(null)).pipe(De(()=>new L(i=>{let s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((D,_)=>s.setRequestHeader(D,_.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){let D=n.detectContentTypeHeader();D!==null&&s.setRequestHeader("Content-Type",D)}if(n.responseType){let D=n.responseType.toLowerCase();s.responseType=D!=="json"?D:"text"}let a=n.serializeBody(),c=null,l=()=>{if(c!==null)return c;let D=s.statusText||"OK",_=new pt(s.getAllResponseHeaders()),T=NC(s)||n.url;return c=new cs({headers:_,status:s.status,statusText:D,url:T}),c},u=()=>{let{headers:D,status:_,statusText:T,url:de}=l(),U=null;_!==wC&&(U=typeof s.response>"u"?s.responseText:s.response),_===0&&(_=U?Sp:0);let fe=_>=200&&_<300;if(n.responseType==="json"&&typeof U=="string"){let ye=U;U=U.replace(FC,"");try{U=U!==""?JSON.parse(U):null}catch(oe){U=ye,fe&&(fe=!1,U={error:oe,text:U})}}fe?(i.next(new Lr({body:U,headers:D,status:_,statusText:T,url:de||void 0})),i.complete()):i.error(new Rt({error:U,headers:D,status:_,statusText:T,url:de||void 0}))},d=D=>{let{url:_}=l(),T=new Rt({error:D,status:s.status||0,statusText:s.statusText||"Unknown Error",url:_||void 0});i.error(T)},m=!1,p=D=>{m||(i.next(l()),m=!0);let _={type:Pt.DownloadProgress,loaded:D.loaded};D.lengthComputable&&(_.total=D.total),n.responseType==="text"&&s.responseText&&(_.partialText=s.responseText),i.next(_)},v=D=>{let _={type:Pt.UploadProgress,loaded:D.loaded};D.lengthComputable&&(_.total=D.total),i.next(_)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",p),a!==null&&s.upload&&s.upload.addEventListener("progress",v)),s.send(a),i.next({type:Pt.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",p),a!==null&&s.upload&&s.upload.removeEventListener("progress",v)),s.readyState!==s.DONE&&s.abort()}})))}static{this.\u0275fac=function(r){return new(r||e)(E(Xn))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Tp=new b(""),OC="XSRF-TOKEN",RC=new b("",{providedIn:"root",factory:()=>OC}),UC="X-XSRF-TOKEN",PC=new b("",{providedIn:"root",factory:()=>UC}),ls=class{},kC=(()=>{class e{constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=rs(n,this.cookieName),this.lastCookieString=n),this.lastToken}static{this.\u0275fac=function(r){return new(r||e)(E(ue),E(Qe),E(RC))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function LC(e,t){let n=e.url.toLowerCase();if(!y(Tp)||e.method==="GET"||e.method==="HEAD"||n.startsWith("http://")||n.startsWith("https://"))return t(e);let r=y(ls).getToken(),o=y(PC);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Fp=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Fp||{});function VC(e,t){return{\u0275kind:e,\u0275providers:t}}function jC(...e){let t=[nn,_p,Ip,{provide:Pr,useExisting:Ip},{provide:as,useFactory:()=>y(IC,{optional:!0})??y(_p)},{provide:cl,useValue:LC,multi:!0},{provide:Tp,useValue:!0},{provide:ls,useClass:kC}];for(let n of e)t.push(...n.\u0275providers);return Oi(t)}var Mp=new b("");function BC(){return VC(Fp.LegacyInterceptors,[{provide:Mp,useFactory:TC},{provide:cl,useExisting:Mp,multi:!0}])}var Np=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({providers:[jC(BC())]})}}return e})();var fl=class extends ns{constructor(){super(...arguments),this.supportsDOMEvents=!0}},hl=class e extends fl{static makeCurrent(){dp(new e)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.remove()}createElement(t,n){return n=n||this.getDefaultDocument(),n.createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return n==="window"?window:n==="document"?t:n==="body"?t.body:null}getBaseHref(t){let n=HC();return n==null?null:$C(n)}resetBaseElement(){Vr=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return rs(document.cookie,t)}},Vr=null;function HC(){return Vr=Vr||document.querySelector("base"),Vr?Vr.getAttribute("href"):null}function $C(e){return new URL(e,document.baseURI).pathname}var pl=class{addToWindow(t){Ve.getAngularTestability=(r,o=!0)=>{let i=t.findTestabilityInTree(r,o);if(i==null)throw new I(5103,!1);return i},Ve.getAllAngularTestabilities=()=>t.getAllTestabilities(),Ve.getAllAngularRootElements=()=>t.getAllRootElements();let n=r=>{let o=Ve.getAllAngularTestabilities(),i=o.length,s=function(){i--,i==0&&r()};o.forEach(a=>{a.whenStable(s)})};Ve.frameworkStabilizers||(Ve.frameworkStabilizers=[]),Ve.frameworkStabilizers.push(n)}findTestabilityInTree(t,n,r){if(n==null)return null;let o=t.getTestability(n);return o??(r?tn().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},zC=(()=>{class e{build(){return new XMLHttpRequest}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),us=new b(""),Pp=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o){return this._findPluginFor(r).addEventListener(n,r,o)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new I(5101,!1);return this._eventNameToPlugin.set(n,r),r}static{this.\u0275fac=function(r){return new(r||e)(E(us),E($))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),jr=class{constructor(t){this._doc=t}},ul="ng-app-id",kp=(()=>{class e{constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=is(i),this.resetHostNodes()}addStyles(n){for(let r of n)this.changeUsageCount(r,1)===1&&this.onStyleAdded(r)}removeStyles(n){for(let r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){let n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(let r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(let r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(let r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){let r=this.styleRef;r.get(n)?.elements?.forEach(o=>o.remove()),r.delete(n)}collectServerRenderedStyles(){let n=this.doc.head?.querySelectorAll(`style[${ul}="${this.appId}"]`);if(n?.length){let r=new Map;return n.forEach(o=>{o.textContent!=null&&r.set(o.textContent,o)}),r}return null}changeUsageCount(n,r){let o=this.styleRef;if(o.has(n)){let i=o.get(n);return i.usage+=r,i.usage}return o.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){let o=this.styleNodesInDOM,i=o?.get(r);if(i?.parentNode===n)return o.delete(r),i.removeAttribute(ul),i;{let s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(ul,this.appId),n.appendChild(s),s}}addStyleToHost(n,r){let o=this.getStyleElement(n,r),i=this.styleRef,s=i.get(r)?.elements;s?s.push(o):i.set(r,{elements:[o],usage:1})}resetHostNodes(){let n=this.hostNodes;n.clear(),n.add(this.doc.head)}static{this.\u0275fac=function(r){return new(r||e)(E(ue),E(ji),E(Sc,8),E(Qe))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),dl={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},ml=/%COMP%/g,Lp="%COMP%",GC=`_nghost-${Lp}`,WC=`_ngcontent-${Lp}`,qC=!0,ZC=new b("",{providedIn:"root",factory:()=>qC});function XC(e){return WC.replace(ml,e)}function KC(e){return GC.replace(ml,e)}function Vp(e,t){return t.map(n=>n.replace(ml,e))}var Op=(()=>{class e{constructor(n,r,o,i,s,a,c,l=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=l,this.rendererByCompId=new Map,this.platformIsServer=is(a),this.defaultRenderer=new Br(n,s,c,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Ze.ShadowDom&&(r=j(C({},r),{encapsulation:Ze.Emulated}));let o=this.getOrCreateRenderer(n,r);return o instanceof ds?o.applyToHost(n):o instanceof Hr&&o.applyStyles(),o}getOrCreateRenderer(n,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,l=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case Ze.Emulated:i=new ds(c,l,r,this.appId,u,s,a,d);break;case Ze.ShadowDom:return new gl(c,l,n,r,s,a,this.nonce,d);default:i=new Hr(c,l,r,u,s,a,d);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(r){return new(r||e)(E(Pp),E(kp),E(ji),E(ZC),E(ue),E(Qe),E($),E(Sc))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Br=class{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(dl[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(Rp(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(Rp(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){n.remove()}selectRootElement(t,n){let r=typeof t=="string"?this.doc.querySelector(t):t;if(!r)throw new I(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;let i=dl[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){let o=dl[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(at.DashCase|at.Important)?t.style.setProperty(n,r,o&at.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&at.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t!=null&&(t[n]=r)}setValue(t,n){t.nodeValue=n}listen(t,n,r){if(typeof t=="string"&&(t=tn().getGlobalEventTarget(this.doc,t),!t))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if(n==="__ngUnwrap__")return t;(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))===!1&&n.preventDefault()}}};function Rp(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var gl=class extends Br{constructor(t,n,r,o,i,s,a,c){super(t,i,s,c),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=Vp(o.id,o.styles);for(let u of l){let d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(null,n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Hr=class extends Br{constructor(t,n,r,o,i,s,a,c){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=c?Vp(c,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},ds=class extends Hr{constructor(t,n,r,o,i,s,a,c){let l=o+"-"+r.id;super(t,n,r,i,s,a,c,l),this.contentAttr=XC(l),this.hostAttr=KC(l)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){let r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}},YC=(()=>{class e extends jr{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o){return n.addEventListener(r,o,!1),()=>this.removeEventListener(n,r,o)}removeEventListener(n,r,o){return n.removeEventListener(r,o)}static{this.\u0275fac=function(r){return new(r||e)(E(ue))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),QC=(()=>{class e extends jr{constructor(n){super(n),this.delegate=y(rp,{optional:!0})}supports(n){return this.delegate?this.delegate.supports(n):!1}addEventListener(n,r,o){return this.delegate.addEventListener(n,r,o)}removeEventListener(n,r,o){return this.delegate.removeEventListener(n,r,o)}static{this.\u0275fac=function(r){return new(r||e)(E(ue))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})(),Up=["alt","control","meta","shift"],JC={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},eD={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},tD=(()=>{class e extends jr{constructor(n){super(n)}supports(n){return e.parseEventName(n)!=null}addEventListener(n,r,o){let i=e.parseEventName(r),s=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>tn().onAndCancel(n,i.domEventName,s))}static parseEventName(n){let r=n.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),Up.forEach(l=>{let u=r.indexOf(l);u>-1&&(r.splice(u,1),s+=l+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(n,r){let o=JC[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),Up.forEach(s=>{if(s!==o){let a=eD[s];a(n)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return n==="esc"?"escape":n}static{this.\u0275fac=function(r){return new(r||e)(E(ue))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function nD(){hl.makeCurrent()}function rD(){return new st}function oD(){return nh(document),document}var iD=[{provide:Qe,useValue:nl},{provide:Mc,useValue:nD,multi:!0},{provide:ue,useFactory:oD,deps:[]}],jp=Gc(tp,"browser",iD),sD=new b(""),aD=[{provide:Nr,useClass:pl,deps:[]},{provide:Hc,useClass:Yi,deps:[$,Qi,Nr]},{provide:Yi,useClass:Yi,deps:[$,Qi,Nr]}],cD=[{provide:Ri,useValue:"root"},{provide:st,useFactory:rD,deps:[]},{provide:us,useClass:YC,multi:!0,deps:[ue,$,Qe]},{provide:us,useClass:tD,multi:!0,deps:[ue]},{provide:us,useClass:QC,multi:!0},Op,kp,Pp,{provide:Rn,useExisting:Op},{provide:Xn,useClass:zC,deps:[]},[]],Bp=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:ji,useValue:n.appId}]}}static{this.\u0275fac=function(r){return new(r||e)(E(sD,12))}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({providers:[...cD,...aD],imports:[yp,np]})}}return e})();var Hp=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static{this.\u0275fac=function(r){return new(r||e)(E(ue))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var lD=new b(""),uD=new b("");function Zp(e){return e!=null}function Xp(e){return en(e)?W(e):e}function Kp(e){let t={};return e.forEach(n=>{t=n!=null?C(C({},t),n):t}),Object.keys(t).length===0?null:t}function Yp(e,t){return t.map(n=>n(e))}function dD(e){return!e.validate}function Qp(e){return e.map(t=>dD(t)?t:n=>t.validate(n))}function fD(e){if(!e)return null;let t=e.filter(Zp);return t.length==0?null:function(n){return Kp(Yp(n,t))}}function wl(e){return e!=null?fD(Qp(e)):null}function hD(e){if(!e)return null;let t=e.filter(Zp);return t.length==0?null:function(n){let r=Yp(n,t).map(Xp);return ta(r).pipe(x(Kp))}}function El(e){return e!=null?hD(Qp(e)):null}function zp(e,t){return e===null?[t]:Array.isArray(e)?[...e,t]:[e,t]}function pD(e){return e._rawValidators}function gD(e){return e._rawAsyncValidators}function yl(e){return e?Array.isArray(e)?e:[e]:[]}function hs(e,t){return Array.isArray(e)?e.includes(t):e===t}function Gp(e,t){let n=yl(t);return yl(e).forEach(o=>{hs(n,o)||n.push(o)}),n}function Wp(e,t){return yl(t).filter(n=>!hs(e,n))}var vl=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=wl(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=El(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return this.control?this.control.hasError(t,n):!1}getError(t,n){return this.control?this.control.getError(t,n):null}},Zr=class extends vl{get formDirective(){return null}get path(){return null}};var Cl=class{constructor(t){this._cd=t}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},mD={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},t6=j(C({},mD),{"[class.ng-submitted]":"isSubmitted"});var Jp=(()=>{class e extends Cl{constructor(n){super(n)}static{this.\u0275fac=function(r){return new(r||e)(z(Zr,10))}}static{this.\u0275dir=Be({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Bc("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},features:[qi]})}}return e})();var $r="VALID",fs="INVALID",Yn="PENDING",zr="DISABLED",Jn=class{},ps=class extends Jn{constructor(t,n){super(),this.value=t,this.source=n}},Wr=class extends Jn{constructor(t,n){super(),this.pristine=t,this.source=n}},qr=class extends Jn{constructor(t,n){super(),this.touched=t,this.source=n}},Qn=class extends Jn{constructor(t,n){super(),this.status=t,this.source=n}};function yD(e){return(bl(e)?e.validators:e)||null}function vD(e){return Array.isArray(e)?wl(e):e||null}function CD(e,t){return(bl(t)?t.asyncValidators:e)||null}function DD(e){return Array.isArray(e)?El(e):e||null}function bl(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function wD(e,t,n){let r=e.controls;if(!(t?Object.keys(r):r).length)throw new I(1e3,"");if(!r[n])throw new I(1001,"")}function ED(e,t,n){e._forEachChild((r,o)=>{if(n[o]===void 0)throw new I(1002,"")})}var Dl=class{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=null,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this._status=Or(()=>this.statusReactive()),this.statusReactive=Tr(void 0),this._pristine=Or(()=>this.pristineReactive()),this.pristineReactive=Tr(!0),this._touched=Or(()=>this.touchedReactive()),this.touchedReactive=Tr(!1),this._events=new J,this.events=this._events.asObservable(),this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get status(){return dt(this.statusReactive)}set status(t){dt(()=>this.statusReactive.set(t))}get valid(){return this.status===$r}get invalid(){return this.status===fs}get pending(){return this.status==Yn}get disabled(){return this.status===zr}get enabled(){return this.status!==zr}get pristine(){return dt(this.pristineReactive)}set pristine(t){dt(()=>this.pristineReactive.set(t))}get dirty(){return!this.pristine}get touched(){return dt(this.touchedReactive)}set touched(t){dt(()=>this.touchedReactive.set(t))}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(Gp(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(Gp(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(Wp(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(Wp(t,this._rawAsyncValidators))}hasValidator(t){return hs(this._rawValidators,t)}hasAsyncValidator(t){return hs(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){let n=this.touched===!1;this.touched=!0;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsTouched(j(C({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new qr(!0,r))}markAllAsTouched(t={}){this.markAsTouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:this}),this._forEachChild(n=>n.markAllAsTouched(t))}markAsUntouched(t={}){let n=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:t.emitEvent,sourceControl:r})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,r),n&&t.emitEvent!==!1&&this._events.next(new qr(!1,r))}markAsDirty(t={}){let n=this.pristine===!0;this.pristine=!1;let r=t.sourceControl??this;this._parent&&!t.onlySelf&&this._parent.markAsDirty(j(C({},t),{sourceControl:r})),n&&t.emitEvent!==!1&&this._events.next(new Wr(!1,r))}markAsPristine(t={}){let n=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=t.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:t.emitEvent})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t,r),n&&t.emitEvent!==!1&&this._events.next(new Wr(!0,r))}markAsPending(t={}){this.status=Yn;let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new Qn(this.status,n)),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.markAsPending(j(C({},t),{sourceControl:n}))}disable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=zr,this.errors=null,this._forEachChild(o=>{o.disable(j(C({},t),{onlySelf:!0}))}),this._updateValue();let r=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ps(this.value,r)),this._events.next(new Qn(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(j(C({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(t={}){let n=this._parentMarkedDirty(t.onlySelf);this.status=$r,this._forEachChild(r=>{r.enable(j(C({},t),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors(j(C({},t),{skipPristineCheck:n}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t,n){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine({},n),this._parent._updateTouched({},n))}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===$r||this.status===Yn)&&this._runAsyncValidator(r,t.emitEvent)}let n=t.sourceControl??this;t.emitEvent!==!1&&(this._events.next(new ps(this.value,n)),this._events.next(new Qn(this.status,n)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(j(C({},t),{sourceControl:n}))}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?zr:$r}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t,n){if(this.asyncValidator){this.status=Yn,this._hasOwnPendingAsyncValidator={emitEvent:n!==!1};let r=Xp(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:n,shouldHaveEmitted:t})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let t=this._hasOwnPendingAsyncValidator?.emitEvent??!1;return this._hasOwnPendingAsyncValidator=null,t}return!1}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(n.emitEvent!==!1,this,n.shouldHaveEmitted)}get(t){let n=t;return n==null||(Array.isArray(n)||(n=n.split(".")),n.length===0)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){let r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t,n,r){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),(t||r)&&this._events.next(new Qn(this.status,n)),this._parent&&this._parent._updateControlsErrors(t,n,r)}_initObservables(){this.valueChanges=new Q,this.statusChanges=new Q}_calculateStatus(){return this._allControlsDisabled()?zr:this.errors?fs:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Yn)?Yn:this._anyControlsHaveStatus(fs)?fs:$r}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t,n){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!t.onlySelf&&this._parent._updatePristine(t,n),o&&this._events.next(new Wr(this.pristine,n))}_updateTouched(t={},n){this.touched=this._anyControlsTouched(),this._events.next(new qr(this.touched,n)),this._parent&&!t.onlySelf&&this._parent._updateTouched(t,n)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){bl(t)&&t.updateOn!=null&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){let n=this._parent&&this._parent.dirty;return!t&&!!n&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=vD(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=DD(this._rawAsyncValidators)}},gs=class extends Dl{constructor(t,n,r){super(yD(n),CD(r,n)),this.controls=t,this._initObservables(),this._setUpdateStrategy(n),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(t,n){return this.controls[t]?this.controls[t]:(this.controls[t]=n,n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange),n)}addControl(t,n,r={}){this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(t,n={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}setControl(t,n,r={}){this.controls[t]&&this.controls[t]._registerOnCollectionChange(()=>{}),delete this.controls[t],n&&this.registerControl(t,n),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(t){return this.controls.hasOwnProperty(t)&&this.controls[t].enabled}setValue(t,n={}){ED(this,!0,t),Object.keys(t).forEach(r=>{wD(this,!0,r),this.controls[r].setValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n)}patchValue(t,n={}){t!=null&&(Object.keys(t).forEach(r=>{let o=this.controls[r];o&&o.patchValue(t[r],{onlySelf:!0,emitEvent:n.emitEvent})}),this.updateValueAndValidity(n))}reset(t={},n={}){this._forEachChild((r,o)=>{r.reset(t?t[o]:null,{onlySelf:!0,emitEvent:n.emitEvent})}),this._updatePristine(n,this),this._updateTouched(n,this),this.updateValueAndValidity(n)}getRawValue(){return this._reduceChildren({},(t,n,r)=>(t[r]=n.getRawValue(),t))}_syncPendingControls(){let t=this._reduceChildren(!1,(n,r)=>r._syncPendingControls()?!0:n);return t&&this.updateValueAndValidity({onlySelf:!0}),t}_forEachChild(t){Object.keys(this.controls).forEach(n=>{let r=this.controls[n];r&&t(r,n)})}_setUpControls(){this._forEachChild(t=>{t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(t){for(let[n,r]of Object.entries(this.controls))if(this.contains(n)&&t(r))return!0;return!1}_reduceValue(){let t={};return this._reduceChildren(t,(n,r,o)=>((r.enabled||this.disabled)&&(n[o]=r.value),n))}_reduceChildren(t,n){let r=t;return this._forEachChild((o,i)=>{r=n(r,o,i)}),r}_allControlsDisabled(){for(let t of Object.keys(this.controls))if(this.controls[t].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(t){return this.controls.hasOwnProperty(t)?this.controls[t]:null}};var Il=new b("CallSetDisabledState",{providedIn:"root",factory:()=>ms}),ms="always";function bD(e,t,n=ms){eg(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||n==="always")&&t.valueAccessor.setDisabledState?.(e.disabled),_D(e,t),SD(e,t),MD(e,t),ID(e,t)}function qp(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function ID(e,t){if(t.valueAccessor.setDisabledState){let n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}function eg(e,t){let n=pD(e);t.validator!==null?e.setValidators(zp(n,t.validator)):typeof n=="function"&&e.setValidators([n]);let r=gD(e);t.asyncValidator!==null?e.setAsyncValidators(zp(r,t.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();qp(t._rawValidators,o),qp(t._rawAsyncValidators,o)}function _D(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&tg(e,t)})}function MD(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&tg(e,t),e.updateOn!=="submit"&&e.markAsTouched()})}function tg(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function SD(e,t){let n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}function AD(e,t){e==null,eg(e,t)}function xD(e,t){e._syncPendingControls(),t.forEach(n=>{let r=n.control;r.updateOn==="submit"&&r._pendingChange&&(n.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}var TD={provide:Zr,useExisting:Ai(()=>_l)},Gr=Promise.resolve(),_l=(()=>{class e extends Zr{get submitted(){return dt(this.submittedReactive)}constructor(n,r,o){super(),this.callSetDisabledState=o,this._submitted=Or(()=>this.submittedReactive()),this.submittedReactive=Tr(!1),this._directives=new Set,this.ngSubmit=new Q,this.form=new gs({},wl(n),El(r))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(n){Gr.then(()=>{let r=this._findContainer(n.path);n.control=r.registerControl(n.name,n.control),bD(n.control,n,this.callSetDisabledState),n.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(n)})}getControl(n){return this.form.get(n.path)}removeControl(n){Gr.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name),this._directives.delete(n)})}addFormGroup(n){Gr.then(()=>{let r=this._findContainer(n.path),o=new gs({});AD(o,n),r.registerControl(n.name,o),o.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(n){Gr.then(()=>{let r=this._findContainer(n.path);r&&r.removeControl(n.name)})}getFormGroup(n){return this.form.get(n.path)}updateModel(n,r){Gr.then(()=>{this.form.get(n.path).setValue(r)})}setValue(n){this.control.setValue(n)}onSubmit(n){return this.submittedReactive.set(!0),xD(this.form,this._directives),this.ngSubmit.emit(n),n?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(n=void 0){this.form.reset(n),this.submittedReactive.set(!1)}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(n){return n.pop(),n.length?this.form.get(n):this.form}static{this.\u0275fac=function(r){return new(r||e)(z(lD,10),z(uD,10),z(Il,8))}}static{this.\u0275dir=Be({type:e,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(r,o){r&1&&Je("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{options:[0,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[zh([TD]),qi]})}}return e})();var ng=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=Be({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return e})();var FD=new b("");var rg=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({})}}return e})();var og=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Il,useValue:n.callSetDisabledState??ms}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({imports:[rg]})}}return e})(),ig=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:FD,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:Il,useValue:n.callSetDisabledState??ms}]}}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({imports:[rg]})}}return e})();var F="primary",co=Symbol("RouteTitle"),Tl=class{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){let n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}};function ir(e){return new Tl(e)}function OD(e,t,n){let r=n.path.split("/");if(r.length>e.length||n.pathMatch==="full"&&(t.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function RD(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!et(e[n],t[n]))return!1;return!0}function et(e,t){let n=e?Fl(e):void 0,r=t?Fl(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!mg(e[o],t[o]))return!1;return!0}function Fl(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function mg(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;let n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}else return e===t}function yg(e){return e.length>0?e[e.length-1]:null}function Vt(e){return ea(e)?e:en(e)?W(Promise.resolve(e)):M(e)}var UD={exact:Cg,subset:Dg},vg={exact:PD,subset:kD,ignored:()=>!0};function sg(e,t,n){return UD[n.paths](e.root,t.root,n.matrixParams)&&vg[n.queryParams](e.queryParams,t.queryParams)&&!(n.fragment==="exact"&&e.fragment!==t.fragment)}function PD(e,t){return et(e,t)}function Cg(e,t,n){if(!on(e.segments,t.segments)||!Cs(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(let r in t.children)if(!e.children[r]||!Cg(e.children[r],t.children[r],n))return!1;return!0}function kD(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>mg(e[n],t[n]))}function Dg(e,t,n){return wg(e,t,t.segments,n)}function wg(e,t,n,r){if(e.segments.length>n.length){let o=e.segments.slice(0,n.length);return!(!on(o,n)||t.hasChildren()||!Cs(o,n,r))}else if(e.segments.length===n.length){if(!on(e.segments,n)||!Cs(e.segments,n,r))return!1;for(let o in t.children)if(!e.children[o]||!Dg(e.children[o],t.children[o],r))return!1;return!0}else{let o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!on(e.segments,o)||!Cs(e.segments,o,r)||!e.children[F]?!1:wg(e.children[F],t,i,r)}}function Cs(e,t,n){return t.every((r,o)=>vg[n](e[o].parameters,r.parameters))}var mt=class{constructor(t=new H([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap??=ir(this.queryParams),this._queryParamMap}toString(){return jD.serialize(this)}},H=class{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Ds(this)}},rn=class{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap??=ir(this.parameters),this._parameterMap}toString(){return bg(this)}};function LD(e,t){return on(e,t)&&e.every((n,r)=>et(n.parameters,t[r].parameters))}function on(e,t){return e.length!==t.length?!1:e.every((n,r)=>n.path===t[r].path)}function VD(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===F&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==F&&(n=n.concat(t(o,r)))}),n}var lo=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>new sr,providedIn:"root"})}}return e})(),sr=class{parse(t){let n=new Ol(t);return new mt(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){let n=`/${Xr(t.root,!0)}`,r=$D(t.queryParams),o=typeof t.fragment=="string"?`#${BD(t.fragment)}`:"";return`${n}${r}${o}`}},jD=new sr;function Ds(e){return e.segments.map(t=>bg(t)).join("/")}function Xr(e,t){if(!e.hasChildren())return Ds(e);if(t){let n=e.children[F]?Xr(e.children[F],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==F&&r.push(`${o}:${Xr(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}else{let n=VD(e,(r,o)=>o===F?[Xr(e.children[F],!1)]:[`${o}:${Xr(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[F]!=null?`${Ds(e)}/${n[0]}`:`${Ds(e)}/(${n.join("//")})`}}function Eg(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ys(e){return Eg(e).replace(/%3B/gi,";")}function BD(e){return encodeURI(e)}function Nl(e){return Eg(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function ws(e){return decodeURIComponent(e)}function ag(e){return ws(e.replace(/\+/g,"%20"))}function bg(e){return`${Nl(e.path)}${HD(e.parameters)}`}function HD(e){return Object.entries(e).map(([t,n])=>`;${Nl(t)}=${Nl(n)}`).join("")}function $D(e){let t=Object.entries(e).map(([n,r])=>Array.isArray(r)?r.map(o=>`${ys(n)}=${ys(o)}`).join("&"):`${ys(n)}=${ys(r)}`).filter(n=>n);return t.length?`?${t.join("&")}`:""}var zD=/^[^\/()?;#]+/;function Ml(e){let t=e.match(zD);return t?t[0]:""}var GD=/^[^\/()?;=#]+/;function WD(e){let t=e.match(GD);return t?t[0]:""}var qD=/^[^=?&#]+/;function ZD(e){let t=e.match(qD);return t?t[0]:""}var XD=/^[^&#]+/;function KD(e){let t=e.match(XD);return t?t[0]:""}var Ol=class{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new H([],{}):new H([],this.parseChildren())}parseQueryParams(){let t={};if(this.consumeOptional("?"))do this.parseQueryParam(t);while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[F]=new H(t,n)),r}parseSegment(){let t=Ml(this.remaining);if(t===""&&this.peekStartsWith(";"))throw new I(4009,!1);return this.capture(t),new rn(ws(t),this.parseMatrixParams())}parseMatrixParams(){let t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){let n=WD(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let o=Ml(this.remaining);o&&(r=o,this.capture(r))}t[ws(n)]=ws(r)}parseQueryParam(t){let n=ZD(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){let s=KD(this.remaining);s&&(r=s,this.capture(r))}let o=ag(n),i=ag(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){let n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Ml(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new I(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=F);let s=this.parseChildren();n[i]=Object.keys(s).length===1?s[F]:new H([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return this.peekStartsWith(t)?(this.remaining=this.remaining.substring(t.length),!0):!1}capture(t){if(!this.consumeOptional(t))throw new I(4011,!1)}};function Ig(e){return e.segments.length>0?new H([],{[F]:e}):e}function _g(e){let t={};for(let[r,o]of Object.entries(e.children)){let i=_g(o);if(r===F&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}let n=new H(e.segments,t);return YD(n)}function YD(e){if(e.numberOfChildren===1&&e.children[F]){let t=e.children[F];return new H(e.segments.concat(t.segments),t.children)}return e}function sn(e){return e instanceof mt}function QD(e,t,n=null,r=null){let o=Mg(e);return Sg(o,t,n,r)}function Mg(e){let t;function n(i){let s={};for(let c of i.children){let l=n(c);s[c.outlet]=l}let a=new H(i.url,s);return i===e&&(t=a),a}let r=n(e.root),o=Ig(r);return t??o}function Sg(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(t.length===0)return Sl(o,o,o,n,r);let i=JD(t);if(i.toRoot())return Sl(o,o,new H([],{}),n,r);let s=ew(i,o,e),a=s.processChildren?Qr(s.segmentGroup,s.index,i.commands):xg(s.segmentGroup,s.index,i.commands);return Sl(o,s.segmentGroup,a,n,r)}function Es(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function to(e){return typeof e=="object"&&e!=null&&e.outlets}function Sl(e,t,n,r,o){let i={};r&&Object.entries(r).forEach(([c,l])=>{i[c]=Array.isArray(l)?l.map(u=>`${u}`):`${l}`});let s;e===t?s=n:s=Ag(e,t,n);let a=Ig(_g(s));return new mt(a,i,o)}function Ag(e,t,n){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===t?r[o]=n:r[o]=Ag(i,t,n)}),new H(e.segments,r)}var bs=class{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Es(r[0]))throw new I(4003,!1);let o=r.find(to);if(o&&o!==yg(r))throw new I(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function JD(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new bs(!0,0,e);let t=0,n=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,l])=>{a[c]=typeof l=="string"?l.split("/"):l}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?n=!0:a===".."?t++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new bs(n,t,r)}var nr=class{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}};function ew(e,t,n){if(e.isAbsolute)return new nr(t,!0,0);if(!n)return new nr(t,!1,NaN);if(n.parent===null)return new nr(n,!0,0);let r=Es(e.commands[0])?0:1,o=n.segments.length-1+r;return tw(n,o,e.numberOfDoubleDots)}function tw(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new I(4005,!1);o=r.segments.length}return new nr(r,!1,o-i)}function nw(e){return to(e[0])?e[0].outlets:{[F]:e}}function xg(e,t,n){if(e??=new H([],{}),e.segments.length===0&&e.hasChildren())return Qr(e,t,n);let r=rw(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new H(e.segments.slice(0,r.pathIndex),{});return i.children[F]=new H(e.segments.slice(r.pathIndex),e.children),Qr(i,0,o)}else return r.match&&o.length===0?new H(e.segments,{}):r.match&&!e.hasChildren()?Rl(e,t,n):r.match?Qr(e,0,o):Rl(e,t,n)}function Qr(e,t,n){if(n.length===0)return new H(e.segments,{});{let r=nw(n),o={};if(Object.keys(r).some(i=>i!==F)&&e.children[F]&&e.numberOfChildren===1&&e.children[F].segments.length===0){let i=Qr(e.children[F],t,n);return new H(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=xg(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new H(e.segments,o)}}function rw(e,t,n){let r=0,o=t,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;let s=e.segments[o],a=n[r];if(to(a))break;let c=`${a}`,l=r<n.length-1?n[r+1]:null;if(o>0&&c===void 0)break;if(c&&l&&typeof l=="object"&&l.outlets===void 0){if(!lg(c,l,s))return i;r+=2}else{if(!lg(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Rl(e,t,n){let r=e.segments.slice(0,t),o=0;for(;o<n.length;){let i=n[o];if(to(i)){let c=ow(i.outlets);return new H(r,c)}if(o===0&&Es(n[0])){let c=e.segments[t];r.push(new rn(c.path,cg(n[0]))),o++;continue}let s=to(i)?i.outlets[F]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Es(a)?(r.push(new rn(s,cg(a))),o+=2):(r.push(new rn(s,{})),o++)}return new H(r,{})}function ow(e){let t={};return Object.entries(e).forEach(([n,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(t[n]=Rl(new H([],{}),0,r))}),t}function cg(e){let t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function lg(e,t,n){return e==n.path&&et(t,n.parameters)}var Jr="imperative",re=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(re||{}),Ue=class{constructor(t,n){this.id=t,this.url=n}},ar=class extends Ue{constructor(t,n,r="imperative",o=null){super(t,n),this.type=re.NavigationStart,this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},tt=class extends Ue{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=re.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},xe=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e}(xe||{}),Is=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Is||{}),gt=class extends Ue{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=re.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},kt=class extends Ue{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=re.NavigationSkipped}},no=class extends Ue{constructor(t,n,r,o){super(t,n),this.error=r,this.target=o,this.type=re.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},_s=class extends Ue{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=re.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ul=class extends Ue{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=re.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Pl=class extends Ue{constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i,this.type=re.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},kl=class extends Ue{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=re.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ll=class extends Ue{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=re.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Vl=class{constructor(t){this.route=t,this.type=re.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},jl=class{constructor(t){this.route=t,this.type=re.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Bl=class{constructor(t){this.snapshot=t,this.type=re.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Hl=class{constructor(t){this.snapshot=t,this.type=re.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},$l=class{constructor(t){this.snapshot=t,this.type=re.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},zl=class{constructor(t){this.snapshot=t,this.type=re.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ms=class{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=re.Scroll}toString(){let t=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${t}')`}},ro=class{},cr=class{constructor(t,n){this.url=t,this.navigationBehaviorOptions=n}};function iw(e,t){return e.providers&&!e._injector&&(e._injector=Zi(e.providers,t,`Route: ${e.path}`)),e._injector??t}function ze(e){return e.outlet||F}function sw(e,t){let n=e.filter(r=>ze(r)===t);return n.push(...e.filter(r=>ze(r)!==t)),n}function uo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){let n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}var Gl=class{get injector(){return uo(this.route?.snapshot)??this.rootInjector}set injector(t){}constructor(t){this.rootInjector=t,this.outlet=null,this.route=null,this.children=new fo(this.rootInjector),this.attachRef=null}},fo=(()=>{class e{constructor(n){this.rootInjector=n,this.contexts=new Map}onChildOutletCreated(n,r){let o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){let r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new Gl(this.rootInjector),this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static{this.\u0275fac=function(r){return new(r||e)(E(ge))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Ss=class{constructor(t){this._root=t}get root(){return this._root.value}parent(t){let n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){let n=Wl(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){let n=Wl(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){let n=ql(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return ql(t,this._root).map(n=>n.value)}};function Wl(e,t){if(e===t.value)return t;for(let n of t.children){let r=Wl(e,n);if(r)return r}return null}function ql(e,t){if(e===t.value)return[t];for(let n of t.children){let r=ql(e,n);if(r.length)return r.unshift(t),r}return[]}var Ae=class{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}};function tr(e){let t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}var As=class extends Ss{constructor(t,n){super(t),this.snapshot=n,nu(this,t)}toString(){return this.snapshot.toString()}};function Tg(e){let t=aw(e),n=new ie([new rn("",{})]),r=new ie({}),o=new ie({}),i=new ie({}),s=new ie(""),a=new an(n,r,i,s,o,F,e,t.root);return a.snapshot=t.root,new As(new Ae(a,[]),t)}function aw(e){let t={},n={},r={},o="",i=new rr([],t,r,o,n,F,e,null,{});return new Ts("",new Ae(i,[]))}var an=class{constructor(t,n,r,o,i,s,a,c){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(x(l=>l[co]))??M(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(x(t=>ir(t))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(x(t=>ir(t))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function xs(e,t,n="emptyOnly"){let r,{routeConfig:o}=e;return t!==null&&(n==="always"||o?.path===""||!t.component&&!t.routeConfig?.loadComponent)?r={params:C(C({},t.params),e.params),data:C(C({},t.data),e.data),resolve:C(C(C(C({},e.data),t.data),o?.data),e._resolvedData)}:r={params:C({},e.params),data:C({},e.data),resolve:C(C({},e.data),e._resolvedData??{})},o&&Ng(o)&&(r.resolve[co]=o.title),r}var rr=class{get title(){return this.data?.[co]}constructor(t,n,r,o,i,s,a,c,l){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=l}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ir(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ir(this.queryParams),this._queryParamMap}toString(){let t=this.url.map(r=>r.toString()).join("/"),n=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${t}', path:'${n}')`}},Ts=class extends Ss{constructor(t,n){super(n),this.url=t,nu(this,n)}toString(){return Fg(this._root)}};function nu(e,t){t.value._routerState=e,t.children.forEach(n=>nu(e,n))}function Fg(e){let t=e.children.length>0?` { ${e.children.map(Fg).join(", ")} } `:"";return`${e.value}${t}`}function Al(e){if(e.snapshot){let t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,et(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),et(t.params,n.params)||e.paramsSubject.next(n.params),RD(t.url,n.url)||e.urlSubject.next(n.url),et(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Zl(e,t){let n=et(e.params,t.params)&&LD(e.url,t.url),r=!e.parent!=!t.parent;return n&&!r&&(!e.parent||Zl(e.parent,t.parent))}function Ng(e){return typeof e.title=="string"||e.title===null}var ru=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=F,this.activateEvents=new Q,this.deactivateEvents=new Q,this.attachEvents=new Q,this.detachEvents=new Q,this.parentContexts=y(fo),this.location=y(Gn),this.changeDetector=y(qn),this.inputBinder=y(Us,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){let{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new I(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new I(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new I(4012,!1);this.location.detach();let n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){let n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new I(4013,!1);this._activatedRoute=n;let o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Xl(n,a,o.injector);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275dir=Be({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Vn]})}}return e})(),Xl=class e{__ngOutletInjector(t){return new e(this.route,this.childContexts,t)}constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===an?this.route:t===fo?this.childContexts:this.parent.get(t,n)}},Us=new b(""),ug=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){let{activatedRoute:r}=n,o=gr([r.queryParams,r.params,r.data]).pipe(De(([i,s,a],c)=>(a=C(C(C({},i),s),a),c===0?M(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(n);return}let s=op(r.component);if(!s){this.unsubscribeFromRouteData(n);return}for(let{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(n,o)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function cw(e,t,n){let r=oo(e,t._root,n?n._root:void 0);return new As(r,t)}function oo(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){let r=n.value;r._futureSnapshot=t.value;let o=lw(e,t,n);return new Ae(r,o)}else{if(e.shouldAttach(t.value)){let i=e.retrieve(t.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>oo(e,a)),s}}let r=uw(t.value),o=t.children.map(i=>oo(e,i));return new Ae(r,o)}}function lw(e,t,n){return t.children.map(r=>{for(let o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return oo(e,r,o);return oo(e,r)})}function uw(e){return new an(new ie(e.url),new ie(e.params),new ie(e.queryParams),new ie(e.fragment),new ie(e.data),e.outlet,e.component,e)}var io=class{constructor(t,n){this.redirectTo=t,this.navigationBehaviorOptions=n}},Og="ngNavigationCancelingError";function Fs(e,t){let{redirectTo:n,navigationBehaviorOptions:r}=sn(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Rg(!1,xe.Redirect);return o.url=n,o.navigationBehaviorOptions=r,o}function Rg(e,t){let n=new Error(`NavigationCancelingError: ${e||""}`);return n[Og]=!0,n.cancellationCode=t,n}function dw(e){return Ug(e)&&sn(e.url)}function Ug(e){return!!e&&e[Og]}var fw=(e,t,n,r)=>x(o=>(new Kl(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)),Kl=class{constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){let n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),Al(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){let o=tr(n);t.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=tr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){let r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=tr(t);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){let o=tr(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new zl(i.value.snapshot))}),t.children.length&&this.forwardEvent(new Hl(t.value.snapshot))}activateRoutes(t,n,r){let o=t.value,i=n?n.value:null;if(Al(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),Al(a.route.value),this.activateChildRoutes(t,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}else this.activateChildRoutes(t,null,r)}},Ns=class{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}},or=class{constructor(t,n){this.component=t,this.route=n}};function hw(e,t,n){let r=e._root,o=t?t._root:null;return Kr(r,o,n,[r.value])}function pw(e){let t=e.routeConfig?e.routeConfig.canActivateChild:null;return!t||t.length===0?null:{node:e,guards:t}}function ur(e,t){let n=Symbol(),r=t.get(e,n);return r===n?typeof e=="function"&&!Bd(e)?e:t.get(e):r}function Kr(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=tr(t);return e.children.forEach(s=>{gw(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>eo(a,n.getContext(s),o)),o}function gw(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=mw(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Ns(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Kr(e,t,a?a.children:null,r,o):Kr(e,t,n,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new or(a.outlet.component,s))}else s&&eo(t,a,o),o.canActivateChecks.push(new Ns(r)),i.component?Kr(e,null,a?a.children:null,r,o):Kr(e,null,n,r,o);return o}function mw(e,t,n){if(typeof n=="function")return n(e,t);switch(n){case"pathParamsChange":return!on(e.url,t.url);case"pathParamsOrQueryParamsChange":return!on(e.url,t.url)||!et(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Zl(e,t)||!et(e.queryParams,t.queryParams);case"paramsChange":default:return!Zl(e,t)}}function eo(e,t,n){let r=tr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?t?eo(s,t.children.getContext(i),n):eo(s,null,n):eo(s,t,n)}),o.component?t&&t.outlet&&t.outlet.isActivated?n.canDeactivateChecks.push(new or(t.outlet.component,o)):n.canDeactivateChecks.push(new or(null,o)):n.canDeactivateChecks.push(new or(null,o))}function ho(e){return typeof e=="function"}function yw(e){return typeof e=="boolean"}function vw(e){return e&&ho(e.canLoad)}function Cw(e){return e&&ho(e.canActivate)}function Dw(e){return e&&ho(e.canActivateChild)}function ww(e){return e&&ho(e.canDeactivate)}function Ew(e){return e&&ho(e.canMatch)}function Pg(e){return e instanceof rt||e?.name==="EmptyError"}var vs=Symbol("INITIAL_VALUE");function lr(){return De(e=>gr(e.map(t=>t.pipe(it(1),ia(vs)))).pipe(x(t=>{for(let n of t)if(n!==!0){if(n===vs)return vs;if(n===!1||bw(n))return n}return!0}),Ce(t=>t!==vs),it(1)))}function bw(e){return sn(e)||e instanceof io}function Iw(e,t){return K(n=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return s.length===0&&i.length===0?M(j(C({},n),{guardsResult:!0})):_w(s,r,o,e).pipe(K(a=>a&&yw(a)?Mw(r,i,e,t):M(a)),x(a=>j(C({},n),{guardsResult:a})))})}function _w(e,t,n,r){return W(e).pipe(K(o=>Fw(o.component,o.route,n,t,r)),Ge(o=>o!==!0,!0))}function Mw(e,t,n,r){return W(t).pipe(ot(o=>yn(Aw(o.route.parent,r),Sw(o.route,r),Tw(e,o.path,n),xw(e,o.route,n))),Ge(o=>o!==!0,!0))}function Sw(e,t){return e!==null&&t&&t(new $l(e)),M(!0)}function Aw(e,t){return e!==null&&t&&t(new Bl(e)),M(!0)}function xw(e,t,n){let r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||r.length===0)return M(!0);let o=r.map(i=>zo(()=>{let s=uo(t)??n,a=ur(i,s),c=Cw(a)?a.canActivate(t,e):Me(s,()=>a(t,e));return Vt(c).pipe(Ge())}));return M(o).pipe(lr())}function Tw(e,t,n){let r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>pw(s)).filter(s=>s!==null).map(s=>zo(()=>{let a=s.guards.map(c=>{let l=uo(s.node)??n,u=ur(c,l),d=Dw(u)?u.canActivateChild(r,e):Me(l,()=>u(r,e));return Vt(d).pipe(Ge())});return M(a).pipe(lr())}));return M(i).pipe(lr())}function Fw(e,t,n,r,o){let i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;if(!i||i.length===0)return M(!0);let s=i.map(a=>{let c=uo(t)??o,l=ur(a,c),u=ww(l)?l.canDeactivate(e,t,n,r):Me(c,()=>l(e,t,n,r));return Vt(u).pipe(Ge())});return M(s).pipe(lr())}function Nw(e,t,n,r){let o=t.canLoad;if(o===void 0||o.length===0)return M(!0);let i=o.map(s=>{let a=ur(s,e),c=vw(a)?a.canLoad(t,n):Me(e,()=>a(t,n));return Vt(c)});return M(i).pipe(lr(),kg(r))}function kg(e){return Ks(ee(t=>{if(typeof t!="boolean")throw Fs(e,t)}),x(t=>t===!0))}function Ow(e,t,n,r){let o=t.canMatch;if(!o||o.length===0)return M(!0);let i=o.map(s=>{let a=ur(s,e),c=Ew(a)?a.canMatch(t,n):Me(e,()=>a(t,n));return Vt(c)});return M(i).pipe(lr(),kg(r))}var so=class{constructor(t){this.segmentGroup=t||null}},ao=class extends Error{constructor(t){super(),this.urlTree=t}};function er(e){return gn(new so(e))}function Rw(e){return gn(new I(4e3,!1))}function Uw(e){return gn(Rg(!1,xe.GuardRejected))}var Yl=class{constructor(t,n){this.urlSerializer=t,this.urlTree=n}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return M(r);if(o.numberOfChildren>1||!o.children[F])return Rw(`${t.redirectTo}`);o=o.children[F]}}applyRedirectCommands(t,n,r,o,i){if(typeof n!="string"){let a=n,{queryParams:c,fragment:l,routeConfig:u,url:d,outlet:m,params:p,data:v,title:D}=o,_=Me(i,()=>a({params:p,data:v,queryParams:c,fragment:l,routeConfig:u,url:d,outlet:m,title:D}));if(_ instanceof mt)throw new ao(_);n=_}let s=this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r);if(n[0]==="/")throw new ao(s);return s}applyRedirectCreateUrlTree(t,n,r,o){let i=this.createSegmentGroup(t,n.root,r,o);return new mt(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){let r={};return Object.entries(t).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){let i=this.createSegments(t,n.segments,r,o),s={};return Object.entries(n.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(t,c,r,o)}),new H(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path[0]===":"?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){let o=r[n.path.substring(1)];if(!o)throw new I(4001,!1);return o}findOrReturn(t,n){let r=0;for(let o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}},Ql={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Pw(e,t,n,r,o){let i=ou(e,t,n);return i.matched?(r=iw(t,r),Ow(r,t,n,o).pipe(x(s=>s===!0?i:C({},Ql)))):M(i)}function ou(e,t,n){if(t.path==="**")return kw(n);if(t.path==="")return t.pathMatch==="full"&&(e.hasChildren()||n.length>0)?C({},Ql):{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};let o=(t.matcher||OD)(n,e,t);if(!o)return C({},Ql);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?C(C({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function kw(e){return{matched:!0,parameters:e.length>0?yg(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function dg(e,t,n,r){return n.length>0&&jw(e,n,r)?{segmentGroup:new H(t,Vw(r,new H(n,e.children))),slicedSegments:[]}:n.length===0&&Bw(e,n,r)?{segmentGroup:new H(e.segments,Lw(e,n,r,e.children)),slicedSegments:n}:{segmentGroup:new H(e.segments,e.children),slicedSegments:n}}function Lw(e,t,n,r){let o={};for(let i of n)if(Ps(e,t,i)&&!r[ze(i)]){let s=new H([],{});o[ze(i)]=s}return C(C({},r),o)}function Vw(e,t){let n={};n[F]=t;for(let r of e)if(r.path===""&&ze(r)!==F){let o=new H([],{});n[ze(r)]=o}return n}function jw(e,t,n){return n.some(r=>Ps(e,t,r)&&ze(r)!==F)}function Bw(e,t,n){return n.some(r=>Ps(e,t,r))}function Ps(e,t,n){return(e.hasChildren()||t.length>0)&&n.pathMatch==="full"?!1:n.path===""}function Hw(e,t,n,r){return ze(e)!==r&&(r===F||!Ps(t,n,e))?!1:ou(t,e,n).matched}function $w(e,t,n){return t.length===0&&!e.children[n]}var Jl=class{};function zw(e,t,n,r,o,i,s="emptyOnly"){return new eu(e,t,n,r,o,s,i).recognize()}var Gw=31,eu=class{constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Yl(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(t){return new I(4002,`'${t.segmentGroup}'`)}recognize(){let t=dg(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(t).pipe(x(({children:n,rootSnapshot:r})=>{let o=new Ae(r,n),i=new Ts("",o),s=QD(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(t){let n=new rr([],Object.freeze({}),Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),F,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,t,F,n).pipe(x(r=>({children:r,rootSnapshot:n})),Ct(r=>{if(r instanceof ao)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof so?this.noMatchError(r):r}))}processSegmentGroup(t,n,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(t,n,r,i):this.processSegment(t,n,r,r.segments,o,!0,i).pipe(x(s=>s instanceof Ae?[s]:[]))}processChildren(t,n,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return W(i).pipe(ot(s=>{let a=r.children[s],c=sw(n,s);return this.processSegmentGroup(t,c,a,s,o)}),oa((s,a)=>(s.push(...a),s)),Dt(null),ra(),K(s=>{if(s===null)return er(r);let a=Lg(s);return Ww(a),M(a)}))}processSegment(t,n,r,o,i,s,a){return W(n).pipe(ot(c=>this.processSegmentAgainstRoute(c._injector??t,n,c,r,o,i,s,a).pipe(Ct(l=>{if(l instanceof so)return M(null);throw l}))),Ge(c=>!!c),Ct(c=>{if(Pg(c))return $w(r,o,i)?M(new Jl):er(r);throw c}))}processSegmentAgainstRoute(t,n,r,o,i,s,a,c){return Hw(r,o,i,s)?r.redirectTo===void 0?this.matchSegmentAgainstRoute(t,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s,c):er(o):er(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s,a){let{matched:c,parameters:l,consumedSegments:u,positionalParamSegments:d,remainingSegments:m}=ou(n,o,i);if(!c)return er(n);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Gw&&(this.allowRedirects=!1));let p=new rr(i,l,Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,fg(o),ze(o),o.component??o._loadedComponent??null,o,hg(o)),v=xs(p,a,this.paramsInheritanceStrategy);p.params=Object.freeze(v.params),p.data=Object.freeze(v.data);let D=this.applyRedirects.applyRedirectCommands(u,o.redirectTo,d,p,t);return this.applyRedirects.lineralizeSegments(o,D).pipe(K(_=>this.processSegment(t,r,n,_.concat(m),s,!1,a)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a=Pw(n,r,o,t,this.urlSerializer);return r.path==="**"&&(n.children={}),a.pipe(De(c=>c.matched?(t=r._injector??t,this.getChildConfig(t,r,o).pipe(De(({routes:l})=>{let u=r._loadedInjector??t,{parameters:d,consumedSegments:m,remainingSegments:p}=c,v=new rr(m,d,Object.freeze(C({},this.urlTree.queryParams)),this.urlTree.fragment,fg(r),ze(r),r.component??r._loadedComponent??null,r,hg(r)),D=xs(v,s,this.paramsInheritanceStrategy);v.params=Object.freeze(D.params),v.data=Object.freeze(D.data);let{segmentGroup:_,slicedSegments:T}=dg(n,m,p,l);if(T.length===0&&_.hasChildren())return this.processChildren(u,l,_,v).pipe(x(U=>new Ae(v,U)));if(l.length===0&&T.length===0)return M(new Ae(v,[]));let de=ze(r)===i;return this.processSegment(u,l,_,T,de?F:i,!0,v).pipe(x(U=>new Ae(v,U instanceof Ae?[U]:[])))}))):er(n)))}getChildConfig(t,n,r){return n.children?M({routes:n.children,injector:t}):n.loadChildren?n._loadedRoutes!==void 0?M({routes:n._loadedRoutes,injector:n._loadedInjector}):Nw(t,n,r,this.urlSerializer).pipe(K(o=>o?this.configLoader.loadChildren(t,n).pipe(ee(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):Uw(n))):M({routes:[],injector:t})}};function Ww(e){e.sort((t,n)=>t.value.outlet===F?-1:n.value.outlet===F?1:t.value.outlet.localeCompare(n.value.outlet))}function qw(e){let t=e.value.routeConfig;return t&&t.path===""}function Lg(e){let t=[],n=new Set;for(let r of e){if(!qw(r)){t.push(r);continue}let o=t.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),n.add(o)):t.push(r)}for(let r of n){let o=Lg(r.children);t.push(new Ae(r.value,o))}return t.filter(r=>!n.has(r))}function fg(e){return e.data||{}}function hg(e){return e.resolve||{}}function Zw(e,t,n,r,o,i){return K(s=>zw(e,t,n,r,s.extractedUrl,o,i).pipe(x(({state:a,tree:c})=>j(C({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function Xw(e,t){return K(n=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return M(n);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let l of Vg(c))s.add(l);let a=0;return W(s).pipe(ot(c=>i.has(c)?Kw(c,r,e,t):(c.data=xs(c,c.parent,e).resolve,M(void 0))),ee(()=>a++),vn(1),K(c=>a===s.size?M(n):Ie))})}function Vg(e){let t=e.children.map(n=>Vg(n)).flat();return[e,...t]}function Kw(e,t,n,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!Ng(o)&&(i[co]=o.title),Yw(i,e,t,r).pipe(x(s=>(e._resolvedData=s,e.data=xs(e,e.parent,n).resolve,null)))}function Yw(e,t,n,r){let o=Fl(e);if(o.length===0)return M({});let i={};return W(o).pipe(K(s=>Qw(e[s],t,n,r).pipe(Ge(),ee(a=>{if(a instanceof io)throw Fs(new sr,a);i[s]=a}))),vn(1),na(i),Ct(s=>Pg(s)?Ie:gn(s)))}function Qw(e,t,n,r){let o=uo(t)??r,i=ur(e,o),s=i.resolve?i.resolve(t,n):Me(o,()=>i(t,n));return Vt(s)}function xl(e){return De(t=>{let n=e(t);return n?W(n).pipe(x(()=>t)):M(t)})}var jg=(()=>{class e{buildTitle(n){let r,o=n.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===F);return r}getResolvedTitleForRoute(n){return n.data[co]}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(Jw),providedIn:"root"})}}return e})(),Jw=(()=>{class e extends jg{constructor(n){super(),this.title=n}updateTitle(n){let r=this.buildTitle(n);r!==void 0&&this.title.setTitle(r)}static{this.\u0275fac=function(r){return new(r||e)(E(Hp))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),po=new b("",{providedIn:"root",factory:()=>({})}),e3=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275cmp=te({type:e,selectors:[["ng-component"]],standalone:!0,features:[Gh],decls:1,vars:0,template:function(r,o){r&1&&g(0,"router-outlet")},dependencies:[ru],encapsulation:2})}}return e})();function iu(e){let t=e.children&&e.children.map(iu),n=t?j(C({},e),{children:t}):C({},e);return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==F&&(n.component=e3),n}var Os=new b(""),su=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=y(ts)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return M(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);let r=Vt(n.loadComponent()).pipe(x(Bg),ee(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),wt(()=>{this.componentLoaders.delete(n)})),o=new pn(r,()=>new J).pipe(hn());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return M({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=t3(r,this.compiler,n,this.onLoadEndListener).pipe(wt(()=>{this.childrenLoaders.delete(r)})),s=new pn(i,()=>new J).pipe(hn());return this.childrenLoaders.set(r,s),s}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function t3(e,t,n,r){return Vt(e.loadChildren()).pipe(x(Bg),K(o=>o instanceof Ir||Array.isArray(o)?M(o):W(t.compileModuleAsync(o))),x(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(n).injector,s=i.get(Os,[],{optional:!0,self:!0}).flat()),{routes:s.map(iu),injector:i}}))}function n3(e){return e&&typeof e=="object"&&"default"in e}function Bg(e){return n3(e)?e.default:e}var au=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(r3),providedIn:"root"})}}return e})(),r3=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Hg=new b(""),$g=new b("");function o3(e,t,n){let r=e.get($g),o=e.get(ue);return e.get($).runOutsideAngular(()=>{if(!o.startViewTransition||r.skipNextTransition)return r.skipNextTransition=!1,new Promise(l=>setTimeout(l));let i,s=new Promise(l=>{i=l}),a=o.startViewTransition(()=>(i(),i3(e))),{onViewTransitionCreated:c}=r;return c&&Me(e,()=>c({transition:a,from:t,to:n})),s})}function i3(e){return new Promise(t=>{Lc({read:()=>setTimeout(t)},{injector:e})})}var s3=new b(""),cu=(()=>{class e{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new J,this.transitionAbortSubject=new J,this.configLoader=y(su),this.environmentInjector=y(ge),this.urlSerializer=y(lo),this.rootContexts=y(fo),this.location=y(Kn),this.inputBindingEnabled=y(Us,{optional:!0})!==null,this.titleStrategy=y(jg),this.options=y(po,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=y(au),this.createViewTransition=y(Hg,{optional:!0}),this.navigationErrorHandler=y(s3,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>M(void 0),this.rootComponentType=null;let n=o=>this.events.next(new Vl(o)),r=o=>this.events.next(new jl(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=n}complete(){this.transitions?.complete()}handleNavigationRequest(n){let r=++this.navigationId;this.transitions?.next(j(C(C({},this.transitions.value),n),{id:r}))}setupNavigations(n,r,o){return this.transitions=new ie({id:0,currentUrlTree:r,currentRawUrl:r,extractedUrl:this.urlHandlingStrategy.extract(r),urlAfterRedirects:this.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:()=>{},reject:()=>{},promise:Promise.resolve(!0),source:Jr,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Ce(i=>i.id!==0),x(i=>j(C({},i),{extractedUrl:this.urlHandlingStrategy.extract(i.rawUrl)})),De(i=>{let s=!1,a=!1;return M(i).pipe(De(c=>{if(this.navigationId>i.id)return this.cancelNavigationTransition(i,"",xe.SupersededByNewNavigation),Ie;this.currentTransition=i,this.currentNavigation={id:c.id,initialUrl:c.rawUrl,extractedUrl:c.extractedUrl,targetBrowserUrl:typeof c.extras.browserUrl=="string"?this.urlSerializer.parse(c.extras.browserUrl):c.extras.browserUrl,trigger:c.source,extras:c.extras,previousNavigation:this.lastSuccessfulNavigation?j(C({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let l=!n.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),u=c.extras.onSameUrlNavigation??n.onSameUrlNavigation;if(!l&&u!=="reload"){let d="";return this.events.next(new kt(c.id,this.urlSerializer.serialize(c.rawUrl),d,Is.IgnoredSameUrlNavigation)),c.resolve(!1),Ie}if(this.urlHandlingStrategy.shouldProcessUrl(c.rawUrl))return M(c).pipe(De(d=>{let m=this.transitions?.getValue();return this.events.next(new ar(d.id,this.urlSerializer.serialize(d.extractedUrl),d.source,d.restoredState)),m!==this.transitions?.getValue()?Ie:Promise.resolve(d)}),Zw(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,this.paramsInheritanceStrategy),ee(d=>{i.targetSnapshot=d.targetSnapshot,i.urlAfterRedirects=d.urlAfterRedirects,this.currentNavigation=j(C({},this.currentNavigation),{finalUrl:d.urlAfterRedirects});let m=new _s(d.id,this.urlSerializer.serialize(d.extractedUrl),this.urlSerializer.serialize(d.urlAfterRedirects),d.targetSnapshot);this.events.next(m)}));if(l&&this.urlHandlingStrategy.shouldProcessUrl(c.currentRawUrl)){let{id:d,extractedUrl:m,source:p,restoredState:v,extras:D}=c,_=new ar(d,this.urlSerializer.serialize(m),p,v);this.events.next(_);let T=Tg(this.rootComponentType).snapshot;return this.currentTransition=i=j(C({},c),{targetSnapshot:T,urlAfterRedirects:m,extras:j(C({},D),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=m,M(i)}else{let d="";return this.events.next(new kt(c.id,this.urlSerializer.serialize(c.extractedUrl),d,Is.IgnoredByUrlHandlingStrategy)),c.resolve(!1),Ie}}),ee(c=>{let l=new Ul(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(l)}),x(c=>(this.currentTransition=i=j(C({},c),{guards:hw(c.targetSnapshot,c.currentSnapshot,this.rootContexts)}),i)),Iw(this.environmentInjector,c=>this.events.next(c)),ee(c=>{if(i.guardsResult=c.guardsResult,c.guardsResult&&typeof c.guardsResult!="boolean")throw Fs(this.urlSerializer,c.guardsResult);let l=new Pl(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot,!!c.guardsResult);this.events.next(l)}),Ce(c=>c.guardsResult?!0:(this.cancelNavigationTransition(c,"",xe.GuardRejected),!1)),xl(c=>{if(c.guards.canActivateChecks.length)return M(c).pipe(ee(l=>{let u=new kl(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}),De(l=>{let u=!1;return M(l).pipe(Xw(this.paramsInheritanceStrategy,this.environmentInjector),ee({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(l,"",xe.NoDataFromResolver)}}))}),ee(l=>{let u=new Ll(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(u)}))}),xl(c=>{let l=u=>{let d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe(ee(m=>{u.component=m}),x(()=>{})));for(let m of u.children)d.push(...l(m));return d};return gr(l(c.targetSnapshot.root)).pipe(Dt(null),it(1))}),xl(()=>this.afterPreactivation()),De(()=>{let{currentSnapshot:c,targetSnapshot:l}=i,u=this.createViewTransition?.(this.environmentInjector,c.root,l.root);return u?W(u).pipe(x(()=>i)):M(i)}),x(c=>{let l=cw(n.routeReuseStrategy,c.targetSnapshot,c.currentRouterState);return this.currentTransition=i=j(C({},c),{targetRouterState:l}),this.currentNavigation.targetRouterState=l,i}),ee(()=>{this.events.next(new ro)}),fw(this.rootContexts,n.routeReuseStrategy,c=>this.events.next(c),this.inputBindingEnabled),it(1),ee({next:c=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new tt(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects))),this.titleStrategy?.updateTitle(c.targetRouterState.snapshot),c.resolve(!0)},complete:()=>{s=!0}}),sa(this.transitionAbortSubject.pipe(ee(c=>{throw c}))),wt(()=>{!s&&!a&&this.cancelNavigationTransition(i,"",xe.SupersededByNewNavigation),this.currentTransition?.id===i.id&&(this.currentNavigation=null,this.currentTransition=null)}),Ct(c=>{if(a=!0,Ug(c))this.events.next(new gt(i.id,this.urlSerializer.serialize(i.extractedUrl),c.message,c.cancellationCode)),dw(c)?this.events.next(new cr(c.url,c.navigationBehaviorOptions)):i.resolve(!1);else{let l=new no(i.id,this.urlSerializer.serialize(i.extractedUrl),c,i.targetSnapshot??void 0);try{let u=Me(this.environmentInjector,()=>this.navigationErrorHandler?.(l));if(u instanceof io){let{message:d,cancellationCode:m}=Fs(this.urlSerializer,u);this.events.next(new gt(i.id,this.urlSerializer.serialize(i.extractedUrl),d,m)),this.events.next(new cr(u.redirectTo,u.navigationBehaviorOptions))}else{this.events.next(l);let d=n.errorHandler(c);i.resolve(!!d)}}catch(u){this.options.resolveNavigationPromiseOnError?i.resolve(!1):i.reject(u)}}return Ie}))}))}cancelNavigationTransition(n,r,o){let i=new gt(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let n=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return n.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function a3(e){return e!==Jr}var c3=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(l3),providedIn:"root"})}}return e})(),tu=class{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}},l3=(()=>{class e extends tu{static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=Li(e)))(o||e)}})()}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),zg=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:()=>y(u3),providedIn:"root"})}}return e})(),u3=(()=>{class e extends zg{constructor(){super(...arguments),this.location=y(Kn),this.urlSerializer=y(lo),this.options=y(po,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=y(au),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new mt,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=Tg(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(n){return this.location.subscribe(r=>{r.type==="popstate"&&n(r.url,r.state)})}handleRouterEvent(n,r){if(n instanceof ar)this.stateMemento=this.createStateMemento();else if(n instanceof kt)this.rawUrlTree=r.initialUrl;else if(n instanceof _s){if(this.urlUpdateStrategy==="eager"&&!r.extras.skipLocationChange){let o=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl);this.setBrowserUrl(r.targetBrowserUrl??o,r)}}else n instanceof ro?(this.currentUrlTree=r.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(r.finalUrl,r.initialUrl),this.routerState=r.targetRouterState,this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(r.targetBrowserUrl??this.rawUrlTree,r)):n instanceof gt&&(n.code===xe.GuardRejected||n.code===xe.NoDataFromResolver)?this.restoreHistory(r):n instanceof no?this.restoreHistory(r,!0):n instanceof tt&&(this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId)}setBrowserUrl(n,r){let o=n instanceof mt?this.urlSerializer.serialize(n):n;if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){let i=this.browserPageId,s=C(C({},r.extras.state),this.generateNgRouterState(r.id,i));this.location.replaceState(o,"",s)}else{let i=C(C({},r.extras.state),this.generateNgRouterState(r.id,this.browserPageId+1));this.location.go(o,"",i)}}restoreHistory(n,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.currentUrlTree===n.finalUrl&&i===0&&(this.resetState(n),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return this.canceledNavigationResolution==="computed"?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static{this.\u0275fac=(()=>{let n;return function(o){return(n||(n=Li(e)))(o||e)}})()}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Yr=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(Yr||{});function Gg(e,t){e.events.pipe(Ce(n=>n instanceof tt||n instanceof gt||n instanceof no||n instanceof kt),x(n=>n instanceof tt||n instanceof kt?Yr.COMPLETE:(n instanceof gt?n.code===xe.Redirect||n.code===xe.SupersededByNewNavigation:!1)?Yr.REDIRECTING:Yr.FAILED),Ce(n=>n!==Yr.REDIRECTING),it(1)).subscribe(()=>{t()})}function d3(e){throw e}var f3={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},h3={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Lt=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.console=y(Ki),this.stateManager=y(zg),this.options=y(po,{optional:!0})||{},this.pendingTasks=y(ut),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=y(cu),this.urlSerializer=y(lo),this.location=y(Kn),this.urlHandlingStrategy=y(au),this._events=new J,this.errorHandler=this.options.errorHandler||d3,this.navigated=!1,this.routeReuseStrategy=y(c3),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=y(Os,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!y(Us,{optional:!0}),this.eventsSubscription=new Z,this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:n=>{this.console.warn(n)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let n=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof gt&&r.code!==xe.Redirect&&r.code!==xe.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof tt)this.navigated=!0;else if(r instanceof cr){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=C({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||a3(o.source)},s);this.scheduleNavigation(a,Jr,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}g3(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Jr,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((n,r)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(n,"popstate",r)},0)})}navigateToSyncWithBrowser(n,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=C({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(iu),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,l=c?this.currentUrlTree.fragment:s,u=null;switch(a){case"merge":u=C(C({},this.currentUrlTree.queryParams),i);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}u!==null&&(u=this.removeEmptyProps(u));let d;try{let m=o?o.snapshot:this.routerState.snapshot.root;d=Mg(m)}catch{(typeof n[0]!="string"||n[0][0]!=="/")&&(n=[]),d=this.currentUrlTree.root}return Sg(d,n,u,l??null)}navigateByUrl(n,r={skipLocationChange:!1}){let o=sn(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Jr,null,r)}navigate(n,r={skipLocationChange:!1}){return p3(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){try{return this.urlSerializer.parse(n)}catch{return this.urlSerializer.parse("/")}}isActive(n,r){let o;if(r===!0?o=C({},f3):r===!1?o=C({},h3):o=r,sn(n))return sg(this.currentUrlTree,n,o);let i=this.parseUrl(n);return sg(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.entries(n).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,l;s?(a=s.resolve,c=s.reject,l=s.promise):l=new Promise((d,m)=>{a=d,c=m});let u=this.pendingTasks.add();return Gg(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:n,extras:i,resolve:a,reject:c,promise:l,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),l.catch(d=>Promise.reject(d))}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function p3(e){for(let t=0;t<e.length;t++)if(e[t]==null)throw new I(4008,!1)}function g3(e){return!(e instanceof ro)&&!(e instanceof cr)}var Wg=(()=>{class e{constructor(n,r,o,i,s,a){this.router=n,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.href=null,this.onChanges=new J,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1,this.routerLinkInput=null;let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area",this.isAnchorElement?this.subscription=n.events.subscribe(l=>{l instanceof tt&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(n){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",n)}ngOnChanges(n){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(n){n==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(sn(n)?this.routerLinkInput=n:this.routerLinkInput=Array.isArray(n)?n:[n],this.setTabIndexIfNotOnNativeEl("0"))}onClick(n,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(n!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let n=this.urlTree;this.href=n!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(n)):null;let r=this.href===null?null:ih(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",r)}applyAttributeValue(n,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,n,r):o.removeAttribute(i,n)}get urlTree(){return this.routerLinkInput===null?null:sn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(r){return new(r||e)(z(Lt),z(an),bc("tabindex"),z($n),z(Yt),z(ht))}}static{this.\u0275dir=Be({type:e,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(r,o){r&1&&Je("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&Xi("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Zn],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Zn],replaceUrl:[2,"replaceUrl","replaceUrl",Zn],routerLink:"routerLink"},standalone:!0,features:[jc,Vn]})}}return e})();var Rs=class{};var m3=(()=>{class e{constructor(n,r,o,i,s){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(Ce(n=>n instanceof tt),ot(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Zi(i.providers,n,`Route: ${i.path}`));let s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return W(o).pipe(mn())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(n,r):o=M(null);let i=o.pipe(K(s=>s===null?M(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(r);return W([i,s]).pipe(mn())}else return i})}static{this.\u0275fac=function(r){return new(r||e)(E(Lt),E(ts),E(ge),E(Rs),E(su))}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),qg=new b(""),y3=(()=>{class e{constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration||="disabled",s.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof ar?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof tt?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof kt&&n.code===Is.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Ms&&(n.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(n.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Ms(n,this.lastSource==="popstate"?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(r){vh()}}static{this.\u0275prov=w({token:e,factory:e.\u0275fac})}}return e})();function v3(e){return e.routerState.root}function go(e,t){return{\u0275kind:e,\u0275providers:t}}function C3(){let e=y(_e);return t=>{let n=e.get(Ot);if(t!==n.components[0])return;let r=e.get(Lt),o=e.get(Zg);e.get(lu)===1&&r.initialNavigation(),e.get(Xg,null,N.Optional)?.setUpPreloading(),e.get(qg,null,N.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var Zg=new b("",{factory:()=>new J}),lu=new b("",{providedIn:"root",factory:()=>1});function D3(){return go(2,[{provide:lu,useValue:0},{provide:Ji,multi:!0,deps:[_e],useFactory:t=>{let n=t.get(fp,Promise.resolve());return()=>n.then(()=>new Promise(r=>{let o=t.get(Lt),i=t.get(Zg);Gg(o,()=>{r(!0)}),t.get(cu).afterPreactivation=()=>(r(!0),i.closed?M(void 0):i),o.initialNavigation()}))}}])}function w3(){return go(3,[{provide:Ji,multi:!0,useFactory:()=>{let t=y(Lt);return()=>{t.setUpLocationChangeListener()}}},{provide:lu,useValue:2}])}var Xg=new b("");function E3(e){return go(0,[{provide:Xg,useExisting:m3},{provide:Rs,useExisting:e}])}function b3(){return go(8,[ug,{provide:Us,useExisting:ug}])}function I3(e){let t=[{provide:Hg,useValue:o3},{provide:$g,useValue:C({skipNextTransition:!!e?.skipInitialTransition},e)}];return go(9,t)}var pg=new b("ROUTER_FORROOT_GUARD"),_3=[Kn,{provide:lo,useClass:sr},Lt,fo,{provide:an,useFactory:v3,deps:[Lt]},su,[]],uu=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[_3,[],{provide:Os,multi:!0,useValue:n},{provide:pg,useFactory:x3,deps:[[Lt,new Ni,new ac]]},{provide:po,useValue:r||{}},r?.useHash?S3():A3(),M3(),r?.preloadingStrategy?E3(r.preloadingStrategy).\u0275providers:[],r?.initialNavigation?T3(r):[],r?.bindToComponentInputs?b3().\u0275providers:[],r?.enableViewTransitions?I3().\u0275providers:[],F3()]}}static forChild(n){return{ngModule:e,providers:[{provide:Os,multi:!0,useValue:n}]}}static{this.\u0275fac=function(r){return new(r||e)(E(pg,8))}}static{this.\u0275mod=ce({type:e})}static{this.\u0275inj=ae({})}}return e})();function M3(){return{provide:qg,useFactory:()=>{let e=y(vp),t=y($),n=y(po),r=y(cu),o=y(lo);return n.scrollOffset&&e.setOffset(n.scrollOffset),new y3(o,r,e,t,n)}}}function S3(){return{provide:ht,useClass:pp}}function A3(){return{provide:ht,useClass:tl}}function x3(e){return"guarded"}function T3(e){return[e.initialNavigation==="disabled"?w3().\u0275providers:[],e.initialNavigation==="enabledBlocking"?D3().\u0275providers:[]]}var gg=new b("");function F3(){return[{provide:gg,useFactory:C3},{provide:es,multi:!0,useExisting:gg}]}var Yg=(()=>{class e{http;about={name:"",description:"",photo:"",email:"",portfolioUrl:""};constructor(n){this.http=n}ngOnInit(){this.getAboutData()}getAboutData(){this.http.get("https://portflio-backend-uiv7.onrender.com/api/about").subscribe(n=>{this.about=n},n=>{console.error("Error fetching About data:",n)})}static \u0275fac=function(r){return new(r||e)(z(nn))};static \u0275cmp=te({type:e,selectors:[["app-about"]],decls:22,vars:2,consts:[["id","about-me",1,"about-me"],[1,"container"],[1,"about-me-container"],[1,"about-me-title"],[1,"about-me-flex-container"],[1,"about-me-image"],[1,"back-div"],[1,"main-image"],["alt","Profile Picture",3,"src"],[1,"about-me-content"],["href","https://drive.google.com/file/d/1So0fhL4n2hvNY3CpCJktXAbe1ZYJg8be/view?usp=sharing","target","_blank"],[1,"cta"],["width","15px","height","10px","viewBox","0 0 13 10"],["d","M1,5 L11,5"],["points","8 1 12 5 8 9"],[1,"text"]],template:function(r,o){r&1&&(f(0,"section",0)(1,"div",1)(2,"div",2)(3,"div",3),R(4," About "),g(5,"br"),R(6," Roaa Ayman "),h(),f(7,"div",4)(8,"div",5),g(9,"div",6),f(10,"div",7),g(11,"img",8),h()(),f(12,"div",9)(13,"a",10)(14,"button",11)(15,"span"),R(16,"Resume"),h(),Bn(),f(17,"svg",12),g(18,"path",13)(19,"polyline",14),h()()(),Hn(),f(20,"div",15),R(21),h()()()()()()),r&2&&(ne(11),Se("src",o.about.photo,Qt),ne(10),Wn(" ",o.about.description||"Loading description..."," "))},styles:['@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:100;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WRhyzbi.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459W1hyzbi.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459WZhyzbi.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wdhyzbi.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Montserrat;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/montserrat/v30/JTUSjIg1_i6t8kCHKm459Wlhyw.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:200;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:300;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:600;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:800;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:900;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOOaBXso.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIMeaBXso.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIOuaBXso.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofIO-aBXso.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Nunito;font-style:normal;font-weight:1000;font-display:swap;src:url(https://fonts.gstatic.com/s/nunito/v31/XRXV3I6Li01BKofINeaB.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}*[_ngcontent-%COMP%]{box-sizing:border-box;margin:0;padding:0;font-family:Nunito,sans-serif}html[_ngcontent-%COMP%]{scroll-behavior:smooth}body[_ngcontent-%COMP%]{background-color:#08505d}.montserrat[_ngcontent-%COMP%]{font-family:Montserrat,sans-serif}img[_ngcontent-%COMP%]{width:100%;-o-object-fit:cover;object-fit:cover}.container[_ngcontent-%COMP%]{max-width:1300px;margin:0 auto;padding:0 40px}@media (min-width: 1200px) and (max-width: 1441px){.container[_ngcontent-%COMP%]{max-width:1250px;padding:0 36px}}@media (max-width: 767px){.container[_ngcontent-%COMP%]{padding:0 30px}}@media (max-width: 479px){.container[_ngcontent-%COMP%]{padding:0 30px 0 20px}}.about-me[_ngcontent-%COMP%]{padding-top:50px;padding-bottom:50px}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{position:relative}@media (max-width: 960px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]{padding-bottom:100px}}.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:55px;color:#9b6195;font-weight:700;margin-bottom:20px}@media (max-width: 500px){.about-me[_ngcontent-%COMP%]   .about-me-container[_ngcontent-%COMP%]   .about-me-title[_ngcontent-%COMP%]{font-size:30px}}.about-me-flex-container[_ngcontent-%COMP%]{margin-top:-25px;margin-left:150px;left:100px;display:flex;justify-content:space-between}@media (max-width: 960px){.about-me-flex-container[_ngcontent-%COMP%]{flex-direction:column;justify-content:center;align-items:center;margin-left:0;gap:50px}}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]{margin-top:-10px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]{position:relative;width:400px;height:400px}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]{width:300px;height:300px}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .back-div[_ngcontent-%COMP%]{position:absolute;bottom:0;z-index:-3;background-color:#9b6195;width:80%;height:80%}.about-me-title[_ngcontent-%COMP%]{font-family:Pacifico,cursive}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .black-image[_ngcontent-%COMP%]{z-index:-2;position:absolute;left:10px;bottom:10px;height:100%}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .black-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]{width:75%;height:75%;overflow:hidden;position:absolute;right:15%;top:15%;box-shadow:#967eb9 0 7px 50px;transition:all .2s ease-out}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]:hover{transform-origin:top center;transform:scale(1.5);border-radius:25px}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform-origin:center center;transform:scale(2);-o-object-fit:cover;object-fit:cover;transition:all .2s ease-out}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-image[_ngcontent-%COMP%]   .main-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover{transform:scale(1)}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:50px;flex:0 0 40%}@media (max-width: 960px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]{flex-direction:row-reverse}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{max-width:200px}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:drop-shadow(0 0 25px rgb(0,0,0))}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{transform:rotate(90deg)}}.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{color:#87a4b6;font-weight:600;font-size:18px}@media (max-width: 500px){.about-me-flex-container[_ngcontent-%COMP%]   .about-me-content[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{font-size:16px}}.card[_ngcontent-%COMP%]{width:fit-content;height:fit-content;background-color:#eee;display:flex;align-items:center;justify-content:center;padding:25px;gap:20px;box-shadow:0 0 20px #0000000e}.socialContainer[_ngcontent-%COMP%]{width:52px;height:52px;background-color:#2c2c2c;display:flex;align-items:center;justify-content:center;overflow:hidden;transition-duration:.3s}.containerOne[_ngcontent-%COMP%]:hover{background-color:#d62976;transition-duration:.3s}.containerThree[_ngcontent-%COMP%]:hover{background-color:#0072b1;transition-duration:.3s}.containerFour[_ngcontent-%COMP%]:hover{background-color:#128c7e;transition-duration:.3s}.socialContainer[_ngcontent-%COMP%]:active{transform:scale(.9);transition-duration:.3s}.socialSvg[_ngcontent-%COMP%]{width:17px}.socialSvg[_ngcontent-%COMP%]   path[_ngcontent-%COMP%]{fill:#fff}.socialContainer[_ngcontent-%COMP%]:hover   .socialSvg[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slide-in-top .3s both}@keyframes _ngcontent-%COMP%_slide-in-top{0%{transform:translateY(-50px);opacity:0}to{transform:translateY(0);opacity:1}}.portfolio-link[_ngcontent-%COMP%]{display:flex;justify-content:center;flex-wrap:wrap;text-align:center}.card[_ngcontent-%COMP%]{width:fit-content;height:fit-content;background-color:#eee;display:flex;align-items:center;justify-content:center;padding:25px;gap:20px;box-shadow:0 0 20px #0000000e;margin:0 auto}.cta[_ngcontent-%COMP%]{position:relative;margin:auto;padding:12px 18px;transition:all .2s ease;border:none;background:none;cursor:pointer}.cta[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:0;display:block;border-radius:50px;background:#b1dae7;width:45px;height:45px;transition:all .3s ease}.cta[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;font-family:Ubuntu,sans-serif;font-size:18px;font-weight:700;letter-spacing:.05em;color:#234567}.cta[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{position:relative;top:0;margin-left:10px;fill:none;stroke-linecap:round;stroke-linejoin:round;stroke:#234567;stroke-width:2;transform:translate(-5px);transition:all .3s ease}.cta[_ngcontent-%COMP%]:hover:before{width:100%;background:#b1dae7}.cta[_ngcontent-%COMP%]:hover   svg[_ngcontent-%COMP%]{transform:translate(0)}.cta[_ngcontent-%COMP%]:active{transform:scale(.95)}.about-me-flex-container[_ngcontent-%COMP%]{display:flex;gap:20px}.about-me-image[_ngcontent-%COMP%]{position:relative}.back-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:300px;height:auto;filter:blur(10px);transition:filter .5s ease-in-out,transform .5s ease-in-out}.front-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:300px;height:auto;position:absolute;top:0;left:0;z-index:10}.about-me-image[_ngcontent-%COMP%]   .show-front-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{filter:blur(0);transform:scale(1.05)}.cta[_ngcontent-%COMP%]{position:relative;display:inline-block;padding:12px 24px;background-color:#f7f7f7;border-radius:30px;cursor:pointer;transition:background-color .3s ease-in-out}.cta[_ngcontent-%COMP%]:hover{background-color:#e2e2e2}.text[_ngcontent-%COMP%]{margin-top:20px;font-size:16px;line-height:1.5;color:#666}']})}return e})();var Qg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=te({type:e,selectors:[["app-first"]],decls:10,vars:0,consts:[[1,"hero-section"],["autoplay","","muted","","loop","","id","bg-video"],["src","video/background.mp4","type","video/mp4"],[1,"profile-pic"],["src","images/roro.jpg","alt","Roaa Ayman"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"video",1),g(2,"source",2),R(3," Your browser does not support the video tag. "),h(),f(4,"div",3),g(5,"img",4),h(),f(6,"h1"),R(7,"Roaa Ayman"),h(),f(8,"p"),R(9,"Recent Computer Science Graduate & Front-End Developer Specializing in Angular"),h()())},styles:[".hero-section[_ngcontent-%COMP%]{position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;color:#fff;overflow:hidden;background-size:cover}.profile-pic[_ngcontent-%COMP%]{border-radius:50%;overflow:hidden;width:150px;height:150px;margin-bottom:20px}.profile-pic[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.hero-section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:10px}.hero-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.5rem;margin-bottom:20px}.social-icons[_ngcontent-%COMP%]{display:flex;gap:15px}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#fff;font-size:1.5rem;transition:color .3s}.social-icons[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#007bff}.background-animation[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1}#bg-video[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;min-width:100%;min-height:100%;width:auto;height:auto;z-index:-1;transform:translate(-50%,-50%);object-fit:cover}"]})}return e})();var N3=[{path:"",component:Qg},{path:"about",component:Yg},{path:"**",redirectTo:""}],Jg=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ce({type:e});static \u0275inj=ae({imports:[uu.forRoot(N3,{scrollPositionRestoration:"enabled",anchorScrolling:"enabled"}),uu]})}return e})();var em=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=te({type:e,selectors:[["app-header"]],decls:25,vars:0,consts:[[1,"h-[70px]","sticky","top-0","z-50","border-b-2","border-black","bg-gradient-to-r","from-[#8e4564]","via-[#81b1d4]","to-[#a06a9d]","px-8","flex","items-center","justify-between"],["type","checkbox","id","check",1,"hidden","peer"],["for","check",1,"menu","block","lg:hidden","cursor-pointer","z-50"],["xmlns","http://www.w3.org/2000/svg","width","30","height","30","fill","currentColor",1,"bi","bi-list"],["fill-rule","evenodd","d","M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"],[1,"logo"],[1,"text-black","font-gupter","font-medium","cursor-pointer","text-xl"],[1,"nav-items","peer-checked:right-0","fixed","lg:static","top-0","right-[-250px]","h-screen","lg:h-auto","w-[250px]","lg:w-auto","flex","flex-col","lg:flex-row","justify-evenly","lg:justify-end","items-start","lg:items-center","bg-[#a06a9d]","lg:bg-transparent","transition-all","duration-500","p-8","lg:p-0","gap-y-6","lg:gap-x-6"],[1,"flex","flex-col","lg:flex-row","gap-y-4","lg:gap-x-4","text-black","text-[18px]","font-medium"],["routerLink","/",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["routerLink","/about",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#skills",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#projects",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"],["href","#contact",1,"hover:text-purple-900","relative","after:block","after:h-[3px]","after:bg-[#481159]","after:w-0","hover:after:w-full","after:transition-all","after:duration-300"]],template:function(r,o){r&1&&(f(0,"nav",0),g(1,"input",1),f(2,"label",2),Bn(),f(3,"svg",3),g(4,"path",4),h()(),Hn(),f(5,"div",5)(6,"h2",6),R(7,"RA"),h()(),f(8,"div",7)(9,"ul",8)(10,"li")(11,"a",9),R(12,"Home"),h()(),f(13,"li")(14,"a",10),R(15,"About"),h()(),f(16,"li")(17,"a",11),R(18,"Skills"),h()(),f(19,"li")(20,"a",12),R(21,"Projects"),h()(),f(22,"li")(23,"a",13),R(24,"Contact"),h()()()()())},dependencies:[Wg],styles:['@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFUkU1Z4Y.woff2) format("woff2");unicode-range:U+0600-06FF,U+0750-077F,U+0870-088E,U+0890-0891,U+0897-08E1,U+08E3-08FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE70-FE74,U+FE76-FEFC,U+102E0-102FB,U+10E60-10E7E,U+10EC2-10EC4,U+10EFC-10EFF,U+1EE00-1EE03,U+1EE05-1EE1F,U+1EE21-1EE22,U+1EE24,U+1EE27,U+1EE29-1EE32,U+1EE34-1EE37,U+1EE39,U+1EE3B,U+1EE42,U+1EE47,U+1EE49,U+1EE4B,U+1EE4D-1EE4F,U+1EE51-1EE52,U+1EE54,U+1EE57,U+1EE59,U+1EE5B,U+1EE5D,U+1EE5F,U+1EE61-1EE62,U+1EE64,U+1EE67-1EE6A,U+1EE6C-1EE72,U+1EE74-1EE77,U+1EE79-1EE7C,U+1EE7E,U+1EE80-1EE89,U+1EE8B-1EE9B,U+1EEA1-1EEA3,U+1EEA5-1EEA9,U+1EEAB-1EEBB,U+1EEF0-1EEF1}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFWkU1Z4Y.woff2) format("woff2");unicode-range:U+0460-052F,U+1C80-1C8A,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFU0U1Z4Y.woff2) format("woff2");unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFVUU1Z4Y.woff2) format("woff2");unicode-range:U+0307-0308,U+0590-05FF,U+200C-2010,U+20AA,U+25CC,U+FB1D-FB4F}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFWUU1Z4Y.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Rubik;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/rubik/v30/iJWZBXyIfDnIV5PNhY1KTN7Z-Yh-B4iFV0U1.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPONa9Ff0.woff2) format("woff2");unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPOJa9Ff0.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Grey Qo;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/greyqo/v10/BXRrvF_Nmv_TyXxNPOxa9A.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cm9JNmxJqPO1QkZpy-.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cl9JNmxJqPO1Qslb-rVc74.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Gupter;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/gupter/v17/2-cl9JNmxJqPO1Qs3bmrVc74.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:Kalnia Glaze;font-style:normal;font-weight:100 700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/kalniaglaze/v4/wlpjgwHCBUNjrGrfu-hwowN1YyC-42Lu26VHf2LtEEIAhqSP.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:Kalnia Glaze;font-style:normal;font-weight:100 700;font-stretch:100%;font-display:swap;src:url(https://fonts.gstatic.com/s/kalniaglaze/v4/wlpjgwHCBUNjrGrfu-hwowN1YyC-42Lu26VHf2LtEEwAhg.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}body[_ngcontent-%COMP%]{--google-font-color-kalniaglaze:none}*[_ngcontent-%COMP%]{margin:0;padding:0;box-sizing:border-box;font-family:Pacifico,cursive}nav[_ngcontent-%COMP%]{height:70px;background-image:linear-gradient(to right,#8e4564,#905f8b,#897aac,#8195c3,#80aed1,#81b1d4,#82b5d6,#83b8d9,#83a6d3,#8b93c8,#967fb6,#a06a9d);padding:0 2rem;display:flex;justify-content:space-between;align-items:center;top:0;z-index:1000;position:sticky;border-bottom:2px solid rgb(0,0,0)}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:none}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-family:Gupter,serif;font-weight:700;font-size:2rem;color:#fff;cursor:pointer;margin:0 .5rem;text-shadow:2px 2px 4px rgba(0,0,0,.3);transition:all .3s ease;position:relative;z-index:1}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:before{content:"";position:absolute;width:100%;height:2px;bottom:-5px;left:0;background:linear-gradient(to right,#8e4564,#81b1d4);transform:scaleX(0);transform-origin:left;transition:transform .3s ease}.logo[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.nav-items[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{display:flex}.overview[_ngcontent-%COMP%]{margin-right:4rem}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:none}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{list-style:none;margin:0 .5rem}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none;color:#000;font-size:18px}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#5b2b6b}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:after{content:"";display:block;height:3px;background:#481159;width:0%;transition:all ease-in-out .3s}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover:after{width:100%}#check[_ngcontent-%COMP%], .menu[_ngcontent-%COMP%]{display:none}@media (max-width: 750px){.nav-items[_ngcontent-%COMP%]{z-index:1000;position:fixed;top:0;height:100vh;width:250px;flex-direction:column;justify-content:space-evenly;background:#a06a9d;padding:2rem;right:-250px;transition:all ease-in-out .5s}.overview[_ngcontent-%COMP%], .account[_ngcontent-%COMP%]{flex-direction:column;width:auto}.overview[_ngcontent-%COMP%]{margin:0}.nav-items[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{display:inline-block;font-weight:400;text-transform:uppercase;font-size:13px;margin-bottom:1rem}nav[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:inline-block;cursor:pointer;vertical-align:top}nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:1rem 0}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:inline-block}nav[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{margin-left:2px;transition:all ease-in-out .3s}.menu[_ngcontent-%COMP%]{display:inline-block;position:fixed;right:2.5rem;z-index:1001}#check[_ngcontent-%COMP%]:checked ~ .nav-items[_ngcontent-%COMP%]{right:0}}']})}return e})();var tm=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/projects";constructor(n){this.http=n}getProjects(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(nn))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function U3(e,t){if(e&1&&(f(0,"span",15),R(1),h()),e&2){let n=t.$implicit;ne(),Nt(n)}}function P3(e,t){if(e&1){let n=Hh();f(0,"div",4)(1,"div",5),g(2,"img",6),h(),f(3,"h1",7),R(4),h(),f(5,"p",8),R(6),h(),f(7,"p",9),R(8),h(),f(9,"div",10)(10,"strong",11),R(11,"Skills:"),h(),Ft(12,U3,2,1,"span",12),h(),f(13,"div",13)(14,"button",14),Je("click",function(){let o=pc(n).$implicit,i=Fr();return gc(i.openGitHub(o.githubLink))}),R(15,"GitHub"),h(),f(16,"button",14),Je("click",function(){let o=pc(n).$implicit,i=Fr();return gc(i.openGitHub(o.link))}),R(17,"Live Demo"),h()()()}if(e&2){let n=t.$implicit;ne(2),Se("src",n.photo,Qt),ne(2),Nt(n.name),ne(2),Nt(n.title),ne(2),Nt(n.description),ne(4),Se("ngForOf",n.skills)}}var nm=(()=>{class e{apiService;projects=[];constructor(n){this.apiService=n}ngOnInit(){this.apiService.getProjects().subscribe(n=>{this.projects=n.map(r=>({photo:r.photo,name:r.name,title:r.title,description:r.description,link:r.link,githubLink:r.githubLink,skills:r.skills}))},n=>{console.error("Error fetching projects:",n)})}openGitHub(n){window.open(n,"_blank")}static \u0275fac=function(r){return new(r||e)(z(tm))};static \u0275cmp=te({type:e,selectors:[["app-projects"]],decls:5,vars:1,consts:[[1,"text-center","mt-6"],[1,"text-4xl","font-righteous","uppercase","text-black","mb-4","relative","inline-block","after:block","after:w-32","after:h-1","after:bg-cyan-500","after:rounded","after:mx-auto","after:mt-2"],[1,"flex","flex-wrap","justify-center","gap-6","p-4"],["class","backdrop-blur-xl bg-[#9b6195]/80 border border-white/20 rounded-xl shadow-xl w-80 p-6 flex flex-col items-center text-center",4,"ngFor","ngForOf"],[1,"backdrop-blur-xl","bg-[#9b6195]/80","border","border-white/20","rounded-xl","shadow-xl","w-80","p-6","flex","flex-col","items-center","text-center"],[1,"w-full","h-72","rounded-xl","overflow-hidden","mb-4"],["alt","Project Image",1,"w-full","h-full","object-cover","object-center",3,"src"],[1,"text-white","text-2xl","font-righteous","uppercase","mb-1"],[1,"text-white","text-sm","font-lato","tracking-wider","uppercase"],[1,"text-white","text-xs","font-lato","mt-1"],[1,"mt-4"],[1,"text-white"],["class","inline-block bg-white text-gray-800 text-xs rounded px-2 py-1 mr-2 mt-2",4,"ngFor","ngForOf"],[1,"mt-5","flex","gap-3"],[1,"px-4","py-2","border","border-purple-900","text-purple-900","rounded-full","text-sm","uppercase","hover:scale-110","hover:border-cyan-600","hover:text-cyan-600","transition",3,"click"],[1,"inline-block","bg-white","text-gray-800","text-xs","rounded","px-2","py-1","mr-2","mt-2"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"h2",1),R(2," Projects "),h()(),f(3,"div",2),Ft(4,P3,18,5,"div",3),h()),r&2&&(ne(4),Se("ngForOf",o.projects))},dependencies:[os]})}return e})();var L3=(e,t)=>({success:e,error:t});function V3(e,t){if(e&1&&(f(0,"div",219),R(1),h()),e&2){let n=Fr();Se("ngClass",Wh(2,L3,n.submissionStatus==="success",n.submissionStatus==="error")),ne(),Wn(" ",n.submissionMessage,`
`)}}var rm=(()=>{class e{submissionStatus=null;submissionMessage="";onSubmit(n){n.preventDefault();let r=n.target;fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json"}}).then(o=>{o.ok?(this.submissionStatus="success",this.submissionMessage="Message sent successfully!",r.reset()):(this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later.")}).catch(()=>{this.submissionStatus="error",this.submissionMessage="There was an error sending your message. Please try again later."})}static \u0275fac=function(r){return new(r||e)};static \u0275cmp=te({type:e,selectors:[["app-contact"]],decls:223,vars:1,consts:[[1,"contact-header"],[1,"heading"],[1,"container","d-flex","justify-content-center","align-items-center"],["xmlns","http://www.w3.org/2000/svg","viewBox","0 0 790 563","fill","none"],["id","Image"],["id","g14"],["id","g16"],["id","g22"],["id","path24","d","M578.06 12.9772C592.384 8.33142 607.668 7.43103 622.682 8.31278C644.252 9.57946 666.668 15.0178 682.527 29.8837C692.521 39.2526 699.149 51.6277 707.182 62.7655C730.486 95.0785 766.513 118.198 782.236 154.912C795.674 186.289 790.623 225.749 767.498 250.666C744.37 275.583 703.649 282.658 675.018 264.535C647.531 247.136 635.383 212.503 610.273 191.742C592.326 176.901 569.144 170.28 549.646 157.607C529.69 144.636 513.457 124.248 509.79 100.515C506.745 80.8173 513.744 59.4156 528.903 46.4558C543.731 33.7796 559.331 19.0536 578.06 12.9772Z","fill","#D0F6FF"],["id","g26"],["id","path28","d","M702.629 254.14C677.841 258.169 653.602 251.674 628.841 247.05C605.059 242.608 581.372 234.267 562.49 218.522C553.842 211.31 546.177 202.344 542.784 191.529C537.944 176.097 542.362 159.436 542.319 143.243C542.267 124.241 537.593 105.929 524.57 91.9138C516.642 83.3826 507.429 75.9038 501.21 66.026C488.249 45.4368 498.285 17.8695 518.578 6.24557C537.067 -4.34208 560.588 -0.151769 579.793 9.03335C598.996 18.2198 615.855 31.9082 635.139 40.9228C656.28 50.8045 679.407 54.6779 702.724 56.9022C720.556 58.6044 738.716 56.5333 753.266 67.1156C763.675 74.6877 771.032 86.0519 775.307 98.2911C783.396 121.448 781.768 148.673 778.037 172.583C775.54 188.601 770.517 204.461 761.348 217.755C750.094 234.074 732.89 245.819 714.058 251.504C710.234 252.66 706.426 253.523 702.629 254.14Z","fill","#ADE0EC"],["id","g30"],["id","path32","d","M663.601 562.578H87.0689C43.5385 528.913 13.2922 480.886 5.1219 426.023C1.72497 403.207 3.65744 376.191 22.008 362.528C50.2285 341.516 92.5784 368.009 124.46 353.325C144.998 343.869 155.119 319.297 155.332 296.439C155.544 273.583 147.922 251.523 142.217 229.409C136.51 207.295 132.749 183.417 140.459 161.935C148.169 140.454 170.87 123.025 192.716 128.727C211.437 133.614 223.318 152.833 241.257 160.133C259.931 167.732 281.608 160.819 298.184 149.256C314.758 137.694 327.949 121.87 343.441 108.858C370.638 86.0156 406.562 72.0169 441.495 77.35C476.426 82.6831 508.747 110.108 514.202 145.471C518.662 174.4 506.652 207.826 524.152 231.129C543.883 257.401 585.152 250.025 613.676 265.983C636.899 278.972 649.286 309.077 642.052 334.934C634.666 361.336 609.565 383.494 613.653 410.622C616.583 430.071 633.6 443.505 645.587 458.982C668.627 488.727 679.049 528.158 663.601 562.578Z","fill","#D0F6FF"],["id","g34"],["id","path36","d","M636.536 562.578H142.588C127.567 548.706 110.711 535.931 102.179 517.242C93.6475 498.553 93.6698 474.269 107.702 459.372C124.638 441.394 152.947 443.847 176.763 437.899C204.228 431.038 229.205 408.689 232.723 380.251C237.265 343.537 206.911 309.992 208.804 273.041C210.296 243.911 234.698 217.737 263.314 214.567C282.66 212.424 302.727 219.607 321.415 214.109C338.741 209.012 351.237 194.119 366.296 184.052C383.968 172.235 406.528 167.099 426.891 172.974C447.257 178.85 464.492 196.695 467.235 217.968C470.152 240.588 458.004 266.283 470.888 284.991C480.485 298.927 499.63 301.618 516.392 301.075C533.155 300.531 551.03 298.252 565.763 306.372C579.463 313.921 587.611 329.548 589.138 345.273C590.664 360.996 586.334 376.788 579.943 391.199C574.357 403.794 567.162 415.706 562.961 428.843C558.759 441.979 557.893 457.066 564.737 469.006C571.941 481.577 585.915 488.105 597.307 496.94C617.442 512.552 635.027 536.936 636.536 562.578Z","fill","#ADE0EC"],["id","g38"],["id","path40","d","M595.195 76.2172L623.725 149.709L684.511 114.948L595.195 76.2172Z","fill","#FAFAFA"],["id","g42"],["id","path44","d","M595.195 76.2172L651.26 133.962L666.528 125.232L595.195 76.2172Z","fill","#DADADA"],["id","g46"],["id","path48","d","M666.528 125.231L655.896 151.885L651.262 133.962L666.528 125.231Z","fill","#DADADA"],["id","g50"],["id","path52","d","M655.896 151.885L642.776 138.814L651.262 133.962L655.896 151.885Z","fill","#B2B2B2"],["id","g54"],["id","path56","d","M222.015 539.778C157.683 522.604 101.579 476.087 72.2367 415.592C60.1279 390.628 52.3612 362.908 54.182 335.155C56.0014 307.4 68.2732 279.663 90.2639 263.011C112.253 246.359 144.303 242.756 167.56 257.538C190.03 271.821 200.733 299.209 220.204 317.461C243.475 339.274 280.404 345.641 308.459 330.683C336.514 315.723 352.288 279.369 342.05 248.968C332.575 220.834 305.793 203.339 282.527 185.228C259.261 167.115 236.126 141.651 239.454 112.116C242.315 86.7319 264.382 67.653 287.628 57.7513C332.132 38.7951 389.516 47.2223 419.844 85.2787C452.476 126.224 446.202 185.954 431.486 236.425C416.769 286.896 395.069 337.985 402.391 390.086C408.475 433.375 434.97 472.304 470.109 497.688C505.247 523.075 548.365 535.649 591.441 538.326C634.426 540.999 680.569 532.908 712.364 503.476C744.158 474.044 754.899 419.157 726.78 386.108C712.226 369.003 690.497 360.328 669.604 352.466C648.708 344.604 626.907 336.377 611.765 319.807C596.621 303.236 590.753 275.553 604.995 258.181C621.492 238.058 665.44 235.858 680.982 214.969C692.069 200.069 679.116 171.364 666.529 157.269","stroke","#00C0E0","stroke-width","2.541","stroke-miterlimit","10","stroke-dasharray","7.62 7.62"],["id","g58"],["id","path60","d","M186.221 462.671C158.423 444.172 133.639 421.035 113.173 394.475C104.595 383.341 96.7115 371.5 91.5083 358.398C86.3038 345.294 83.8862 330.794 86.4431 316.909C88.2757 306.953 93.6209 296.589 103.112 293.404C110.525 290.917 118.902 293.505 125.077 298.35C131.253 303.195 135.584 310.023 139.418 316.916C154.207 343.52 163.287 372.9 174.224 401.352C179.474 415.006 185.205 428.511 192.17 441.366C195.631 447.754 199.387 453.984 203.532 459.939C207.289 465.334 214.117 471.144 216.477 476.969C211.073 481.321 191.263 466.026 186.221 462.671Z","fill","#009D9C"],["id","g62"],["id","path64","d","M107.952 308.508C121.544 366.877 153.477 420.713 197.968 460.267","stroke","#00BBBF","stroke-width","2.02","stroke-miterlimit","10"],["id","g66"],["id","path68","d","M556.282 462.962C580.155 451.221 602.114 435.493 621.004 416.609C628.922 408.693 636.362 400.145 641.81 390.319C647.257 380.493 650.64 369.27 650.028 358.018C649.587 349.946 646.41 341.19 639.223 337.682C633.608 334.942 626.717 336.117 621.339 339.307C615.961 342.497 611.841 347.447 608.109 352.504C593.705 372.014 583.539 394.316 571.997 415.691C566.459 425.947 560.553 436.037 553.736 445.484C550.349 450.177 546.746 454.716 542.861 458.995C539.341 462.875 533.349 466.761 530.891 471.124C534.727 475.129 551.952 465.092 556.282 462.962Z","fill","#009D9C"],["id","g70"],["id","path72","d","M633.861 349.129C617.182 393.899 586.452 433.173 547.233 459.836","stroke","#00BBBF","stroke-width","1.612","stroke-miterlimit","10"],["id","g74"],["id","path76","d","M198.233 424.458C213.177 349.774 197.247 269.251 155.048 206.17","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g78"],["id","path80","d","M159.471 213.554C147.424 209.56 136.887 201.07 130.331 190.079C123.775 179.087 121.256 165.687 123.366 153.024C136.148 156.495 148.154 164.541 154.962 176.037C161.771 187.536 162.465 200.493 159.471 213.554Z","fill","#11ABBA"],["id","g82"],["id","path84","d","M172.923 237.731C170.163 228.217 170.886 217.71 174.922 208.676C178.958 199.643 186.273 192.157 195.149 187.981C198.557 197.74 198.756 208.999 194.512 218.417C190.269 227.834 182.434 233.949 172.923 237.731Z","fill","#11ABBA"],["id","g86"],["id","path88","d","M173.775 236.831C166.404 230.308 156.684 226.574 146.897 226.504C137.11 226.434 127.338 230.03 119.876 236.447C127.196 243.672 137.206 248.568 147.423 248.608C157.641 248.647 166.403 243.999 173.775 236.831Z","fill","#11ABBA"],["id","g90"],["id","path92","d","M188.104 276.094C187.024 266.239 189.542 256.02 195.07 247.837C200.597 239.655 209.088 233.576 218.546 231.029C220.225 241.241 218.483 252.363 212.686 260.887C206.887 269.41 198.122 274.049 188.104 276.094Z","fill","#11ABBA"],["id","g94"],["id","path96","d","M189.099 275.358C182.962 267.634 174.033 262.24 164.408 260.443C154.782 258.647 144.542 260.463 136.091 265.464C142.057 273.87 151.07 280.459 161.124 282.301C171.179 284.145 180.606 281.115 189.099 275.358Z","fill","#11ABBA"],["id","g98"],["id","path100","d","M198.154 314.469C197.924 304.556 201.31 294.598 207.521 286.933C213.729 279.267 222.71 273.961 232.351 272.257C233.146 282.578 230.456 293.504 223.948 301.485C217.439 309.467 208.308 313.315 198.154 314.469Z","fill","#11ABBA"],["id","g102"],["id","path104","d","M199.208 313.823C193.758 305.586 185.324 299.426 175.891 296.789C166.457 294.15 156.099 295.057 147.252 299.294C152.471 308.194 160.885 315.553 170.744 318.274C180.602 320.997 190.253 318.808 199.208 313.823Z","fill","#11ABBA"],["id","g106"],["id","path108","d","M203.971 356.696C205.264 346.866 210.136 337.563 217.445 330.968C224.754 324.372 234.439 320.543 244.225 320.378C243.428 330.699 239.095 341.071 231.443 347.929C223.789 354.789 214.179 357.154 203.971 356.696Z","fill","#11ABBA"],["id","g110"],["id","path112","d","M205.112 356.224C200.99 347.23 193.605 339.817 184.689 335.725C175.775 331.635 165.404 330.9 156.012 333.694C159.806 343.307 166.988 351.901 176.31 356.142C185.632 360.381 195.5 359.74 205.112 356.224Z","fill","#11ABBA"],["id","g114"],["id","path116","d","M546.285 450.207C530.11 375.786 544.71 295.004 585.861 231.219","stroke","#11ABBA","stroke-width","2.541","stroke-miterlimit","10"],["id","g118"],["id","path120","d","M581.562 238.676C593.54 234.478 603.937 225.811 610.312 214.71C616.685 203.608 618.983 190.168 616.663 177.542C603.94 181.23 592.069 189.476 585.452 201.088C578.835 212.7 578.354 225.668 581.562 238.676Z","fill","#11ABBA"],["id","g122"],["id","path124","d","M568.512 263.078C571.114 253.518 570.219 243.024 566.033 234.06C561.85 225.096 554.412 217.737 545.469 213.71C542.22 223.525 542.208 234.787 546.607 244.131C551.006 253.476 558.939 259.457 568.512 263.078Z","fill","#11ABBA"],["id","g126"],["id","path128","d","M567.646 262.192C574.907 255.545 584.566 251.647 594.349 251.411C604.134 251.175 613.963 254.605 621.528 260.895C614.331 268.242 604.403 273.308 594.187 273.52C583.972 273.732 575.135 269.234 567.646 262.192Z","fill","#11ABBA"],["id","g130"],["id","path132","d","M553.965 301.692C554.883 291.82 552.196 281.645 546.534 273.556C540.872 265.469 532.283 259.535 522.783 257.148C521.274 267.388 523.198 278.478 529.136 286.902C535.074 295.328 543.915 299.817 553.965 301.692Z","fill","#11ABBA"],["id","g134"],["id","path136","d","M552.959 300.973C558.968 293.147 567.807 287.6 577.401 285.642C586.995 283.683 597.263 285.324 605.795 290.182C599.97 298.687 591.066 305.428 581.044 307.441C571.021 309.454 561.546 306.585 552.959 300.973Z","fill","#11ABBA"],["id","g138"],["id","path140","d","M544.55 340.232C544.617 330.317 541.066 320.416 534.731 312.857C528.396 305.299 519.329 300.144 509.661 298.606C509.036 308.939 511.905 319.818 518.546 327.687C525.186 335.556 534.379 339.25 544.55 340.232Z","fill","#11ABBA"],["id","g142"],["id","path144","d","M543.486 339.603C548.799 331.276 557.13 324.975 566.519 322.176C575.908 319.378 586.279 320.109 595.196 324.198C590.124 333.185 581.833 340.685 572.021 343.571C562.207 346.46 552.522 344.437 543.486 339.603Z","fill","#11ABBA"],["id","g146"],["id","path148","d","M539.431 382.551C537.978 372.745 532.951 363.525 525.535 357.055C518.117 350.586 508.371 346.92 498.585 346.921C499.551 357.227 504.053 367.523 511.819 374.253C519.583 380.981 529.232 383.182 539.431 382.551Z","fill","#11ABBA"],["id","g150"],["id","path152","d","M538.282 382.098C542.255 373.036 549.518 365.498 558.363 361.259C567.21 357.016 577.568 356.105 587.003 358.74C583.369 368.417 576.328 377.13 567.079 381.527C557.828 385.925 547.95 385.452 538.282 382.098Z","fill","#11ABBA"],["id","g154"],["id","path156","d","M186.615 500.321C190.696 492.791 196.119 485.823 199.682 478.076C190.178 465.849 178.777 454.862 166.819 445.23C159.004 438.931 150.847 433.032 142.419 427.531C134.688 433.762 126.957 439.994 119.225 446.225C120.579 435.351 121.356 425.888 122.482 415.574C105.313 406.143 87.2411 398.331 68.6211 392.377C64.3289 399.386 60.6691 406.825 54.8967 412.829C54.9847 404.798 54.2249 396.412 53.1469 387.893C35.9349 383.405 18.3639 380.482 0.707452 379.308C0.649609 386.531 1.06635 393.746 1.88798 400.912C6.50223 399.507 10.074 395.563 14.9604 394.821C11.7383 402.728 8.82513 411.421 4.99044 419.449C9.19717 438.521 16.3959 456.93 26.2186 473.763C34.3468 468.915 41.9636 462.248 51.7627 458.125C50.0576 473.301 50.0274 489.179 48.7351 504.527C53.8963 510.215 59.4097 515.573 65.2741 520.527C75.5977 529.245 86.9217 536.691 98.9201 542.791C101.353 533.385 103.872 524.016 109.898 516.114C116.996 529.781 124.688 541.96 131.128 555.467C157.986 563.194 186.571 564.779 214.002 559.454C218.542 558.574 222.349 551.211 223.76 546.749C225.172 542.289 224.898 537.468 224.262 532.827C222.26 518.237 216.907 504.646 209.377 492.145C201.36 494.069 193.248 496.332 186.615 500.321Z","fill","#11ABBA"],["id","g158"],["id","path160","d","M194.298 545.299C131.158 507.676 73.43 460.749 23.4922 406.451","stroke","#55CDE2","stroke-width","2.541","stroke-miterlimit","10"],["id","g162"],["id","path164","d","M559.699 515.384C555.868 510.221 551.098 505.622 547.63 500.242C553.415 490.113 560.744 480.704 568.626 472.241C573.781 466.709 579.23 461.436 584.922 456.429C591.334 460.232 597.744 464.032 604.155 467.835C602.002 459.887 600.425 452.929 598.502 445.374C610.285 436.498 622.913 428.73 636.143 422.286C640.078 427.037 643.584 432.176 648.514 436.023C647.601 430.055 647.283 423.73 647.188 417.273C659.526 412.073 672.295 407.997 685.312 405.212C686.116 410.582 686.566 415.998 686.708 421.42C683.127 420.873 680.053 418.324 676.338 418.3C679.571 423.837 682.654 429.991 686.353 435.55C685.232 450.201 681.815 464.681 676.277 478.269C669.715 475.541 663.346 471.403 655.618 469.394C658.486 480.504 660.182 492.319 662.759 503.602C659.518 508.393 655.978 512.977 652.135 517.298C645.372 524.903 637.727 531.67 629.441 537.508C626.639 530.772 623.778 524.07 618.459 518.842C614.616 529.781 610.176 539.676 606.805 550.427C587.63 559.082 566.522 563.353 545.546 562.358C542.075 562.193 538.466 557.123 536.945 553.957C535.425 550.79 535.121 547.171 535.105 543.651C535.058 532.573 537.61 521.88 541.897 511.761C548.065 512.326 554.342 513.133 559.699 515.384Z","fill","#11ABBA"],["id","g166"],["id","path168","d","M558.719 549.691C601.746 514.86 639.767 473.689 671.212 427.878","stroke","#55CDE2","stroke-width","1.91","stroke-miterlimit","10"],["id","g170"],["id","path172","d","M554.113 562.578H187.856C180.008 562.578 173.645 556.132 173.645 548.18V310.114C173.645 302.163 180.008 295.717 187.856 295.717H554.113C561.963 295.717 568.324 302.163 568.324 310.114V548.18C568.324 556.132 561.963 562.578 554.113 562.578Z","fill","#060C37"],["id","g174"],["id","path176","d","M563.719 429.147C563.719 435.866 558.342 441.314 551.71 441.314C545.078 441.314 539.701 435.866 539.701 429.147C539.701 422.427 545.078 416.981 551.71 416.981C558.342 416.981 563.719 422.427 563.719 429.147Z","fill","#111E65"],["id","g178"],["id","path180","d","M182.05 474.266C179.95 474.266 178.247 472.542 178.247 470.413V387.882C178.247 385.753 179.95 384.028 182.05 384.028C184.151 384.028 185.854 385.753 185.854 387.882V470.413C185.854 472.542 184.151 474.266 182.05 474.266Z","fill","#111E65"],["id","path182","d","M535.104 552.722H191.254V305.564H535.104V552.722Z","fill","#D8E9F5"],["id","path184","d","M535.1 322.18H191.256V305.568H535.1V322.18Z","fill","#A4B1BA"],["id","path186","d","M201.252 320.17H196.898V314.56H201.252V320.17Z","fill","#FF6044"],["id","path188","d","M206.906 320.17H202.552V310.653H206.906V320.17Z","fill","#FF6044"],["id","path190","d","M212.886 320.17H208.532V307.952H212.886V320.17Z","fill","#FF6044"],["id","g192"],["id","path194","d","M507.781 308.957V309.767C507.781 310.411 507.264 310.933 506.629 310.933H505.346C504.711 310.933 504.196 311.455 504.196 312.099V315.647C504.196 316.293 504.711 316.814 505.346 316.814H506.629C507.264 316.814 507.781 317.336 507.781 317.979V318.792C507.781 319.435 508.296 319.957 508.931 319.957H526.844C527.479 319.957 527.995 319.435 527.995 318.792V308.957C527.995 308.313 527.479 307.791 526.844 307.791H508.931C508.296 307.791 507.781 308.313 507.781 308.957Z","fill","#D8E9F5"],["id","g196"],["id","path198","d","M526.894 319.341H523.692C523.458 319.341 523.267 319.148 523.267 318.909V308.824C523.267 308.584 523.458 308.391 523.692 308.391H526.894C527.13 308.391 527.32 308.584 527.32 308.824V318.909C527.32 319.148 527.13 319.341 526.894 319.341Z","fill","#92FC28"],["id","g200"],["id","path202","d","M521.94 319.341H518.739C518.505 319.341 518.313 319.148 518.313 318.909V308.824C518.313 308.584 518.505 308.391 518.739 308.391H521.94C522.175 308.391 522.366 308.584 522.366 308.824V318.909C522.366 319.148 522.175 319.341 521.94 319.341Z","fill","#92FC28"],["id","g204"],["id","path206","d","M516.987 319.341H513.785C513.551 319.341 513.36 319.148 513.36 318.909V308.824C513.36 308.584 513.551 308.391 513.785 308.391H516.987C517.223 308.391 517.413 308.584 517.413 308.824V318.909C517.413 319.148 517.223 319.341 516.987 319.341Z","fill","#92FC28"],["id","g208"],["id","path210","d","M498.8 313.874C498.8 316.456 496.733 318.551 494.183 318.551C491.635 318.551 489.569 316.456 489.569 313.874C489.569 311.292 491.635 309.197 494.183 309.197C496.733 309.197 498.8 311.292 498.8 313.874Z","fill","#D8E9F5"],["id","path212","d","M513.36 533.681H212.999V340.836H513.36V533.681Z","fill","#C0CFDA"],["id","path214","d","M513.36 357.464H212.999V340.838H513.36V357.464Z","fill","#A4B3BC"],["id","path216","d","M507.28 373.991H310.642V366.083H507.28V373.991Z","fill","#DCEEFB"],["id","path218","d","M419.169 389.046H310.642V381.138H419.169V389.046Z","fill","#DCEEFB"],["id","path220","d","M369.032 404.104H310.642V396.196H369.032V404.104Z","fill","#DCEEFB"],["id","path222","d","M507.28 430.213H310.642V422.305H507.28V430.213Z","fill","#DCEEFB"],["id","path224","d","M419.169 445.268H310.642V437.36H419.169V445.268Z","fill","#DCEEFB"],["id","path226","d","M369.032 460.325H310.642V452.418H369.032V460.325Z","fill","#DCEEFB"],["id","path228","d","M507.28 485.114H310.642V477.206H507.28V485.114Z","fill","#DCEEFB"],["id","path230","d","M419.169 500.172H310.642V492.264H419.169V500.172Z","fill","#DCEEFB"],["id","path232","d","M369.032 515.228H310.642V507.32H369.032V515.228Z","fill","#DCEEFB"],["id","path234","d","M301.035 409.578H224.781V366.082H301.035V409.578Z","fill","#DCEEFB"],["id","g236"],["id","path238","d","M224.781 409.579L262.908 387.831L301.034 409.579H224.781Z","fill","#CADBE7"],["id","g240"],["id","path242","d","M301.034 366.082L262.908 387.83L224.781 366.082H301.034Z","fill","#CADBE7"],["id","path244","d","M301.035 465.546H224.781V422.05H301.035V465.546Z","fill","#DCEEFB"],["id","g246"],["id","path248","d","M224.781 465.546L262.908 443.798L301.034 465.546H224.781Z","fill","#CADBE7"],["id","g250"],["id","path252","d","M301.034 422.05L262.908 443.798L224.781 422.05H301.034Z","fill","#CADBE7"],["id","path254","d","M301.035 521.515H224.781V478.019H301.035V521.515Z","fill","#DCEEFB"],["id","g256"],["id","path258","d","M224.781 521.514L262.908 499.766L301.034 521.514H224.781Z","fill","#CADBE7"],["id","g260"],["id","path262","d","M301.034 478.018L262.908 499.766L224.781 478.018H301.034Z","fill","#CADBE7"],["id","g264"],["id","g282"],["id","g280","opacity","0.440002"],["id","g274","opacity","0.440002"],["id","path272","opacity","0.440002","d","M314.124 305.565L191.254 430.069V321.271L206.769 305.565H314.124Z","fill","white"],["id","g278","opacity","0.440002"],["id","path276","opacity","0.440002","d","M388.697 305.565L191.254 505.613V449.961L333.77 305.565H388.697Z","fill","white"],["id","g284"],["id","g302"],["id","g300","opacity","0.440002"],["id","g294","opacity","0.440002"],["id","path292","opacity","0.440002","d","M535.104 332.465V441.249L425.071 552.723H317.715L535.104 332.465Z","fill","white"],["id","g298","opacity","0.440002"],["id","path296","opacity","0.440002","d","M535.104 461.142V516.794L499.632 552.723H444.716L535.104 461.142Z","fill","white"],["id","envelope"],["id","g304"],["id","path306","d","M249.266 298.798L351.208 218.764C357.652 213.705 366.657 213.705 373.102 218.764L475.045 298.798V432.924H249.266V298.798Z","fill","#FF9004"],["id","path308","d","M448.926 227.706H275.382V421.076H448.926V227.706Z","fill","#FAFAFA"],["id","path310","d","M438.481 239.346H285.831V245.241H438.481V239.346Z","fill","#DCDCDC"],["id","path312","d","M415.561 251.195H285.831V257.09H415.561V251.195Z","fill","#DCDCDC"],["id","path314","d","M394.51 263.044H285.831V268.939H394.51V263.044Z","fill","#DCDCDC"],["id","path316","d","M394.51 285.792H285.831V291.688H394.51V285.792Z","fill","#DCDCDC"],["id","path318","d","M366.443 297.167H285.831V303.062H366.443V297.167Z","fill","#DCDCDC"],["id","path320","d","M442.769 321H362.156V326.896H442.769V321Z","fill","#DCDCDC"],["id","path322","d","M442.768 332.609H377.201V338.504H442.768V332.609Z","fill","#DCDCDC"],["id","g324"],["id","path326","d","M362.155 365.9L249.265 298.877V432.924L362.155 365.9Z","fill","#FFAE35"],["id","g328"],["id","path330","d","M362.156 365.9L475.045 298.877V432.924L362.156 365.9Z","fill","#FFAE35"],["id","g332"],["id","path334","d","M351.209 352.89L249.267 432.924H475.044L373.102 352.89C366.658 347.831 357.652 347.831 351.209 352.89Z","fill","#FFBF69"],["id","g348"],["id","path350","d","M185.705 159.357C185.994 158.402 185.854 157.315 185.28 156.095C184.719 154.898 183.98 154.112 183.067 153.736C182.152 153.361 181.213 153.405 180.251 153.868C179.287 154.333 178.667 155.04 178.388 155.99C178.109 156.941 178.251 158.015 178.813 159.212C179.375 160.409 180.11 161.203 181.02 161.595C181.927 161.986 182.863 161.951 183.826 161.487C184.789 161.022 185.415 160.312 185.705 159.357ZM184.018 139.899C186.987 140.019 189.648 140.858 192.003 142.415C194.358 143.972 196.169 146.103 197.439 148.81C198.376 150.805 198.868 152.668 198.915 154.398C198.964 156.13 198.62 157.627 197.886 158.892C197.151 160.158 196.083 161.127 194.682 161.803C193.522 162.361 192.412 162.597 191.351 162.51C190.29 162.423 189.34 161.997 188.499 161.234C188.332 163.679 187.01 165.499 184.538 166.691C183.247 167.313 181.88 167.543 180.435 167.382C178.991 167.222 177.639 166.671 176.378 165.728C175.116 164.786 174.101 163.494 173.331 161.853C172.56 160.212 172.207 158.602 172.27 157.021C172.334 155.441 172.761 154.037 173.555 152.812C174.35 151.588 175.402 150.658 176.716 150.026C178.642 149.097 180.458 148.996 182.169 149.723L181.404 148.093L186.755 145.514L191.517 155.66C191.851 156.368 192.222 156.816 192.63 157C193.038 157.183 193.46 157.17 193.898 156.96C195.278 156.295 195.16 154.244 193.547 150.807C192.558 148.7 191.191 147.062 189.448 145.89C187.703 144.718 185.729 144.098 183.524 144.033C181.32 143.967 179.056 144.493 176.737 145.61C174.438 146.718 172.631 148.201 171.317 150.058C170.001 151.916 169.265 153.963 169.108 156.2C168.949 158.438 169.386 160.655 170.416 162.85C171.468 165.09 172.892 166.864 174.687 168.175C176.483 169.485 178.493 170.207 180.719 170.346C182.943 170.483 185.205 169.998 187.503 168.891C189.845 167.763 191.793 166.226 193.349 164.28L196.235 167.254C195.479 168.216 194.473 169.176 193.219 170.134C191.964 171.092 190.636 171.908 189.237 172.583C186.063 174.113 182.955 174.794 179.912 174.629C176.868 174.464 174.138 173.537 171.722 171.846C169.304 170.157 167.414 167.859 166.05 164.954C164.698 162.072 164.144 159.149 164.393 156.189C164.639 153.228 165.671 150.494 167.487 147.988C169.301 145.481 171.807 143.458 175.003 141.918C178.045 140.453 181.05 139.779 184.018 139.899Z","fill","#ADE0EC"],["id","g352"],["id","path354","d","M478.281 145.979L473.499 145.088L471.809 150.637L476.591 151.528L478.281 145.979ZM483.567 146.965L481.877 152.514L486.737 153.418L485.812 158.499L480.333 157.478L478.528 163.209L473.241 162.224L475.046 156.492L470.263 155.601L468.46 161.331L463.174 160.347L464.977 154.616L460.079 153.702L461.001 148.622L466.522 149.65L468.214 144.102L463.314 143.19L464.237 138.109L469.759 139.138L471.562 133.407L476.848 134.393L475.043 140.124L479.826 141.015L481.629 135.284L486.917 136.269L485.112 142.001L490.01 142.913L489.088 147.994L483.567 146.965Z","fill","#ADE0EC"],["id","g356"],["id","path358","d","M230.094 489.727H164.645C144.782 489.727 128.679 473.412 128.679 453.286C128.679 433.159 144.782 416.844 164.645 416.844H194.128C213.99 416.844 230.094 433.159 230.094 453.286V489.727Z","fill","#FFBF69"],["id","g360"],["id","path362","d","M190.288 474.567C192.225 471.057 193.491 467.457 194.24 463.884C197.265 463.216 199.718 462.418 201.535 461.712C199.468 467.269 195.439 471.849 190.288 474.567ZM173.549 476.516C170.414 472.301 168.399 468.049 167.204 463.913C172.228 464.889 176.849 465.295 180.987 465.295C184.501 465.295 187.666 465.013 190.478 464.585C189.44 468.665 187.643 472.75 184.795 476.628C183.054 477.042 181.249 477.283 179.386 477.283C177.368 477.283 175.42 476.999 173.549 476.516ZM157.077 461.27C159.25 461.983 161.355 462.573 163.406 463.075C164.255 466.725 165.672 470.467 167.822 474.207C162.852 471.377 159.006 466.783 157.077 461.27ZM166.919 432.92C165.905 435.193 164.777 438.165 163.89 441.631C161.455 442.199 159.416 442.847 157.807 443.446C159.751 439.087 162.942 435.428 166.919 432.92ZM185.694 430.179C186.289 431.348 188.269 435.45 189.79 441.13C180.926 439.619 173.434 439.938 167.6 440.897C169.168 435.61 171.267 431.824 172.077 430.47C174.382 429.71 176.835 429.288 179.386 429.288C181.572 429.288 183.682 429.614 185.694 430.179ZM201.203 443.946C198.569 443.098 196.02 442.407 193.568 441.864C192.612 437.856 191.394 434.47 190.4 432.058C195.218 434.635 199.063 438.835 201.203 443.946ZM194.354 445.71C196.968 446.339 199.688 447.138 202.507 448.133C202.868 449.796 203.071 451.515 203.071 453.285C203.071 454.669 202.929 456.014 202.707 457.334C201.441 457.942 198.765 459.081 194.862 460.045C195.44 454.989 195.108 450.091 194.354 445.71ZM166.64 444.734C172.634 443.581 180.793 443.047 190.668 444.909C191.612 449.668 192.068 455.159 191.237 460.804C184.963 461.903 176.497 462.275 166.311 460.097C165.321 454.509 165.701 449.252 166.64 444.734ZM155.701 453.285C155.701 451.44 155.927 449.649 156.319 447.921C157.561 447.343 159.839 446.402 163.05 445.549C162.325 449.694 162.056 454.341 162.712 459.24C160.557 458.67 158.328 457.976 156.039 457.161C155.835 455.896 155.701 454.608 155.701 453.285ZM179.386 425.733C164.391 425.733 152.192 438.093 152.192 453.285C152.192 468.479 164.391 480.838 179.386 480.838C194.381 480.838 206.58 468.479 206.58 453.285C206.58 438.093 194.381 425.733 179.386 425.733Z","fill","#FAFAFA"],["id","g364"],["id","path366","d","M487.575 534.716H553.024C572.888 534.716 588.99 518.4 588.99 498.275C588.99 478.149 572.888 461.834 553.024 461.834H523.541C503.679 461.834 487.575 478.149 487.575 498.275V534.716Z","fill","#FFBF69"],["id","g368"],["id","path370","d","M565.214 487.805C565.214 477.497 549.034 468.633 538.283 477.531C527.532 468.633 511.352 477.497 511.352 487.805C511.352 487.805 507.872 508.014 538.283 522.676C568.694 508.014 565.214 487.805 565.214 487.805Z","stroke","#FAFAFA","stroke-width","3.811","stroke-miterlimit","10","stroke-linejoin","round"],["id","g372"],["id","path374","d","M466.093 53.4869C465.677 53.3258 465.259 53.1899 464.843 53.074C464.729 52.6558 464.594 52.2389 464.437 51.8207C463.767 50.1411 462.888 48.4615 461.12 46.7819C459.352 48.4615 458.474 50.1411 457.804 51.8207C457.645 52.2415 457.51 52.6638 457.395 53.0847C456.978 53.2019 456.563 53.3391 456.147 53.4989C454.489 54.1782 452.832 55.0679 451.174 56.8594C452.832 58.6509 454.489 59.5406 456.147 60.2199C456.56 60.3797 456.973 60.5156 457.384 60.6315C457.499 61.0537 457.633 61.4759 457.792 61.8982C458.46 63.5777 459.342 65.2573 461.12 66.9369C462.899 65.2573 463.781 63.5777 464.449 61.8982C464.605 61.4799 464.741 61.0617 464.855 60.6421C465.267 60.5276 465.681 60.3917 466.093 60.2319C467.751 59.5553 469.409 58.6615 471.067 56.8594C469.409 55.0573 467.751 54.1635 466.093 53.4869Z","fill","#ADE0EC"],["id","star1"],["id","path378","d","M18.666 335.315C18.2493 335.154 17.8325 335.016 17.4145 334.901C17.3001 334.484 17.166 334.067 17.0096 333.649C16.3392 331.968 15.461 330.289 13.6929 328.61C11.9247 330.289 11.0466 331.968 10.3761 333.649C10.2171 334.069 10.0816 334.492 9.96728 334.913C9.55186 335.028 9.13514 335.167 8.71972 335.327C7.06201 336.006 5.4043 336.896 3.74658 338.687C5.4043 340.479 7.06201 341.369 8.71972 342.048C9.13251 342.206 9.54398 342.342 9.95676 342.458C10.0698 342.882 10.2052 343.304 10.3643 343.725C11.0321 345.406 11.9142 347.085 13.6929 348.765C15.4715 347.085 16.3536 345.406 17.0214 343.725C17.1779 343.308 17.3133 342.89 17.4263 342.47C17.8391 342.354 18.2532 342.22 18.666 342.058C20.3237 341.383 21.9814 340.489 23.6391 338.687C21.9814 336.885 20.3237 335.991 18.666 335.315Z","fill","#ADE0EC"],["id","g380"],["id","path382","d","M500.378 253.717C499.962 253.558 499.545 253.42 499.128 253.305C499.014 252.886 498.878 252.469 498.722 252.052C498.052 250.372 497.173 248.692 495.405 247.012C493.637 248.692 492.759 250.372 492.089 252.052C491.931 252.472 491.795 252.894 491.681 253.317C491.264 253.432 490.849 253.57 490.433 253.729C488.774 254.409 487.117 255.298 485.459 257.09C487.117 258.881 488.774 259.772 490.433 260.45C490.845 260.61 491.258 260.746 491.669 260.862C491.784 261.284 491.918 261.706 492.078 262.129C492.745 263.808 493.627 265.488 495.405 267.167C497.184 265.488 498.066 263.808 498.734 262.129C498.892 261.71 499.026 261.292 499.14 260.874C499.553 260.758 499.966 260.622 500.378 260.462C502.037 259.786 503.694 258.892 505.352 257.09C503.694 255.289 502.037 254.395 500.378 253.717Z","fill","#ADE0EC"],["id","g384"],["id","path386","d","M673.413 79.5778C673.204 79.4978 672.995 79.4286 672.785 79.3713C672.729 79.1622 672.662 78.9517 672.583 78.7426C672.246 77.9008 671.806 77.059 670.921 76.2172C670.035 77.059 669.595 77.9008 669.258 78.7426C669.178 78.9544 669.112 79.1648 669.054 79.3766C668.844 79.4352 668.636 79.5032 668.429 79.5844C667.596 79.9241 666.766 80.3703 665.936 81.2693C666.766 82.1657 667.596 82.6119 668.429 82.9529C668.635 83.0328 668.84 83.1008 669.048 83.158C669.106 83.3698 669.173 83.5816 669.253 83.7947C669.587 84.6352 670.03 85.4769 670.921 86.3201C671.811 85.4769 672.254 84.6352 672.589 83.7947C672.668 83.5842 672.734 83.3738 672.792 83.1647C672.999 83.1061 673.206 83.0382 673.413 82.9596C674.244 82.6199 675.075 82.1711 675.906 81.2693C675.075 80.3649 674.244 79.9174 673.413 79.5778Z","fill","#D0F6FF"],["id","g388"],["id","path390","d","M724.621 229.528C724.413 229.448 724.204 229.379 723.994 229.321C723.936 229.112 723.87 228.902 723.791 228.694C723.455 227.851 723.014 227.009 722.128 226.167C721.244 227.009 720.803 227.851 720.467 228.694C720.387 228.904 720.32 229.115 720.262 229.327C720.053 229.385 719.845 229.453 719.636 229.534C718.805 229.874 717.974 230.32 717.145 231.219C717.974 232.116 718.805 232.562 719.636 232.903C719.842 232.983 720.049 233.051 720.256 233.108C720.314 233.32 720.38 233.532 720.46 233.745C720.795 234.585 721.238 235.427 722.128 236.27C723.02 235.427 723.461 234.585 723.797 233.745C723.877 233.534 723.943 233.324 723.999 233.115C724.208 233.056 724.415 232.988 724.621 232.91C725.453 232.57 726.284 232.121 727.113 231.219C726.284 230.315 725.453 229.867 724.621 229.528Z","fill","#D0F6FF"],["id","g392"],["id","path394","d","M722.669 226.015C722.46 225.935 722.251 225.866 722.042 225.809C721.984 225.6 721.918 225.389 721.838 225.18C721.503 224.338 721.063 223.497 720.177 222.655C719.291 223.497 718.85 224.338 718.515 225.18C718.435 225.392 718.368 225.602 718.31 225.814C718.101 225.873 717.892 225.941 717.684 226.022C716.853 226.362 716.022 226.808 715.192 227.707C716.022 228.603 716.853 229.049 717.684 229.39C717.891 229.47 718.097 229.538 718.305 229.595C718.361 229.807 718.428 230.019 718.508 230.232C718.844 231.073 719.285 231.914 720.177 232.758C721.068 231.914 721.51 231.073 721.845 230.232C721.924 230.022 721.991 229.811 722.047 229.602C722.255 229.544 722.463 229.476 722.669 229.397C723.5 229.057 724.331 228.609 725.162 227.707C724.331 226.802 723.5 226.355 722.669 226.015Z","fill","#D0F6FF"],["id","g396"],["id","path398","d","M122.37 271.837C122.161 271.756 121.952 271.688 121.742 271.63C121.686 271.421 121.619 271.211 121.54 271.002C121.203 270.16 120.763 269.318 119.877 268.476C118.991 269.318 118.551 270.16 118.215 271.002C118.135 271.213 118.068 271.424 118.01 271.636C117.801 271.694 117.594 271.762 117.385 271.842C116.554 272.183 115.723 272.629 114.892 273.527C115.723 274.425 116.554 274.871 117.385 275.212C117.591 275.291 117.797 275.36 118.005 275.417C118.062 275.629 118.129 275.841 118.209 276.052C118.544 276.894 118.986 277.736 119.877 278.578C120.768 277.736 121.211 276.894 121.545 276.052C121.624 275.843 121.691 275.633 121.748 275.422C121.955 275.365 122.163 275.297 122.37 275.217C123.2 274.878 124.031 274.43 124.862 273.527C124.031 272.624 123.2 272.176 122.37 271.837Z","fill","#ADE0EC"],["id","g400"],["id","path402","d","M30.9696 538.087C30.7606 538.007 30.5516 537.939 30.3426 537.881C30.2847 537.671 30.219 537.461 30.1401 537.252C29.8036 536.41 29.3632 535.568 28.4772 534.728C27.5911 535.568 27.1507 536.41 26.8155 537.252C26.7353 537.464 26.6683 537.674 26.6104 537.887C26.4014 537.945 26.1937 538.012 25.9847 538.094C25.1538 538.435 24.323 538.881 23.4922 539.779C24.323 540.675 25.1538 541.121 25.9847 541.462C26.1911 541.542 26.3975 541.611 26.6052 541.667C26.6617 541.88 26.7301 542.092 26.8089 542.303C27.1442 543.146 27.5859 543.988 28.4772 544.829C29.3685 543.988 29.8115 543.146 30.1454 542.303C30.2243 542.094 30.2913 541.884 30.3478 541.674C30.5555 541.615 30.7633 541.549 30.9696 541.468C31.8005 541.128 32.6313 540.68 33.4621 539.779C32.6313 538.876 31.8005 538.427 30.9696 538.087Z","fill","#ADE0EC"],["id","g404"],["id","path406","d","M384.68 138.195C384.471 138.114 384.262 138.046 384.053 137.989C383.995 137.78 383.928 137.569 383.849 137.36C383.514 136.518 383.073 135.676 382.187 134.835C381.301 135.676 380.861 136.518 380.524 137.36C380.445 137.572 380.377 137.782 380.32 137.994C380.111 138.053 379.904 138.121 379.695 138.202C378.864 138.541 378.033 138.988 377.202 139.885C378.033 140.783 378.864 141.229 379.695 141.57C379.901 141.65 380.107 141.718 380.314 141.775C380.372 141.987 380.439 142.199 380.519 142.411C380.854 143.253 381.296 144.094 382.187 144.936C383.078 144.094 383.52 143.253 383.855 142.411C383.934 142.202 384.001 141.991 384.058 141.781C384.266 141.723 384.472 141.656 384.68 141.576C385.51 141.236 386.341 140.788 387.172 139.885C386.341 138.982 385.51 138.535 384.68 138.195Z","fill","#ADE0EC"],["id","g408"],["id","path410","d","M143.253 52.4684C143.044 52.3885 142.835 52.3192 142.626 52.262C142.568 52.0528 142.501 51.8424 142.423 51.6333C142.087 50.7915 141.646 49.9497 140.76 49.1079C139.874 49.9497 139.434 50.7915 139.097 51.6333C139.019 51.8451 138.951 52.0555 138.894 52.2673C138.685 52.3259 138.477 52.3938 138.268 52.4751C137.437 52.8147 136.606 53.2609 135.775 54.1586C136.606 55.0564 137.437 55.5026 138.268 55.8436C138.474 55.9235 138.681 55.9914 138.888 56.0487C138.945 56.2605 139.012 56.4722 139.092 56.6854C139.427 57.5258 139.869 58.3676 140.76 59.2107C141.652 58.3676 142.093 57.5258 142.429 56.6854C142.507 56.4749 142.575 56.2645 142.631 56.0553C142.839 55.9967 143.045 55.9288 143.253 55.8502C144.084 55.5106 144.915 55.0617 145.745 54.1586C144.915 53.2556 144.084 52.8081 143.253 52.4684Z","fill","#ADE0EC"],["id","star4"],["id","path414","d","M659.175 279.551C658.966 279.47 658.757 279.402 658.546 279.344C658.49 279.135 658.423 278.925 658.344 278.716C658.009 277.874 657.567 277.032 656.682 276.19C655.796 277.032 655.356 277.874 655.019 278.716C654.939 278.926 654.873 279.138 654.816 279.35C654.605 279.408 654.397 279.476 654.19 279.556C653.359 279.897 652.527 280.343 651.697 281.241C652.527 282.139 653.359 282.585 654.19 282.926C654.396 283.005 654.603 283.074 654.81 283.131C654.867 283.343 654.934 283.555 655.014 283.766C655.349 284.608 655.791 285.45 656.682 286.292C657.574 285.45 658.015 284.608 658.35 283.766C658.429 283.557 658.495 283.347 658.553 283.136C658.761 283.079 658.968 283.011 659.175 282.931C660.006 282.592 660.836 282.144 661.667 281.241C660.836 280.338 660.006 279.89 659.175 279.551Z","fill","#ADE0EC"],["id","star5"],["id","path418","d","M412.477 191.341C412.268 191.26 412.059 191.192 411.85 191.133C411.793 190.924 411.727 190.715 411.647 190.506C411.311 189.664 410.871 188.822 409.985 187.98C409.099 188.822 408.659 189.664 408.323 190.506C408.243 190.718 408.176 190.928 408.118 191.14C407.909 191.197 407.7 191.266 407.492 191.346C406.662 191.687 405.831 192.133 405 193.031C405.831 193.929 406.662 194.375 407.492 194.715C407.699 194.795 407.905 194.864 408.113 194.921C408.17 195.133 408.237 195.345 408.317 195.556C408.652 196.398 409.094 197.24 409.985 198.082C410.876 197.24 411.318 196.398 411.653 195.556C411.732 195.346 411.799 195.137 411.856 194.926C412.063 194.869 412.271 194.801 412.477 194.721C413.308 194.382 414.139 193.934 414.97 193.031C414.139 192.128 413.308 191.681 412.477 191.341Z","fill","#D0F6FF"],["id","star2"],["id","path422","d","M318.495 91.4014C318.129 91.2602 317.762 91.1403 317.396 91.0391C317.295 90.6715 317.178 90.3039 317.04 89.9363C316.45 88.4605 315.678 86.9847 314.124 85.5075C312.57 86.9847 311.797 88.4605 311.208 89.9363C311.069 90.3079 310.95 90.6782 310.85 91.0484C310.483 91.151 310.117 91.2709 309.752 91.4121C308.295 92.0088 306.837 92.792 305.381 94.3663C306.837 95.9407 308.295 96.7239 309.752 97.3206C310.115 97.4604 310.476 97.5803 310.839 97.6815C310.939 98.0531 311.059 98.4234 311.198 98.795C311.786 100.272 312.56 101.749 314.124 103.225C315.687 101.749 316.463 100.272 317.049 98.795C317.187 98.4274 317.306 98.0598 317.406 97.6922C317.77 97.5896 318.132 97.4711 318.495 97.3312C319.953 96.7358 321.41 95.95 322.868 94.3663C321.41 92.7826 319.953 91.9968 318.495 91.4014Z","fill","#ADE0EC"],["id","g424"],["id","path426","d","M95.3161 198.94C94.9494 198.801 94.5826 198.679 94.2171 198.578C94.1159 198.21 93.9989 197.843 93.8609 197.475C93.2706 195.999 92.4989 194.524 90.9451 193.047C89.3912 194.524 88.6182 195.999 88.0293 197.475C87.8899 197.847 87.7703 198.217 87.6704 198.587C87.3036 198.69 86.9382 198.81 86.5727 198.951C85.1161 199.548 83.6582 200.331 82.2017 201.905C83.6582 203.48 85.1161 204.263 86.5727 204.86C86.9355 204.999 87.2971 205.119 87.6599 205.221C87.7598 205.592 87.8794 205.962 88.0188 206.334C88.6064 207.811 89.3807 209.288 90.9451 210.764C92.5081 209.288 93.2838 207.811 93.8701 206.334C94.0081 205.966 94.1264 205.599 94.2263 205.231C94.5892 205.129 94.9533 205.01 95.3161 204.87C96.774 204.275 98.2306 203.49 99.6885 201.905C98.2306 200.322 96.774 199.536 95.3161 198.94Z","fill","#ADE0EC"],["id","star3"],["id","path430","d","M567.016 163.164C566.649 163.023 566.282 162.903 565.915 162.8C565.815 162.434 565.697 162.066 565.559 161.699C564.97 160.223 564.197 158.746 562.643 157.27C561.089 158.746 560.316 160.223 559.728 161.699C559.59 162.069 559.47 162.441 559.369 162.81C559.003 162.912 558.638 163.033 558.272 163.175C556.814 163.771 555.358 164.553 553.9 166.129C555.358 167.703 556.814 168.486 558.272 169.082C558.634 169.222 558.997 169.343 559.359 169.444C559.459 169.816 559.579 170.186 559.717 170.558C560.306 172.035 561.08 173.51 562.643 174.986C564.206 173.51 564.982 172.035 565.57 170.558C565.708 170.19 565.826 169.822 565.926 169.453C566.289 169.352 566.653 169.234 567.016 169.094C568.472 168.498 569.93 167.713 571.387 166.129C569.93 164.545 568.472 163.759 567.016 163.164Z","fill","#D0F6FF"],["id","star6"],["id","path434","d","M785.486 113.408C785.119 113.267 784.752 113.147 784.385 113.045C784.285 112.678 784.167 112.31 784.03 111.943C783.44 110.467 782.667 108.99 781.113 107.514C779.559 108.99 778.786 110.467 778.198 111.943C778.059 112.314 777.94 112.685 777.839 113.055C777.473 113.157 777.108 113.277 776.742 113.418C775.284 114.015 773.828 114.798 772.37 116.373C773.828 117.947 775.284 118.73 776.742 119.327C777.104 119.467 777.467 119.587 777.829 119.688C777.929 120.06 778.049 120.43 778.187 120.801C778.776 122.279 779.55 123.756 781.113 125.231C782.676 123.756 783.452 122.279 784.04 120.801C784.178 120.434 784.296 120.066 784.396 119.697C784.759 119.596 785.123 119.477 785.486 119.338C786.942 118.742 788.4 117.956 789.857 116.373C788.4 114.789 786.942 114.003 785.486 113.408Z","fill","#D0F6FF"],["id","g436"],["id","path438","d","M556.27 45.0362C555.903 44.895 555.536 44.7752 555.169 44.6739C555.069 44.3063 554.951 43.9387 554.813 43.5711C554.224 42.0953 553.451 40.6182 551.897 39.1424C550.343 40.6182 549.57 42.0953 548.983 43.5711C548.843 43.9427 548.724 44.313 548.624 44.6833C548.257 44.7858 547.892 44.9057 547.526 45.0469C546.068 45.6436 544.612 46.4268 543.154 48.0011C544.612 49.5755 546.068 50.3587 547.526 50.9554C547.888 51.0953 548.251 51.2151 548.613 51.3164C548.713 51.688 548.833 52.0583 548.971 52.4299C549.56 53.907 550.334 55.3841 551.897 56.8599C553.46 55.3841 554.236 53.907 554.824 52.4299C554.962 52.0622 555.08 51.6946 555.18 51.327C555.543 51.2245 555.907 51.1059 556.27 50.9661C557.726 50.3707 559.184 49.5848 560.641 48.0011C559.184 46.4175 557.726 45.6316 556.27 45.0362Z","fill","#D0F6FF"],["id","contact-form","action","https://formspree.io/f/mldrlygg","method","POST",3,"submit"],[1,"title","text-center","mb-4"],[1,"form-group","position-relative"],["for","name",1,"d-block"],["data-feather","user",1,"icon"],["type","text","id","name","name","name","placeholder","Name","required","",1,"form-control","form-control-lg","thick"],["for","email",1,"d-block"],["data-feather","mail",1,"icon"],["type","email","id","email","name","email","placeholder","E-mail","required","",1,"form-control","form-control-lg","thick"],[1,"form-group","message"],["id","message","name","message","rows","7","placeholder","Message","required","",1,"form-control","form-control-lg"],[1,"text-center"],["type","submit",1,"btn","btn-primary"],[3,"ngClass",4,"ngIf"],[3,"ngClass"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"h2",1),R(2,"Contact me "),h()(),f(3,"div",2),Bn(),f(4,"svg",3)(5,"g",4)(6,"g",5)(7,"g",6)(8,"g",7),g(9,"path",8),h(),f(10,"g",9),g(11,"path",10),h(),f(12,"g",11),g(13,"path",12),h(),f(14,"g",13),g(15,"path",14),h(),f(16,"g",15),g(17,"path",16),h(),f(18,"g",17),g(19,"path",18),h(),f(20,"g",19),g(21,"path",20),h(),f(22,"g",21),g(23,"path",22),h(),f(24,"g",23),g(25,"path",24),h(),f(26,"g",25),g(27,"path",26),h(),f(28,"g",27),g(29,"path",28),h(),f(30,"g",29),g(31,"path",30),h(),f(32,"g",31),g(33,"path",32),h(),f(34,"g",33),g(35,"path",34),h(),f(36,"g",35),g(37,"path",36),h(),f(38,"g",37),g(39,"path",38),h(),f(40,"g",39),g(41,"path",40),h(),f(42,"g",41),g(43,"path",42),h(),f(44,"g",43),g(45,"path",44),h(),f(46,"g",45),g(47,"path",46),h(),f(48,"g",47),g(49,"path",48),h(),f(50,"g",49),g(51,"path",50),h(),f(52,"g",51),g(53,"path",52),h(),f(54,"g",53),g(55,"path",54),h(),f(56,"g",55),g(57,"path",56),h(),f(58,"g",57),g(59,"path",58),h(),f(60,"g",59),g(61,"path",60),h(),f(62,"g",61),g(63,"path",62),h(),f(64,"g",63),g(65,"path",64),h(),f(66,"g",65),g(67,"path",66),h(),f(68,"g",67),g(69,"path",68),h(),f(70,"g",69),g(71,"path",70),h(),f(72,"g",71),g(73,"path",72),h(),f(74,"g",73),g(75,"path",74),h(),f(76,"g",75),g(77,"path",76),h(),f(78,"g",77),g(79,"path",78),h(),f(80,"g",79),g(81,"path",80),h(),f(82,"g",81),g(83,"path",82),h(),f(84,"g",83),g(85,"path",84),h(),f(86,"g",85),g(87,"path",86),h(),g(88,"path",87)(89,"path",88)(90,"path",89)(91,"path",90)(92,"path",91),f(93,"g",92),g(94,"path",93),h(),f(95,"g",94),g(96,"path",95),h(),f(97,"g",96),g(98,"path",97),h(),f(99,"g",98),g(100,"path",99),h(),f(101,"g",100),g(102,"path",101),h(),g(103,"path",102)(104,"path",103)(105,"path",104)(106,"path",105)(107,"path",106)(108,"path",107)(109,"path",108)(110,"path",109)(111,"path",110)(112,"path",111)(113,"path",112)(114,"path",113),f(115,"g",114),g(116,"path",115),h(),f(117,"g",116),g(118,"path",117),h(),g(119,"path",118),f(120,"g",119),g(121,"path",120),h(),f(122,"g",121),g(123,"path",122),h(),g(124,"path",123),f(125,"g",124),g(126,"path",125),h(),f(127,"g",126),g(128,"path",127),h(),f(129,"g",128)(130,"g",129)(131,"g",130)(132,"g",131),g(133,"path",132),h(),f(134,"g",133),g(135,"path",134),h()()()(),f(136,"g",135)(137,"g",136)(138,"g",137)(139,"g",138),g(140,"path",139),h(),f(141,"g",140),g(142,"path",141),h()()()(),f(143,"g",142)(144,"g",143),g(145,"path",144),h(),g(146,"path",145)(147,"path",146)(148,"path",147)(149,"path",148)(150,"path",149)(151,"path",150)(152,"path",151)(153,"path",152),f(154,"g",153),g(155,"path",154),h(),f(156,"g",155),g(157,"path",156),h(),f(158,"g",157),g(159,"path",158),h()(),f(160,"g",159),g(161,"path",160),h(),f(162,"g",161),g(163,"path",162),h(),f(164,"g",163),g(165,"path",164),h(),f(166,"g",165),g(167,"path",166),h(),f(168,"g",167),g(169,"path",168),h(),f(170,"g",169),g(171,"path",170),h(),f(172,"g",171),g(173,"path",172),h(),f(174,"g",173),g(175,"path",174),h(),f(176,"g",175),g(177,"path",176),h(),f(178,"g",177),g(179,"path",178),h(),f(180,"g",179),g(181,"path",180),h(),f(182,"g",181),g(183,"path",182),h(),f(184,"g",183),g(185,"path",184),h(),f(186,"g",185),g(187,"path",186),h(),f(188,"g",187),g(189,"path",188),h(),f(190,"g",189),g(191,"path",190),h(),f(192,"g",191),g(193,"path",192),h(),f(194,"g",193),g(195,"path",194),h(),f(196,"g",195),g(197,"path",196),h(),f(198,"g",197),g(199,"path",198),h(),f(200,"g",199),g(201,"path",200),h(),f(202,"g",201),g(203,"path",202),h(),f(204,"g",203),g(205,"path",204),h()()()()(),Hn(),f(206,"form",205),Je("submit",function(s){return o.onSubmit(s)}),f(207,"h1",206),R(208,"Talk to me"),h(),f(209,"div",207)(210,"label",208),g(211,"i",209),h(),g(212,"input",210),h(),f(213,"div",207)(214,"label",211),g(215,"i",212),h(),g(216,"input",213),h(),f(217,"div",214),g(218,"textarea",215),h(),f(219,"div",216)(220,"button",217),R(221,"Send message"),h()()(),Ft(222,V3,2,5,"div",218),h()),r&2&&(ne(222),Se("ngIf",o.submissionStatus))},dependencies:[gp,mp,ng,Jp,_l],styles:['*[_ngcontent-%COMP%]{margin-bottom:1rem}.contact-header[_ngcontent-%COMP%]{text-align:center;color:#000;padding:1rem;position:relative;font-family:Pacifico,cursive;font-size:2rem}.contact-header[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:150px;height:4px;background-color:#11abba;border-radius:2px}.container[_ngcontent-%COMP%]{display:flex;align-items:center;gap:9rem}svg[_ngcontent-%COMP%]{width:30%;height:auto;animation:_ngcontent-%COMP%_float 2s ease-in-out infinite;margin-left:10%}form[_ngcontent-%COMP%]{width:35%;background-color:#fff;padding:2rem;border-radius:3rem;box-shadow:0 10px 20px #0000001a;margin-right:10%}.title[_ngcontent-%COMP%]{font-family:Pacifico,cursive;color:#212529;font-size:2.5rem;text-align:center;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]{position:relative;margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{width:100%;padding:1rem 1rem 1rem 3rem;font-size:1.1rem;color:#212529;background-color:#f2f6f8;border:none;border-radius:2rem;box-shadow:0 7px 5px #6f1a8e41}.form-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;height:7rem}.form-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{position:absolute;left:1rem;top:50%;transform:translateY(-50%);color:#57565c}[_ngcontent-%COMP%]::placeholder{color:#838788;font-weight:600}.btn.btn-primary[_ngcontent-%COMP%]{display:inline-block;width:100%;padding:.8rem;font-size:1.1rem;font-weight:700;border:none;border-radius:3rem;background-image:linear-gradient(131deg,#905f8b,#82b5d6,#83b8d9,#a06a9d);background-size:300% 100%;transition:all .3s ease-in-out;color:#fff;cursor:pointer}.btn.btn-primary[_ngcontent-%COMP%]:hover{box-shadow:0 7px 5px #6f1a8e41;background-size:100% 100%;transform:translateY(-.15em)}@keyframes _ngcontent-%COMP%_float{0%{transform:translateY(0)}50%{transform:translateY(-20px)}to{transform:translateY(0)}}@keyframes _ngcontent-%COMP%_blink{0%{opacity:0}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.container[_ngcontent-%COMP%]{flex-direction:column;align-items:center;gap:2rem}svg[_ngcontent-%COMP%]{width:60%;margin-left:0}form[_ngcontent-%COMP%]{width:90%;margin-right:0}.title[_ngcontent-%COMP%]{font-size:2rem}.btn.btn-primary[_ngcontent-%COMP%]{font-size:1rem}}@media (max-width: 480px){.title[_ngcontent-%COMP%]{font-size:1.8rem}.contact-header[_ngcontent-%COMP%]{font-size:1.5rem}form[_ngcontent-%COMP%]{padding:1.5rem}.btn.btn-primary[_ngcontent-%COMP%]{padding:.6rem}}']})}return e})();var om=(()=>{class e{http;apiUrl="https://portflio-backend-uiv7.onrender.com/api/skills";constructor(n){this.http=n}getSkills(){return this.http.get(this.apiUrl)}static \u0275fac=function(r){return new(r||e)(E(nn))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function H3(e,t){if(e&1&&(f(0,"div",5)(1,"div",6),g(2,"div",7)(3,"div",8),f(4,"div",9),g(5,"img",10)(6,"div",11),h(),f(7,"h3",12),R(8),h()()()),e&2){let n=t.$implicit;ne(5),Se("src",n.icon,Qt)("alt",n.name),ne(3),Nt(n.name)}}var im=(()=>{class e{skillsService;skills=[];constructor(n){this.skillsService=n}ngOnInit(){this.skillsService.getSkills().subscribe(n=>{this.skills=n.map(r=>({icon:r.photo,name:r.name}))},n=>{console.error("Error fetching skills:",n)})}static \u0275fac=function(r){return new(r||e)(z(om))};static \u0275cmp=te({type:e,selectors:[["app-skills"]],decls:6,vars:1,consts:[[1,"pt-10"],[1,"text-center","relative","pb-4"],[1,"text-4xl","font-righteous","uppercase","text-black","mb-2","relative","inline-block","after:block","after:w-24","after:h-1","after:bg-cyan-500","after:rounded","after:mx-auto","after:mt-2"],[1,"grid","grid-cols-2","sm:grid-cols-3","md:grid-cols-4","lg:grid-cols-5","gap-6","px-4","sm:px-10","text-lg","text-center"],["class","p-4 text-black cursor-pointer hover:[&_.skills-img]:-translate-x-5 transition-all duration-300",4,"ngFor","ngForOf"],[1,"p-4","text-black","cursor-pointer","hover:[&_.skills-img]:-translate-x-5","transition-all","duration-300"],[1,"flex","flex-col","items-center","pb-2","relative"],[1,"absolute","bottom-0","right-1/2","translate-x-1/2","w-24","h-1","rounded-r","bg-cyan-500"],[1,"absolute","bottom-0","left-1/2","-translate-x-1/2","w-8","h-1","rounded-l","bg-cyan-500","transition-all","duration-500","group-hover:w-8"],[1,"skills-img","w-[90px]","h-[90px]","relative","rounded-full","bg-gray-100","flex","justify-center","items-center","transition-transform","duration-500","overflow-hidden"],[1,"w-[50px]","z-10",3,"src","alt"],[1,"absolute","top-0","left-0","w-1/2","h-full","bg-gray-500/50","rounded-l-full"],[1,"text-gray-800","mt-2","text-base"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"div",1)(2,"h2",2),R(3," Skills "),h()(),f(4,"div",3),Ft(5,H3,9,3,"div",4),h()()),r&2&&(ne(5),Se("ngForOf",o.skills))},dependencies:[os]})}return e})();var sm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=te({type:e,selectors:[["app-footer"]],decls:17,vars:0,consts:[[1,"bg-gray-900","text-white","py-1"],[1,"flex","justify-center","gap-4"],[1,"text-sm","hover:text-blue-400","transition-colors"],["href","mailto:<EMAIL>"],["href","https://www.instagram.com/roaaayman_10/"],["href","https://github.com/roaaayman21"],["href","https://wa.me/+2001151310078"],[1,"text-xl"]],template:function(r,o){r&1&&(f(0,"div",0)(1,"ul",1)(2,"li",2)(3,"a",3),R(4,"Email"),h()(),f(5,"li",2)(6,"a",4),R(7,"Instagram"),h()(),f(8,"li",2)(9,"a",5),R(10,"Github"),h()(),f(11,"li",2)(12,"a",6),R(13,"WhatsApp"),h()(),f(14,"li",7)(15,"p"),R(16,"\u{1F44B}"),h()()()())},styles:["div[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;height:100%;width:100%;background-image:linear-gradient(to right,#8e4564,#905f8b,#897aac,#8195c3,#80aed1,#81b1d4,#82b5d6,#83b8d9,#83a6d3,#8b93c8,#967fb6,#a06a9d);background-size:cover;font-family:Menlo,monospace;flex-direction:column}ul[_ngcontent-%COMP%]{display:inline-grid;grid-auto-flow:row;grid-gap:12px;justify-items:center;margin:auto;padding:10px;list-style:none;text-align:center}@media (min-width: 500px){ul[_ngcontent-%COMP%]{grid-auto-flow:column}}li[_ngcontent-%COMP%]{padding:5px}a[_ngcontent-%COMP%]{color:#000;text-decoration:none;font-size:1.2rem;box-shadow:inset 0 -1px #fff6}a[_ngcontent-%COMP%]:hover{box-shadow:inset 0 -1.2em #fff6}li[_ngcontent-%COMP%]:last-child{grid-column:1 / 2;grid-row:1 / 2}li[_ngcontent-%COMP%]:hover ~ li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_wave-animation .3s infinite}@keyframes _ngcontent-%COMP%_wave-animation{0%,to{transform:rotate(0)}25%{transform:rotate(20deg)}75%{transform:rotate(-15deg)}}@media (max-width: 500px){div[_ngcontent-%COMP%]{background-size:200% 100%;background-position:center}}"]})}return e})();var am=(()=>{class e{title="portflio";static \u0275fac=function(r){return new(r||e)};static \u0275cmp=te({type:e,selectors:[["app-root"]],decls:6,vars:0,consts:[["id","projects"],["id","skills"],["id","contact"]],template:function(r,o){r&1&&g(0,"app-header")(1,"router-outlet")(2,"app-projects",0)(3,"app-skills",1)(4,"app-contact",2)(5,"app-footer")},dependencies:[ru,em,nm,rm,im,sm]})}return e})();var cm=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ce({type:e,bootstrap:[am]});static \u0275inj=ae({imports:[Bp,Jg,Np,og,ig]})}return e})();jp().bootstrapModule(cm,{ngZoneEventCoalescing:!0}).catch(e=>console.error(e));
